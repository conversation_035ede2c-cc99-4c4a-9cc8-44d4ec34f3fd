<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SettingController extends Controller
{
    public function index()
    {
        $settings = Setting::orderBy('group')->orderBy('key')->get()->groupBy('group');
        
        return view('admin.settings.index', compact('settings'));
    }

    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        foreach ($request->settings as $key => $value) {
            $setting = Setting::where('key', $key)->first();
            
            if ($setting) {
                // Handle different types
                switch ($setting->type) {
                    case 'boolean':
                        $value = $value === '1' || $value === 'true' || $value === true;
                        break;
                    case 'integer':
                        $value = (int) $value;
                        break;
                    case 'float':
                        $value = (float) $value;
                        break;
                    case 'array':
                    case 'json':
                        if (is_string($value)) {
                            $value = json_decode($value, true) ?: [];
                        }
                        break;
                    default:
                        $value = (string) $value;
                        break;
                }

                $setting->update(['value' => $value]);
            } else {
                // Create new setting if it doesn't exist
                Setting::create([
                    'key' => $key,
                    'value' => $value,
                    'type' => 'string',
                    'group' => 'general',
                ]);
            }
        }

        return back()->with('success', 'Settings updated successfully.');
    }

    // API endpoint for settings
    public function apiIndex()
    {
        $settings = Setting::all()->groupBy('group');
        
        $formattedSettings = [];
        foreach ($settings as $group => $groupSettings) {
            $formattedSettings[$group] = [];
            foreach ($groupSettings as $setting) {
                $formattedSettings[$group][$setting->key] = $setting->value;
            }
        }

        return response()->json([
            'success' => true,
            'data' => $formattedSettings
        ]);
    }

    public function apiUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string',
            'value' => 'required',
            'type' => 'nullable|string|in:string,integer,float,boolean,array,json',
            'group' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $setting = Setting::set(
            $request->key,
            $request->value,
            $request->type ?: 'string',
            $request->group ?: 'general'
        );

        return response()->json([
            'success' => true,
            'message' => 'Setting updated successfully',
            'data' => [
                'key' => $setting->key,
                'value' => $setting->value,
                'type' => $setting->type,
                'group' => $setting->group,
            ]
        ]);
    }
}
