"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '../context/UserContext';

interface UseAuthOptions {
  redirectTo?: string;
  redirectIfFound?: boolean;
}

export function useAuth({ 
  redirectTo = '/login', 
  redirectIfFound = false 
}: UseAuthOptions = {}) {
  const { user, isAuthenticated, isLoading } = useUser();
  const router = useRouter();

  useEffect(() => {
    // Don't do anything while loading
    if (isLoading) return;

    // If redirectIfFound is true and user is authenticated, redirect
    if (redirectIfFound && isAuthenticated) {
      router.push('/');
      return;
    }

    // If user is not authenticated and we need authentication, redirect
    if (!redirectIfFound && !isAuthenticated) {
      router.push(redirectTo);
      return;
    }
  }, [isAuthenticated, isLoading, redirectTo, redirectIfFound, router]);

  return {
    user,
    isAuthenticated,
    isLoading,
  };
}

// Hook for protecting pages that require authentication
export function useRequireAuth() {
  return useAuth({ redirectTo: '/login' });
}

// Hook for auth pages (login, signup) that should redirect if user is already authenticated
export function useRedirectIfAuthenticated() {
  return useAuth({ redirectIfFound: true });
}
