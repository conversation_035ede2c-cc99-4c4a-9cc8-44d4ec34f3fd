<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    public function index(Request $request)
    {
        $query = News::published()->with('conference');

        // Filter by conference
        if ($request->has('conference_id')) {
            $query->where('conference_id', $request->conference_id);
        }

        // Filter featured news
        if ($request->has('featured') && $request->featured) {
            $query->where('is_featured', true);
        }

        // Pagination
        $perPage = $request->get('per_page', 10);
        $news = $query->orderBy('published_at', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $news->items(),
            'pagination' => [
                'current_page' => $news->currentPage(),
                'last_page' => $news->lastPage(),
                'per_page' => $news->perPage(),
                'total' => $news->total(),
                'from' => $news->firstItem(),
                'to' => $news->lastItem(),
            ]
        ]);
    }

    public function show($slug)
    {
        $news = News::published()
            ->where('slug', $slug)
            ->with('conference')
            ->firstOrFail();

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $news->id,
                'title' => $news->title,
                'slug' => $news->slug,
                'excerpt' => $news->excerpt,
                'content' => $news->content,
                'featured_image_url' => $news->featured_image_url,
                'author_name' => $news->author_name,
                'published_at' => $news->published_at,
                'reading_time' => $news->reading_time,
                'is_featured' => $news->is_featured,
                'meta_title' => $news->meta_title,
                'meta_description' => $news->meta_description,
                'meta_keywords' => $news->meta_keywords,
                'conference' => [
                    'id' => $news->conference->id,
                    'title' => $news->conference->title,
                ],
            ]
        ]);
    }

    public function featured(Request $request)
    {
        $query = News::published()->featured()->with('conference');

        // Filter by conference
        if ($request->has('conference_id')) {
            $query->where('conference_id', $request->conference_id);
        }

        $limit = $request->get('limit', 5);
        $news = $query->orderBy('published_at', 'desc')->limit($limit)->get();

        return response()->json([
            'success' => true,
            'data' => $news->map(function($article) {
                return [
                    'id' => $article->id,
                    'title' => $article->title,
                    'slug' => $article->slug,
                    'excerpt' => $article->excerpt,
                    'featured_image_url' => $article->featured_image_url,
                    'author_name' => $article->author_name,
                    'published_at' => $article->published_at,
                    'reading_time' => $article->reading_time,
                ];
            })
        ]);
    }

    public function recent(Request $request)
    {
        $query = News::published()->with('conference');

        // Filter by conference
        if ($request->has('conference_id')) {
            $query->where('conference_id', $request->conference_id);
        }

        $limit = $request->get('limit', 5);
        $news = $query->orderBy('published_at', 'desc')->limit($limit)->get();

        return response()->json([
            'success' => true,
            'data' => $news->map(function($article) {
                return [
                    'id' => $article->id,
                    'title' => $article->title,
                    'slug' => $article->slug,
                    'excerpt' => $article->excerpt,
                    'featured_image_url' => $article->featured_image_url,
                    'author_name' => $article->author_name,
                    'published_at' => $article->published_at,
                    'reading_time' => $article->reading_time,
                ];
            })
        ]);
    }
}
