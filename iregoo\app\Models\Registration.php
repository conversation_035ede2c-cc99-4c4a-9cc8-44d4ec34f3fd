<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Registration extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'conference_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'country',
        'city',
        'organization',
        'position',
        'participant_type', // local, international
        'registration_type', // presenter, attendee
        'dietary_requirements',
        'special_needs',
        'payment_status', // pending, paid, cancelled
        'payment_method',
        'payment_reference',
        'registration_fee',
        'currency',
        'confirmation_code',
        'notes',
        'is_confirmed',
        'confirmed_at',
        'checked_in',
        'checked_in_at',
    ];

    protected $casts = [
        'is_confirmed' => 'boolean',
        'confirmed_at' => 'datetime',
        'checked_in' => 'boolean',
        'checked_in_at' => 'datetime',
        'registration_fee' => 'decimal:2',
    ];

    // Relationships
    public function conference()
    {
        return $this->belongsTo(Conference::class);
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getStatusAttribute()
    {
        if ($this->payment_status === 'cancelled') {
            return 'Cancelled';
        }
        
        if (!$this->is_confirmed) {
            return 'Pending Confirmation';
        }
        
        if ($this->payment_status === 'pending') {
            return 'Payment Pending';
        }
        
        if ($this->payment_status === 'paid') {
            return $this->checked_in ? 'Checked In' : 'Confirmed';
        }
        
        return 'Unknown';
    }

    // Scopes
    public function scopeConfirmed($query)
    {
        return $query->where('is_confirmed', true);
    }

    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    public function scopeLocal($query)
    {
        return $query->where('participant_type', 'local');
    }

    public function scopeInternational($query)
    {
        return $query->where('participant_type', 'international');
    }

    public function scopeCheckedIn($query)
    {
        return $query->where('checked_in', true);
    }

    // Methods
    public function generateConfirmationCode()
    {
        $this->confirmation_code = 'IREGO-' . strtoupper(substr(md5($this->email . time()), 0, 8));
        $this->save();
        return $this->confirmation_code;
    }

    public function confirm()
    {
        $this->is_confirmed = true;
        $this->confirmed_at = now();
        if (!$this->confirmation_code) {
            $this->generateConfirmationCode();
        }
        $this->save();
    }

    public function checkIn()
    {
        $this->checked_in = true;
        $this->checked_in_at = now();
        $this->save();
    }
}
