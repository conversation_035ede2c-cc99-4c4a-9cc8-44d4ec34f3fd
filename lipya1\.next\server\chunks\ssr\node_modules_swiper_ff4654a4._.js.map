{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/node_modules/swiper/shared/ssr-window.esm.mjs"], "sourcesContent": ["/**\n * SSR Window 5.0.0\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2025, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: February 12, 2025\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object' && 'constructor' in obj && obj.constructor === Object;\n}\nfunction extend(target, src) {\n  if (target === void 0) {\n    target = {};\n  }\n  if (src === void 0) {\n    src = {};\n  }\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      extend(target[key], src[key]);\n    }\n  });\n}\nconst ssrDocument = {\n  body: {},\n  addEventListener() {},\n  removeEventListener() {},\n  activeElement: {\n    blur() {},\n    nodeName: ''\n  },\n  querySelector() {\n    return null;\n  },\n  querySelectorAll() {\n    return [];\n  },\n  getElementById() {\n    return null;\n  },\n  createEvent() {\n    return {\n      initEvent() {}\n    };\n  },\n  createElement() {\n    return {\n      children: [],\n      childNodes: [],\n      style: {},\n      setAttribute() {},\n      getElementsByTagName() {\n        return [];\n      }\n    };\n  },\n  createElementNS() {\n    return {};\n  },\n  importNode() {\n    return null;\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  }\n};\nfunction getDocument() {\n  const doc = typeof document !== 'undefined' ? document : {};\n  extend(doc, ssrDocument);\n  return doc;\n}\nconst ssrWindow = {\n  document: ssrDocument,\n  navigator: {\n    userAgent: ''\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  },\n  history: {\n    replaceState() {},\n    pushState() {},\n    go() {},\n    back() {}\n  },\n  CustomEvent: function CustomEvent() {\n    return this;\n  },\n  addEventListener() {},\n  removeEventListener() {},\n  getComputedStyle() {\n    return {\n      getPropertyValue() {\n        return '';\n      }\n    };\n  },\n  Image() {},\n  Date() {},\n  screen: {},\n  setTimeout() {},\n  clearTimeout() {},\n  matchMedia() {\n    return {};\n  },\n  requestAnimationFrame(callback) {\n    if (typeof setTimeout === 'undefined') {\n      callback();\n      return null;\n    }\n    return setTimeout(callback, 0);\n  },\n  cancelAnimationFrame(id) {\n    if (typeof setTimeout === 'undefined') {\n      return;\n    }\n    clearTimeout(id);\n  }\n};\nfunction getWindow() {\n  const win = typeof window !== 'undefined' ? window : {};\n  extend(win, ssrWindow);\n  return win;\n}\n\nexport { getWindow as a, getDocument as g };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GACD,oCAAoC;;;;AACpC,SAAS,SAAS,GAAG;IACnB,OAAO,QAAQ,QAAQ,OAAO,QAAQ,YAAY,iBAAiB,OAAO,IAAI,WAAW,KAAK;AAChG;AACA,SAAS,OAAO,MAAM,EAAE,GAAG;IACzB,IAAI,WAAW,KAAK,GAAG;QACrB,SAAS,CAAC;IACZ;IACA,IAAI,QAAQ,KAAK,GAAG;QAClB,MAAM,CAAC;IACT;IACA,MAAM,WAAW;QAAC;QAAa;QAAe;KAAY;IAC1D,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,CAAA,MAAO,SAAS,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,CAAA;QAChE,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;aAAM,IAAI,SAAS,GAAG,CAAC,IAAI,KAAK,SAAS,MAAM,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;YACvJ,OAAO,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;QAC9B;IACF;AACF;AACA,MAAM,cAAc;IAClB,MAAM,CAAC;IACP,qBAAoB;IACpB,wBAAuB;IACvB,eAAe;QACb,SAAQ;QACR,UAAU;IACZ;IACA;QACE,OAAO;IACT;IACA;QACE,OAAO,EAAE;IACX;IACA;QACE,OAAO;IACT;IACA;QACE,OAAO;YACL,cAAa;QACf;IACF;IACA;QACE,OAAO;YACL,UAAU,EAAE;YACZ,YAAY,EAAE;YACd,OAAO,CAAC;YACR,iBAAgB;YAChB;gBACE,OAAO,EAAE;YACX;QACF;IACF;IACA;QACE,OAAO,CAAC;IACV;IACA;QACE,OAAO;IACT;IACA,UAAU;QACR,MAAM;QACN,MAAM;QACN,UAAU;QACV,MAAM;QACN,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;IACV;AACF;AACA,SAAS;IACP,MAAM,MAAM,OAAO,aAAa,cAAc,WAAW,CAAC;IAC1D,OAAO,KAAK;IACZ,OAAO;AACT;AACA,MAAM,YAAY;IAChB,UAAU;IACV,WAAW;QACT,WAAW;IACb;IACA,UAAU;QACR,MAAM;QACN,MAAM;QACN,UAAU;QACV,MAAM;QACN,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IACA,SAAS;QACP,iBAAgB;QAChB,cAAa;QACb,OAAM;QACN,SAAQ;IACV;IACA,aAAa,SAAS;QACpB,OAAO,IAAI;IACb;IACA,qBAAoB;IACpB,wBAAuB;IACvB;QACE,OAAO;YACL;gBACE,OAAO;YACT;QACF;IACF;IACA,UAAS;IACT,SAAQ;IACR,QAAQ,CAAC;IACT,eAAc;IACd,iBAAgB;IAChB;QACE,OAAO,CAAC;IACV;IACA,uBAAsB,QAAQ;QAC5B,IAAI,OAAO,eAAe,aAAa;YACrC;YACA,OAAO;QACT;QACA,OAAO,WAAW,UAAU;IAC9B;IACA,sBAAqB,EAAE;QACrB,IAAI,OAAO,eAAe,aAAa;YACrC;QACF;QACA,aAAa;IACf;AACF;AACA,SAAS;IACP,MAAM,MAAM,OAAO,WAAW,cAAc,SAAS,CAAC;IACtD,OAAO,KAAK;IACZ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/node_modules/swiper/shared/utils.mjs"], "sourcesContent": ["import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\n\nfunction classesToTokens(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return classes.trim().split(' ').filter(c => !!c.trim());\n}\n\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n  return style;\n}\nfunction getTranslate(el, axis) {\n  if (axis === void 0) {\n    axis = 'x';\n  }\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el);\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend() {\n  const to = Object(arguments.length <= 0 ? undefined : arguments[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < arguments.length; i += 1) {\n    const nextSource = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\nfunction animateCSSModeScroll(_ref) {\n  let {\n    swiper,\n    targetPosition,\n    side\n  } = _ref;\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\nfunction getSlideTransformEl(slideEl) {\n  return slideEl.querySelector('.swiper-slide-transform') || slideEl.shadowRoot && slideEl.shadowRoot.querySelector('.swiper-slide-transform') || slideEl;\n}\nfunction elementChildren(element, selector) {\n  if (selector === void 0) {\n    selector = '';\n  }\n  const window = getWindow();\n  const children = [...element.children];\n  if (window.HTMLSlotElement && element instanceof HTMLSlotElement) {\n    children.push(...element.assignedElements());\n  }\n  if (!selector) {\n    return children;\n  }\n  return children.filter(el => el.matches(selector));\n}\nfunction elementIsChildOfSlot(el, slot) {\n  // Breadth-first search through all parent's children and assigned elements\n  const elementsQueue = [slot];\n  while (elementsQueue.length > 0) {\n    const elementToCheck = elementsQueue.shift();\n    if (el === elementToCheck) {\n      return true;\n    }\n    elementsQueue.push(...elementToCheck.children, ...(elementToCheck.shadowRoot ? elementToCheck.shadowRoot.children : []), ...(elementToCheck.assignedElements ? elementToCheck.assignedElements() : []));\n  }\n}\nfunction elementIsChildOf(el, parent) {\n  const window = getWindow();\n  let isChild = parent.contains(el);\n  if (!isChild && window.HTMLSlotElement && parent instanceof HTMLSlotElement) {\n    const children = [...parent.assignedElements()];\n    isChild = children.includes(el);\n    if (!isChild) {\n      isChild = elementIsChildOfSlot(el, parent);\n    }\n  }\n  return isChild;\n}\nfunction showWarning(text) {\n  try {\n    console.warn(text);\n    return;\n  } catch (err) {\n    // err\n  }\n}\nfunction createElement(tag, classes) {\n  if (classes === void 0) {\n    classes = [];\n  }\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : classesToTokens(classes)));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top')) + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'));\n  }\n  return el.offsetWidth;\n}\nfunction makeElementsArray(el) {\n  return (Array.isArray(el) ? el : [el]).filter(e => !!e);\n}\nfunction getRotateFix(swiper) {\n  return v => {\n    if (Math.abs(v) > 0 && swiper.browser && swiper.browser.need3dFix && Math.abs(v) % 90 === 0) {\n      return v + 0.001;\n    }\n    return v;\n  };\n}\nfunction setInnerHTML(el, html) {\n  if (html === void 0) {\n    html = '';\n  }\n  if (typeof trustedTypes !== 'undefined') {\n    el.innerHTML = trustedTypes.createPolicy('html', {\n      createHTML: s => s\n    }).createHTML(html);\n  } else {\n    el.innerHTML = html;\n  }\n}\n\nexport { setCSSProperty as a, elementParents as b, createElement as c, elementOffset as d, elementChildren as e, now as f, getSlideTransformEl as g, elementOuterSize as h, elementIndex as i, classesToTokens as j, getTranslate as k, elementTransitionEnd as l, makeElementsArray as m, nextTick as n, isObject as o, getRotateFix as p, elementStyle as q, elementNextAll as r, setInnerHTML as s, elementPrevAll as t, animateCSSModeScroll as u, showWarning as v, elementIsChildOf as w, extend as x, deleteProps as y };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA,SAAS,gBAAgB,OAAO;IAC9B,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IACA,OAAO,QAAQ,IAAI,GAAG,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,IAAI;AACvD;AAEA,SAAS,YAAY,GAAG;IACtB,MAAM,SAAS;IACf,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;QAC1B,IAAI;YACF,MAAM,CAAC,IAAI,GAAG;QAChB,EAAE,OAAO,GAAG;QACV,uBAAuB;QACzB;QACA,IAAI;YACF,OAAO,MAAM,CAAC,IAAI;QACpB,EAAE,OAAO,GAAG;QACV,sBAAsB;QACxB;IACF;AACF;AACA,SAAS,SAAS,QAAQ,EAAE,KAAK;IAC/B,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ;IACV;IACA,OAAO,WAAW,UAAU;AAC9B;AACA,SAAS;IACP,OAAO,KAAK,GAAG;AACjB;AACA,SAAS,iBAAiB,EAAE;IAC1B,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,IAAI;IACJ,IAAI,QAAO,gBAAgB,EAAE;QAC3B,QAAQ,QAAO,gBAAgB,CAAC,IAAI;IACtC;IACA,IAAI,CAAC,SAAS,GAAG,YAAY,EAAE;QAC7B,QAAQ,GAAG,YAAY;IACzB;IACA,IAAI,CAAC,OAAO;QACV,QAAQ,GAAG,KAAK;IAClB;IACA,OAAO;AACT;AACA,SAAS,aAAa,EAAE,EAAE,IAAI;IAC5B,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO;IACT;IACA,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM,WAAW,iBAAiB;IAClC,IAAI,QAAO,eAAe,EAAE;QAC1B,eAAe,SAAS,SAAS,IAAI,SAAS,eAAe;QAC7D,IAAI,aAAa,KAAK,CAAC,KAAK,MAAM,GAAG,GAAG;YACtC,eAAe,aAAa,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,KAAK,MAAM,IAAI,CAAC;QAC7E;QACA,gEAAgE;QAChE,oCAAoC;QACpC,kBAAkB,IAAI,QAAO,eAAe,CAAC,iBAAiB,SAAS,KAAK;IAC9E,OAAO;QACL,kBAAkB,SAAS,YAAY,IAAI,SAAS,UAAU,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,IAAI,SAAS,SAAS,IAAI,SAAS,gBAAgB,CAAC,aAAa,OAAO,CAAC,cAAc;QACrM,SAAS,gBAAgB,QAAQ,GAAG,KAAK,CAAC;IAC5C;IACA,IAAI,SAAS,KAAK;QAChB,gCAAgC;QAChC,IAAI,QAAO,eAAe,EAAE,eAAe,gBAAgB,GAAG;aAEzD,IAAI,OAAO,MAAM,KAAK,IAAI,eAAe,WAAW,MAAM,CAAC,GAAG;aAE9D,eAAe,WAAW,MAAM,CAAC,EAAE;IAC1C;IACA,IAAI,SAAS,KAAK;QAChB,gCAAgC;QAChC,IAAI,QAAO,eAAe,EAAE,eAAe,gBAAgB,GAAG;aAEzD,IAAI,OAAO,MAAM,KAAK,IAAI,eAAe,WAAW,MAAM,CAAC,GAAG;aAE9D,eAAe,WAAW,MAAM,CAAC,EAAE;IAC1C;IACA,OAAO,gBAAgB;AACzB;AACA,SAAS,SAAS,CAAC;IACjB,OAAO,OAAO,MAAM,YAAY,MAAM,QAAQ,EAAE,WAAW,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO;AACpH;AACA,SAAS,OAAO,IAAI;IAClB,2BAA2B;IAC3B,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,WAAW,KAAK,aAAa;QAC9E,OAAO,gBAAgB;IACzB;IACA,OAAO,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,EAAE;AAC7D;AACA,SAAS;IACP,MAAM,KAAK,OAAO,UAAU,MAAM,IAAI,IAAI,YAAY,SAAS,CAAC,EAAE;IAClE,MAAM,WAAW;QAAC;QAAa;QAAe;KAAY;IAC1D,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;QAC5C,MAAM,aAAa,IAAI,KAAK,UAAU,MAAM,IAAI,IAAI,YAAY,SAAS,CAAC,EAAE;QAC5E,IAAI,eAAe,aAAa,eAAe,QAAQ,CAAC,OAAO,aAAa;YAC1E,MAAM,YAAY,OAAO,IAAI,CAAC,OAAO,aAAa,MAAM,CAAC,CAAA,MAAO,SAAS,OAAO,CAAC,OAAO;YACxF,IAAK,IAAI,YAAY,GAAG,MAAM,UAAU,MAAM,EAAE,YAAY,KAAK,aAAa,EAAG;gBAC/E,MAAM,UAAU,SAAS,CAAC,UAAU;gBACpC,MAAM,OAAO,OAAO,wBAAwB,CAAC,YAAY;gBACzD,IAAI,SAAS,aAAa,KAAK,UAAU,EAAE;oBACzC,IAAI,SAAS,EAAE,CAAC,QAAQ,KAAK,SAAS,UAAU,CAAC,QAAQ,GAAG;wBAC1D,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,EAAE;4BAClC,EAAE,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ;wBACnC,OAAO;4BACL,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBACzC;oBACF,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ,KAAK,SAAS,UAAU,CAAC,QAAQ,GAAG;wBAClE,EAAE,CAAC,QAAQ,GAAG,CAAC;wBACf,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,EAAE;4BAClC,EAAE,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ;wBACnC,OAAO;4BACL,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBACzC;oBACF,OAAO;wBACL,EAAE,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ;oBACnC;gBACF;YACF;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,eAAe,EAAE,EAAE,OAAO,EAAE,QAAQ;IAC3C,GAAG,KAAK,CAAC,WAAW,CAAC,SAAS;AAChC;AACA,SAAS,qBAAqB,IAAI;IAChC,IAAI,EACF,MAAM,EACN,cAAc,EACd,IAAI,EACL,GAAG;IACJ,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,MAAM,gBAAgB,CAAC,OAAO,SAAS;IACvC,IAAI,YAAY;IAChB,IAAI;IACJ,MAAM,WAAW,OAAO,MAAM,CAAC,KAAK;IACpC,OAAO,SAAS,CAAC,KAAK,CAAC,cAAc,GAAG;IACxC,QAAO,oBAAoB,CAAC,OAAO,cAAc;IACjD,MAAM,MAAM,iBAAiB,gBAAgB,SAAS;IACtD,MAAM,eAAe,CAAC,SAAS;QAC7B,OAAO,QAAQ,UAAU,WAAW,UAAU,QAAQ,UAAU,WAAW;IAC7E;IACA,MAAM,UAAU;QACd,OAAO,IAAI,OAAO,OAAO;QACzB,IAAI,cAAc,MAAM;YACtB,YAAY;QACd;QACA,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,OAAO,SAAS,IAAI,UAAU,IAAI;QACtE,MAAM,eAAe,MAAM,KAAK,GAAG,CAAC,WAAW,KAAK,EAAE,IAAI;QAC1D,IAAI,kBAAkB,gBAAgB,eAAe,CAAC,iBAAiB,aAAa;QACpF,IAAI,aAAa,iBAAiB,iBAAiB;YACjD,kBAAkB;QACpB;QACA,OAAO,SAAS,CAAC,QAAQ,CAAC;YACxB,CAAC,KAAK,EAAE;QACV;QACA,IAAI,aAAa,iBAAiB,iBAAiB;YACjD,OAAO,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG;YAClC,OAAO,SAAS,CAAC,KAAK,CAAC,cAAc,GAAG;YACxC,WAAW;gBACT,OAAO,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAClC,OAAO,SAAS,CAAC,QAAQ,CAAC;oBACxB,CAAC,KAAK,EAAE;gBACV;YACF;YACA,QAAO,oBAAoB,CAAC,OAAO,cAAc;YACjD;QACF;QACA,OAAO,cAAc,GAAG,QAAO,qBAAqB,CAAC;IACvD;IACA;AACF;AACA,SAAS,oBAAoB,OAAO;IAClC,OAAO,QAAQ,aAAa,CAAC,8BAA8B,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,aAAa,CAAC,8BAA8B;AAClJ;AACA,SAAS,gBAAgB,OAAO,EAAE,QAAQ;IACxC,IAAI,aAAa,KAAK,GAAG;QACvB,WAAW;IACb;IACA,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,MAAM,WAAW;WAAI,QAAQ,QAAQ;KAAC;IACtC,IAAI,QAAO,eAAe,IAAI,mBAAmB,iBAAiB;QAChE,SAAS,IAAI,IAAI,QAAQ,gBAAgB;IAC3C;IACA,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IACA,OAAO,SAAS,MAAM,CAAC,CAAA,KAAM,GAAG,OAAO,CAAC;AAC1C;AACA,SAAS,qBAAqB,EAAE,EAAE,IAAI;IACpC,2EAA2E;IAC3E,MAAM,gBAAgB;QAAC;KAAK;IAC5B,MAAO,cAAc,MAAM,GAAG,EAAG;QAC/B,MAAM,iBAAiB,cAAc,KAAK;QAC1C,IAAI,OAAO,gBAAgB;YACzB,OAAO;QACT;QACA,cAAc,IAAI,IAAI,eAAe,QAAQ,KAAM,eAAe,UAAU,GAAG,eAAe,UAAU,CAAC,QAAQ,GAAG,EAAE,KAAO,eAAe,gBAAgB,GAAG,eAAe,gBAAgB,KAAK,EAAE;IACvM;AACF;AACA,SAAS,iBAAiB,EAAE,EAAE,MAAM;IAClC,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,IAAI,UAAU,OAAO,QAAQ,CAAC;IAC9B,IAAI,CAAC,WAAW,QAAO,eAAe,IAAI,kBAAkB,iBAAiB;QAC3E,MAAM,WAAW;eAAI,OAAO,gBAAgB;SAAG;QAC/C,UAAU,SAAS,QAAQ,CAAC;QAC5B,IAAI,CAAC,SAAS;YACZ,UAAU,qBAAqB,IAAI;QACrC;IACF;IACA,OAAO;AACT;AACA,SAAS,YAAY,IAAI;IACvB,IAAI;QACF,QAAQ,IAAI,CAAC;QACb;IACF,EAAE,OAAO,KAAK;IACZ,MAAM;IACR;AACF;AACA,SAAS,cAAc,GAAG,EAAE,OAAO;IACjC,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,EAAE;IACd;IACA,MAAM,KAAK,SAAS,aAAa,CAAC;IAClC,GAAG,SAAS,CAAC,GAAG,IAAK,MAAM,OAAO,CAAC,WAAW,UAAU,gBAAgB;IACxE,OAAO;AACT;AACA,SAAS,cAAc,EAAE;IACvB,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,MAAM,YAAW,CAAA,GAAA,yJAAA,CAAA,IAAW,AAAD;IAC3B,MAAM,MAAM,GAAG,qBAAqB;IACpC,MAAM,OAAO,UAAS,IAAI;IAC1B,MAAM,YAAY,GAAG,SAAS,IAAI,KAAK,SAAS,IAAI;IACpD,MAAM,aAAa,GAAG,UAAU,IAAI,KAAK,UAAU,IAAI;IACvD,MAAM,YAAY,OAAO,UAAS,QAAO,OAAO,GAAG,GAAG,SAAS;IAC/D,MAAM,aAAa,OAAO,UAAS,QAAO,OAAO,GAAG,GAAG,UAAU;IACjE,OAAO;QACL,KAAK,IAAI,GAAG,GAAG,YAAY;QAC3B,MAAM,IAAI,IAAI,GAAG,aAAa;IAChC;AACF;AACA,SAAS,eAAe,EAAE,EAAE,QAAQ;IAClC,MAAM,UAAU,EAAE;IAClB,MAAO,GAAG,sBAAsB,CAAE;QAChC,MAAM,OAAO,GAAG,sBAAsB,EAAE,sBAAsB;QAC9D,IAAI,UAAU;YACZ,IAAI,KAAK,OAAO,CAAC,WAAW,QAAQ,IAAI,CAAC;QAC3C,OAAO,QAAQ,IAAI,CAAC;QACpB,KAAK;IACP;IACA,OAAO;AACT;AACA,SAAS,eAAe,EAAE,EAAE,QAAQ;IAClC,MAAM,UAAU,EAAE;IAClB,MAAO,GAAG,kBAAkB,CAAE;QAC5B,MAAM,OAAO,GAAG,kBAAkB,EAAE,sBAAsB;QAC1D,IAAI,UAAU;YACZ,IAAI,KAAK,OAAO,CAAC,WAAW,QAAQ,IAAI,CAAC;QAC3C,OAAO,QAAQ,IAAI,CAAC;QACpB,KAAK;IACP;IACA,OAAO;AACT;AACA,SAAS,aAAa,EAAE,EAAE,IAAI;IAC5B,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,OAAO,QAAO,gBAAgB,CAAC,IAAI,MAAM,gBAAgB,CAAC;AAC5D;AACA,SAAS,aAAa,EAAE;IACtB,IAAI,QAAQ;IACZ,IAAI;IACJ,IAAI,OAAO;QACT,IAAI;QACJ,2BAA2B;QAC3B,MAAO,CAAC,QAAQ,MAAM,eAAe,MAAM,KAAM;YAC/C,IAAI,MAAM,QAAQ,KAAK,GAAG,KAAK;QACjC;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,eAAe,EAAE,EAAE,QAAQ;IAClC,MAAM,UAAU,EAAE,EAAE,sBAAsB;IAC1C,IAAI,SAAS,GAAG,aAAa,EAAE,sBAAsB;IACrD,MAAO,OAAQ;QACb,IAAI,UAAU;YACZ,IAAI,OAAO,OAAO,CAAC,WAAW,QAAQ,IAAI,CAAC;QAC7C,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;QACA,SAAS,OAAO,aAAa;IAC/B;IACA,OAAO;AACT;AACA,SAAS,qBAAqB,EAAE,EAAE,QAAQ;IACxC,SAAS,aAAa,CAAC;QACrB,IAAI,EAAE,MAAM,KAAK,IAAI;QACrB,SAAS,IAAI,CAAC,IAAI;QAClB,GAAG,mBAAmB,CAAC,iBAAiB;IAC1C;IACA,IAAI,UAAU;QACZ,GAAG,gBAAgB,CAAC,iBAAiB;IACvC;AACF;AACA,SAAS,iBAAiB,EAAE,EAAE,IAAI,EAAE,cAAc;IAChD,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,IAAI,gBAAgB;QAClB,OAAO,EAAE,CAAC,SAAS,UAAU,gBAAgB,eAAe,GAAG,WAAW,QAAO,gBAAgB,CAAC,IAAI,MAAM,gBAAgB,CAAC,SAAS,UAAU,iBAAiB,iBAAiB,WAAW,QAAO,gBAAgB,CAAC,IAAI,MAAM,gBAAgB,CAAC,SAAS,UAAU,gBAAgB;IACrR;IACA,OAAO,GAAG,WAAW;AACvB;AACA,SAAS,kBAAkB,EAAE;IAC3B,OAAO,CAAC,MAAM,OAAO,CAAC,MAAM,KAAK;QAAC;KAAG,EAAE,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC;AACvD;AACA,SAAS,aAAa,MAAM;IAC1B,OAAO,CAAA;QACL,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,KAAK,OAAO,GAAG;YAC3F,OAAO,IAAI;QACb;QACA,OAAO;IACT;AACF;AACA,SAAS,aAAa,EAAE,EAAE,IAAI;IAC5B,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO;IACT;IACA,IAAI,OAAO,iBAAiB,aAAa;QACvC,GAAG,SAAS,GAAG,aAAa,YAAY,CAAC,QAAQ;YAC/C,YAAY,CAAA,IAAK;QACnB,GAAG,UAAU,CAAC;IAChB,OAAO;QACL,GAAG,SAAS,GAAG;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/node_modules/swiper/shared/swiper-core.mjs"], "sourcesContent": ["import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { b as elementParents, q as elementStyle, e as elementChildren, a as setCSSProperty, h as elementOuterSize, r as elementNextAll, t as elementPrevAll, k as getTranslate, u as animateCSSModeScroll, n as nextTick, v as showWarning, c as createElement, w as elementIsChildOf, f as now, x as extend, i as elementIndex, y as deleteProps } from './utils.mjs';\n\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\n\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\n\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\n\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\n\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\n\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\n\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\n\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\n\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\n\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\n\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\n\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\n\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\n\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\n\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && dir === 'reset') {\n    swiper.emit(`slideResetTransition${step}`);\n  } else if (runCallbacks && activeIndex !== previousIndex) {\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\n\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\n\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\n\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\n\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (slideToIndex < swiper.loopedSlides - slidesPerView / 2 || slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2) {\n        swiper.loopFix();\n        slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\n\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\n\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\n\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\n\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\n\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\n\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\n\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n\nvar classes = {\n  addClasses,\n  removeClasses\n};\n\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\n\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\n\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\n\nexport { Swiper as S, defaults as d };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,IAAI;AACJ,SAAS;IACP,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,MAAM,YAAW,CAAA,GAAA,yJAAA,CAAA,IAAW,AAAD;IAC3B,OAAO;QACL,cAAc,UAAS,eAAe,IAAI,UAAS,eAAe,CAAC,KAAK,IAAI,oBAAoB,UAAS,eAAe,CAAC,KAAK;QAC9H,OAAO,CAAC,CAAC,CAAC,kBAAkB,WAAU,QAAO,aAAa,IAAI,qBAAoB,QAAO,aAAa;IACxG;AACF;AACA,SAAS;IACP,IAAI,CAAC,SAAS;QACZ,UAAU;IACZ;IACA,OAAO;AACT;AAEA,IAAI;AACJ,SAAS,WAAW,KAAK;IACvB,IAAI,EACF,SAAS,EACV,GAAG,UAAU,KAAK,IAAI,CAAC,IAAI;IAC5B,MAAM,UAAU;IAChB,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,MAAM,WAAW,QAAO,SAAS,CAAC,QAAQ;IAC1C,MAAM,KAAK,aAAa,QAAO,SAAS,CAAC,SAAS;IAClD,MAAM,SAAS;QACb,KAAK;QACL,SAAS;IACX;IACA,MAAM,cAAc,QAAO,MAAM,CAAC,KAAK;IACvC,MAAM,eAAe,QAAO,MAAM,CAAC,MAAM;IACzC,MAAM,UAAU,GAAG,KAAK,CAAC,gCAAgC,sBAAsB;IAC/E,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,MAAM,OAAO,GAAG,KAAK,CAAC;IACtB,MAAM,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;IACjC,MAAM,UAAU,aAAa;IAC7B,IAAI,QAAQ,aAAa;IAEzB,gBAAgB;IAChB,MAAM,cAAc;QAAC;QAAa;QAAa;QAAY;QAAY;QAAY;QAAY;QAAY;QAAY;QAAY;QAAY;QAAY;KAAW;IACtK,IAAI,CAAC,QAAQ,SAAS,QAAQ,KAAK,IAAI,YAAY,OAAO,CAAC,GAAG,YAAY,CAAC,EAAE,cAAc,KAAK,GAAG;QACjG,OAAO,GAAG,KAAK,CAAC;QAChB,IAAI,CAAC,MAAM,OAAO;YAAC;YAAG;YAAG;SAAS;QAClC,QAAQ;IACV;IAEA,UAAU;IACV,IAAI,WAAW,CAAC,SAAS;QACvB,OAAO,EAAE,GAAG;QACZ,OAAO,OAAO,GAAG;IACnB;IACA,IAAI,QAAQ,UAAU,MAAM;QAC1B,OAAO,EAAE,GAAG;QACZ,OAAO,GAAG,GAAG;IACf;IAEA,gBAAgB;IAChB,OAAO;AACT;AACA,SAAS,UAAU,SAAS;IAC1B,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY,CAAC;IACf;IACA,IAAI,CAAC,cAAc;QACjB,eAAe,WAAW;IAC5B;IACA,OAAO;AACT;AAEA,IAAI;AACJ,SAAS;IACP,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,MAAM,SAAS;IACf,IAAI,qBAAqB;IACzB,SAAS;QACP,MAAM,KAAK,QAAO,SAAS,CAAC,SAAS,CAAC,WAAW;QACjD,OAAO,GAAG,OAAO,CAAC,aAAa,KAAK,GAAG,OAAO,CAAC,YAAY,KAAK,GAAG,OAAO,CAAC,aAAa;IAC1F;IACA,IAAI,YAAY;QACd,MAAM,KAAK,OAAO,QAAO,SAAS,CAAC,SAAS;QAC5C,IAAI,GAAG,QAAQ,CAAC,aAAa;YAC3B,MAAM,CAAC,OAAO,MAAM,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,OAAO;YAC1F,qBAAqB,QAAQ,MAAM,UAAU,MAAM,QAAQ;QAC7D;IACF;IACA,MAAM,YAAY,+CAA+C,IAAI,CAAC,QAAO,SAAS,CAAC,SAAS;IAChG,MAAM,kBAAkB;IACxB,MAAM,YAAY,mBAAmB,aAAa,OAAO,GAAG;IAC5D,OAAO;QACL,UAAU,sBAAsB;QAChC;QACA;QACA;IACF;AACF;AACA,SAAS;IACP,IAAI,CAAC,SAAS;QACZ,UAAU;IACZ;IACA,OAAO;AACT;AAEA,SAAS,OAAO,IAAI;IAClB,IAAI,EACF,MAAM,EACN,EAAE,EACF,IAAI,EACL,GAAG;IACJ,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,IAAI,WAAW;IACf,IAAI,iBAAiB;IACrB,MAAM,gBAAgB;QACpB,IAAI,CAAC,UAAU,OAAO,SAAS,IAAI,CAAC,OAAO,WAAW,EAAE;QACxD,KAAK;QACL,KAAK;IACP;IACA,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU,OAAO,SAAS,IAAI,CAAC,OAAO,WAAW,EAAE;QACxD,WAAW,IAAI,eAAe,CAAA;YAC5B,iBAAiB,QAAO,qBAAqB,CAAC;gBAC5C,MAAM,EACJ,KAAK,EACL,MAAM,EACP,GAAG;gBACJ,IAAI,WAAW;gBACf,IAAI,YAAY;gBAChB,QAAQ,OAAO,CAAC,CAAA;oBACd,IAAI,EACF,cAAc,EACd,WAAW,EACX,MAAM,EACP,GAAG;oBACJ,IAAI,UAAU,WAAW,OAAO,EAAE,EAAE;oBACpC,WAAW,cAAc,YAAY,KAAK,GAAG,CAAC,cAAc,CAAC,EAAE,IAAI,cAAc,EAAE,UAAU;oBAC7F,YAAY,cAAc,YAAY,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE,IAAI,cAAc,EAAE,SAAS;gBAChG;gBACA,IAAI,aAAa,SAAS,cAAc,QAAQ;oBAC9C;gBACF;YACF;QACF;QACA,SAAS,OAAO,CAAC,OAAO,EAAE;IAC5B;IACA,MAAM,iBAAiB;QACrB,IAAI,gBAAgB;YAClB,QAAO,oBAAoB,CAAC;QAC9B;QACA,IAAI,YAAY,SAAS,SAAS,IAAI,OAAO,EAAE,EAAE;YAC/C,SAAS,SAAS,CAAC,OAAO,EAAE;YAC5B,WAAW;QACb;IACF;IACA,MAAM,2BAA2B;QAC/B,IAAI,CAAC,UAAU,OAAO,SAAS,IAAI,CAAC,OAAO,WAAW,EAAE;QACxD,KAAK;IACP;IACA,GAAG,QAAQ;QACT,IAAI,OAAO,MAAM,CAAC,cAAc,IAAI,OAAO,QAAO,cAAc,KAAK,aAAa;YAChF;YACA;QACF;QACA,QAAO,gBAAgB,CAAC,UAAU;QAClC,QAAO,gBAAgB,CAAC,qBAAqB;IAC/C;IACA,GAAG,WAAW;QACZ;QACA,QAAO,mBAAmB,CAAC,UAAU;QACrC,QAAO,mBAAmB,CAAC,qBAAqB;IAClD;AACF;AAEA,SAAS,SAAS,IAAI;IACpB,IAAI,EACF,MAAM,EACN,YAAY,EACZ,EAAE,EACF,IAAI,EACL,GAAG;IACJ,MAAM,YAAY,EAAE;IACpB,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,MAAM,SAAS,SAAU,MAAM,EAAE,OAAO;QACtC,IAAI,YAAY,KAAK,GAAG;YACtB,UAAU,CAAC;QACb;QACA,MAAM,eAAe,QAAO,gBAAgB,IAAI,QAAO,sBAAsB;QAC7E,MAAM,WAAW,IAAI,aAAa,CAAA;YAChC,oDAAoD;YACpD,oDAAoD;YACpD,6CAA6C;YAC7C,IAAI,OAAO,mBAAmB,EAAE;YAChC,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,KAAK,kBAAkB,SAAS,CAAC,EAAE;gBACnC;YACF;YACA,MAAM,iBAAiB,SAAS;gBAC9B,KAAK,kBAAkB,SAAS,CAAC,EAAE;YACrC;YACA,IAAI,QAAO,qBAAqB,EAAE;gBAChC,QAAO,qBAAqB,CAAC;YAC/B,OAAO;gBACL,QAAO,UAAU,CAAC,gBAAgB;YACpC;QACF;QACA,SAAS,OAAO,CAAC,QAAQ;YACvB,YAAY,OAAO,QAAQ,UAAU,KAAK,cAAc,OAAO,QAAQ,UAAU;YACjF,WAAW,OAAO,SAAS,IAAI,CAAC,OAAO,QAAQ,SAAS,KAAK,cAAc,OAAO,OAAO,EAAE,SAAS;YACpG,eAAe,OAAO,QAAQ,aAAa,KAAK,cAAc,OAAO,QAAQ,aAAa;QAC5F;QACA,UAAU,IAAI,CAAC;IACjB;IACA,MAAM,OAAO;QACX,IAAI,CAAC,OAAO,MAAM,CAAC,QAAQ,EAAE;QAC7B,IAAI,OAAO,MAAM,CAAC,cAAc,EAAE;YAChC,MAAM,mBAAmB,CAAA,GAAA,0IAAA,CAAA,IAAc,AAAD,EAAE,OAAO,MAAM;YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,KAAK,EAAG;gBACnD,OAAO,gBAAgB,CAAC,EAAE;YAC5B;QACF;QACA,oBAAoB;QACpB,OAAO,OAAO,MAAM,EAAE;YACpB,WAAW,OAAO,MAAM,CAAC,oBAAoB;QAC/C;QAEA,kBAAkB;QAClB,OAAO,OAAO,SAAS,EAAE;YACvB,YAAY;QACd;IACF;IACA,MAAM,UAAU;QACd,UAAU,OAAO,CAAC,CAAA;YAChB,SAAS,UAAU;QACrB;QACA,UAAU,MAAM,CAAC,GAAG,UAAU,MAAM;IACtC;IACA,aAAa;QACX,UAAU;QACV,gBAAgB;QAChB,sBAAsB;IACxB;IACA,GAAG,QAAQ;IACX,GAAG,WAAW;AAChB;AAEA,uCAAuC,GAEvC,IAAI,gBAAgB;IAClB,IAAG,MAAM,EAAE,OAAO,EAAE,QAAQ;QAC1B,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,KAAK,eAAe,IAAI,KAAK,SAAS,EAAE,OAAO;QACpD,IAAI,OAAO,YAAY,YAAY,OAAO;QAC1C,MAAM,SAAS,WAAW,YAAY;QACtC,OAAO,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;YACxB,IAAI,CAAC,KAAK,eAAe,CAAC,MAAM,EAAE,KAAK,eAAe,CAAC,MAAM,GAAG,EAAE;YAClE,KAAK,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC;QACtC;QACA,OAAO;IACT;IACA,MAAK,MAAM,EAAE,OAAO,EAAE,QAAQ;QAC5B,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,KAAK,eAAe,IAAI,KAAK,SAAS,EAAE,OAAO;QACpD,IAAI,OAAO,YAAY,YAAY,OAAO;QAC1C,SAAS;YACP,KAAK,GAAG,CAAC,QAAQ;YACjB,IAAI,YAAY,cAAc,EAAE;gBAC9B,OAAO,YAAY,cAAc;YACnC;YACA,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;gBACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;YAC9B;YACA,QAAQ,KAAK,CAAC,MAAM;QACtB;QACA,YAAY,cAAc,GAAG;QAC7B,OAAO,KAAK,EAAE,CAAC,QAAQ,aAAa;IACtC;IACA,OAAM,OAAO,EAAE,QAAQ;QACrB,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,KAAK,eAAe,IAAI,KAAK,SAAS,EAAE,OAAO;QACpD,IAAI,OAAO,YAAY,YAAY,OAAO;QAC1C,MAAM,SAAS,WAAW,YAAY;QACtC,IAAI,KAAK,kBAAkB,CAAC,OAAO,CAAC,WAAW,GAAG;YAChD,KAAK,kBAAkB,CAAC,OAAO,CAAC;QAClC;QACA,OAAO;IACT;IACA,QAAO,OAAO;QACZ,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,KAAK,eAAe,IAAI,KAAK,SAAS,EAAE,OAAO;QACpD,IAAI,CAAC,KAAK,kBAAkB,EAAE,OAAO;QACrC,MAAM,QAAQ,KAAK,kBAAkB,CAAC,OAAO,CAAC;QAC9C,IAAI,SAAS,GAAG;YACd,KAAK,kBAAkB,CAAC,MAAM,CAAC,OAAO;QACxC;QACA,OAAO;IACT;IACA,KAAI,MAAM,EAAE,OAAO;QACjB,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,KAAK,eAAe,IAAI,KAAK,SAAS,EAAE,OAAO;QACpD,IAAI,CAAC,KAAK,eAAe,EAAE,OAAO;QAClC,OAAO,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;YACxB,IAAI,OAAO,YAAY,aAAa;gBAClC,KAAK,eAAe,CAAC,MAAM,GAAG,EAAE;YAClC,OAAO,IAAI,KAAK,eAAe,CAAC,MAAM,EAAE;gBACtC,KAAK,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,cAAc;oBACjD,IAAI,iBAAiB,WAAW,aAAa,cAAc,IAAI,aAAa,cAAc,KAAK,SAAS;wBACtG,KAAK,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO;oBAC5C;gBACF;YACF;QACF;QACA,OAAO;IACT;IACA;QACE,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,KAAK,eAAe,IAAI,KAAK,SAAS,EAAE,OAAO;QACpD,IAAI,CAAC,KAAK,eAAe,EAAE,OAAO;QAClC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,YAAY,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG;YACzD,SAAS,IAAI,CAAC,EAAE;YAChB,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM;YAChC,UAAU;QACZ,OAAO;YACL,SAAS,IAAI,CAAC,EAAE,CAAC,MAAM;YACvB,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI;YACnB,UAAU,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI;QAC/B;QACA,KAAK,OAAO,CAAC;QACb,MAAM,cAAc,MAAM,OAAO,CAAC,UAAU,SAAS,OAAO,KAAK,CAAC;QAClE,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,MAAM,EAAE;gBAC7D,KAAK,kBAAkB,CAAC,OAAO,CAAC,CAAA;oBAC9B,aAAa,KAAK,CAAC,SAAS;wBAAC;2BAAU;qBAAK;gBAC9C;YACF;YACA,IAAI,KAAK,eAAe,IAAI,KAAK,eAAe,CAAC,MAAM,EAAE;gBACvD,KAAK,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;oBAClC,aAAa,KAAK,CAAC,SAAS;gBAC9B;YACF;QACF;QACA,OAAO;IACT;AACF;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,IAAI;IACJ,IAAI;IACJ,MAAM,KAAK,OAAO,EAAE;IACpB,IAAI,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,eAAe,OAAO,MAAM,CAAC,KAAK,KAAK,MAAM;QAC9E,QAAQ,OAAO,MAAM,CAAC,KAAK;IAC7B,OAAO;QACL,QAAQ,GAAG,WAAW;IACxB;IACA,IAAI,OAAO,OAAO,MAAM,CAAC,MAAM,KAAK,eAAe,OAAO,MAAM,CAAC,MAAM,KAAK,MAAM;QAChF,SAAS,OAAO,MAAM,CAAC,MAAM;IAC/B,OAAO;QACL,SAAS,GAAG,YAAY;IAC1B;IACA,IAAI,UAAU,KAAK,OAAO,YAAY,MAAM,WAAW,KAAK,OAAO,UAAU,IAAI;QAC/E;IACF;IAEA,oBAAoB;IACpB,QAAQ,QAAQ,SAAS,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,IAAI,mBAAmB,GAAG,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,IAAI,oBAAoB,GAAG;IACvH,SAAS,SAAS,SAAS,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,IAAI,kBAAkB,GAAG,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,IAAI,qBAAqB,GAAG;IACzH,IAAI,OAAO,KAAK,CAAC,QAAQ,QAAQ;IACjC,IAAI,OAAO,KAAK,CAAC,SAAS,SAAS;IACnC,OAAO,MAAM,CAAC,QAAQ;QACpB;QACA;QACA,MAAM,OAAO,YAAY,KAAK,QAAQ;IACxC;AACF;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,SAAS,0BAA0B,IAAI,EAAE,KAAK;QAC5C,OAAO,WAAW,KAAK,gBAAgB,CAAC,OAAO,iBAAiB,CAAC,WAAW;IAC9E;IACA,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,MAAM,UAAU,EAChB,cAAc,GAAG,EACjB,QAAQ,EACT,GAAG;IACJ,MAAM,YAAY,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO;IAC1D,MAAM,uBAAuB,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,MAAM;IAC5F,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,IAAe,AAAD,EAAE,UAAU,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC;IACrF,MAAM,eAAe,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,OAAO,MAAM;IAC7E,IAAI,WAAW,EAAE;IACjB,MAAM,aAAa,EAAE;IACrB,MAAM,kBAAkB,EAAE;IAC1B,IAAI,eAAe,OAAO,kBAAkB;IAC5C,IAAI,OAAO,iBAAiB,YAAY;QACtC,eAAe,OAAO,kBAAkB,CAAC,IAAI,CAAC;IAChD;IACA,IAAI,cAAc,OAAO,iBAAiB;IAC1C,IAAI,OAAO,gBAAgB,YAAY;QACrC,cAAc,OAAO,iBAAiB,CAAC,IAAI,CAAC;IAC9C;IACA,MAAM,yBAAyB,OAAO,QAAQ,CAAC,MAAM;IACrD,MAAM,2BAA2B,OAAO,UAAU,CAAC,MAAM;IACzD,IAAI,eAAe,OAAO,YAAY;IACtC,IAAI,gBAAgB,CAAC;IACrB,IAAI,gBAAgB;IACpB,IAAI,QAAQ;IACZ,IAAI,OAAO,eAAe,aAAa;QACrC;IACF;IACA,IAAI,OAAO,iBAAiB,YAAY,aAAa,OAAO,CAAC,QAAQ,GAAG;QACtE,eAAe,WAAW,aAAa,OAAO,CAAC,KAAK,OAAO,MAAM;IACnE,OAAO,IAAI,OAAO,iBAAiB,UAAU;QAC3C,eAAe,WAAW;IAC5B;IACA,OAAO,WAAW,GAAG,CAAC;IAEtB,gBAAgB;IAChB,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,KAAK;YACP,QAAQ,KAAK,CAAC,UAAU,GAAG;QAC7B,OAAO;YACL,QAAQ,KAAK,CAAC,WAAW,GAAG;QAC9B;QACA,QAAQ,KAAK,CAAC,YAAY,GAAG;QAC7B,QAAQ,KAAK,CAAC,SAAS,GAAG;IAC5B;IAEA,wBAAwB;IACxB,IAAI,OAAO,cAAc,IAAI,OAAO,OAAO,EAAE;QAC3C,CAAA,GAAA,0IAAA,CAAA,IAAc,AAAD,EAAE,WAAW,mCAAmC;QAC7D,CAAA,GAAA,0IAAA,CAAA,IAAc,AAAD,EAAE,WAAW,kCAAkC;IAC9D;IACA,MAAM,cAAc,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG,KAAK,OAAO,IAAI;IACtE,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,OAAO,IAAI,OAAO,IAAI,EAAE;QACtB,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,cAAc;IACd,IAAI;IACJ,MAAM,uBAAuB,OAAO,aAAa,KAAK,UAAU,OAAO,WAAW,IAAI,OAAO,IAAI,CAAC,OAAO,WAAW,EAAE,MAAM,CAAC,CAAA;QAC3H,OAAO,OAAO,OAAO,WAAW,CAAC,IAAI,CAAC,aAAa,KAAK;IAC1D,GAAG,MAAM,GAAG;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,KAAK,EAAG;QACxC,YAAY;QACZ,IAAI;QACJ,IAAI,MAAM,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,EAAE;QAChC,IAAI,aAAa;YACf,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,OAAO;QACpC;QACA,IAAI,MAAM,CAAC,EAAE,IAAI,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,OAAO,eAAe,QAAQ,UAAU,sBAAsB;QAE5F,IAAI,OAAO,aAAa,KAAK,QAAQ;YACnC,IAAI,sBAAsB;gBACxB,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,iBAAiB,CAAC,SAAS,GAAG,EAAE;YACzD;YACA,MAAM,cAAc,iBAAiB;YACrC,MAAM,mBAAmB,MAAM,KAAK,CAAC,SAAS;YAC9C,MAAM,yBAAyB,MAAM,KAAK,CAAC,eAAe;YAC1D,IAAI,kBAAkB;gBACpB,MAAM,KAAK,CAAC,SAAS,GAAG;YAC1B;YACA,IAAI,wBAAwB;gBAC1B,MAAM,KAAK,CAAC,eAAe,GAAG;YAChC;YACA,IAAI,OAAO,YAAY,EAAE;gBACvB,YAAY,OAAO,YAAY,KAAK,CAAA,GAAA,0IAAA,CAAA,IAAgB,AAAD,EAAE,OAAO,SAAS,QAAQ,CAAA,GAAA,0IAAA,CAAA,IAAgB,AAAD,EAAE,OAAO,UAAU;YACjH,OAAO;gBACL,2BAA2B;gBAC3B,MAAM,QAAQ,0BAA0B,aAAa;gBACrD,MAAM,cAAc,0BAA0B,aAAa;gBAC3D,MAAM,eAAe,0BAA0B,aAAa;gBAC5D,MAAM,aAAa,0BAA0B,aAAa;gBAC1D,MAAM,cAAc,0BAA0B,aAAa;gBAC3D,MAAM,YAAY,YAAY,gBAAgB,CAAC;gBAC/C,IAAI,aAAa,cAAc,cAAc;oBAC3C,YAAY,QAAQ,aAAa;gBACnC,OAAO;oBACL,MAAM,EACJ,WAAW,EACX,WAAW,EACZ,GAAG;oBACJ,YAAY,QAAQ,cAAc,eAAe,aAAa,cAAc,CAAC,cAAc,WAAW;gBACxG;YACF;YACA,IAAI,kBAAkB;gBACpB,MAAM,KAAK,CAAC,SAAS,GAAG;YAC1B;YACA,IAAI,wBAAwB;gBAC1B,MAAM,KAAK,CAAC,eAAe,GAAG;YAChC;YACA,IAAI,OAAO,YAAY,EAAE,YAAY,KAAK,KAAK,CAAC;QAClD,OAAO;YACL,YAAY,CAAC,aAAa,CAAC,OAAO,aAAa,GAAG,CAAC,IAAI,YAAY,IAAI,OAAO,aAAa;YAC3F,IAAI,OAAO,YAAY,EAAE,YAAY,KAAK,KAAK,CAAC;YAChD,IAAI,MAAM,CAAC,EAAE,EAAE;gBACb,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,iBAAiB,CAAC,SAAS,GAAG,GAAG,UAAU,EAAE,CAAC;YACvE;QACF;QACA,IAAI,MAAM,CAAC,EAAE,EAAE;YACb,MAAM,CAAC,EAAE,CAAC,eAAe,GAAG;QAC9B;QACA,gBAAgB,IAAI,CAAC;QACrB,IAAI,OAAO,cAAc,EAAE;YACzB,gBAAgB,gBAAgB,YAAY,IAAI,gBAAgB,IAAI;YACpE,IAAI,kBAAkB,KAAK,MAAM,GAAG,gBAAgB,gBAAgB,aAAa,IAAI;YACrF,IAAI,MAAM,GAAG,gBAAgB,gBAAgB,aAAa,IAAI;YAC9D,IAAI,KAAK,GAAG,CAAC,iBAAiB,IAAI,MAAM,gBAAgB;YACxD,IAAI,OAAO,YAAY,EAAE,gBAAgB,KAAK,KAAK,CAAC;YACpD,IAAI,QAAQ,OAAO,cAAc,KAAK,GAAG,SAAS,IAAI,CAAC;YACvD,WAAW,IAAI,CAAC;QAClB,OAAO;YACL,IAAI,OAAO,YAAY,EAAE,gBAAgB,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC,OAAO,MAAM,CAAC,kBAAkB,EAAE,MAAM,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,GAAG,SAAS,IAAI,CAAC;YACpH,WAAW,IAAI,CAAC;YAChB,gBAAgB,gBAAgB,YAAY;QAC9C;QACA,OAAO,WAAW,IAAI,YAAY;QAClC,gBAAgB;QAChB,SAAS;IACX;IACA,OAAO,WAAW,GAAG,KAAK,GAAG,CAAC,OAAO,WAAW,EAAE,cAAc;IAChE,IAAI,OAAO,YAAY,CAAC,OAAO,MAAM,KAAK,WAAW,OAAO,MAAM,KAAK,WAAW,GAAG;QACnF,UAAU,KAAK,CAAC,KAAK,GAAG,GAAG,OAAO,WAAW,GAAG,aAAa,EAAE,CAAC;IAClE;IACA,IAAI,OAAO,cAAc,EAAE;QACzB,UAAU,KAAK,CAAC,OAAO,iBAAiB,CAAC,SAAS,GAAG,GAAG,OAAO,WAAW,GAAG,aAAa,EAAE,CAAC;IAC/F;IACA,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW;IAC3C;IAEA,+CAA+C;IAC/C,IAAI,CAAC,OAAO,cAAc,EAAE;QAC1B,MAAM,gBAAgB,EAAE;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;YAC3C,IAAI,iBAAiB,QAAQ,CAAC,EAAE;YAChC,IAAI,OAAO,YAAY,EAAE,iBAAiB,KAAK,KAAK,CAAC;YACrD,IAAI,QAAQ,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,YAAY;gBAClD,cAAc,IAAI,CAAC;YACrB;QACF;QACA,WAAW;QACX,IAAI,KAAK,KAAK,CAAC,OAAO,WAAW,GAAG,cAAc,KAAK,KAAK,CAAC,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,IAAI,GAAG;YAC/F,SAAS,IAAI,CAAC,OAAO,WAAW,GAAG;QACrC;IACF;IACA,IAAI,aAAa,OAAO,IAAI,EAAE;QAC5B,MAAM,OAAO,eAAe,CAAC,EAAE,GAAG;QAClC,IAAI,OAAO,cAAc,GAAG,GAAG;YAC7B,MAAM,SAAS,KAAK,IAAI,CAAC,CAAC,OAAO,OAAO,CAAC,YAAY,GAAG,OAAO,OAAO,CAAC,WAAW,IAAI,OAAO,cAAc;YAC3G,MAAM,YAAY,OAAO,OAAO,cAAc;YAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK,EAAG;gBAClC,SAAS,IAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,GAAG;YAChD;QACF;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,OAAO,CAAC,YAAY,GAAG,OAAO,OAAO,CAAC,WAAW,EAAE,KAAK,EAAG;YACpF,IAAI,OAAO,cAAc,KAAK,GAAG;gBAC/B,SAAS,IAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,GAAG;YAChD;YACA,WAAW,IAAI,CAAC,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,GAAG;YACpD,OAAO,WAAW,IAAI;QACxB;IACF;IACA,IAAI,SAAS,MAAM,KAAK,GAAG,WAAW;QAAC;KAAE;IACzC,IAAI,iBAAiB,GAAG;QACtB,MAAM,MAAM,OAAO,YAAY,MAAM,MAAM,eAAe,OAAO,iBAAiB,CAAC;QACnF,OAAO,MAAM,CAAC,CAAC,GAAG;YAChB,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE,OAAO;YAC3C,IAAI,eAAe,OAAO,MAAM,GAAG,GAAG;gBACpC,OAAO;YACT;YACA,OAAO;QACT,GAAG,OAAO,CAAC,CAAA;YACT,QAAQ,KAAK,CAAC,IAAI,GAAG,GAAG,aAAa,EAAE,CAAC;QAC1C;IACF;IACA,IAAI,OAAO,cAAc,IAAI,OAAO,oBAAoB,EAAE;QACxD,IAAI,gBAAgB;QACpB,gBAAgB,OAAO,CAAC,CAAA;YACtB,iBAAiB,iBAAiB,CAAC,gBAAgB,CAAC;QACtD;QACA,iBAAiB;QACjB,MAAM,UAAU,gBAAgB,aAAa,gBAAgB,aAAa;QAC1E,WAAW,SAAS,GAAG,CAAC,CAAA;YACtB,IAAI,QAAQ,GAAG,OAAO,CAAC;YACvB,IAAI,OAAO,SAAS,OAAO,UAAU;YACrC,OAAO;QACT;IACF;IACA,IAAI,OAAO,wBAAwB,EAAE;QACnC,IAAI,gBAAgB;QACpB,gBAAgB,OAAO,CAAC,CAAA;YACtB,iBAAiB,iBAAiB,CAAC,gBAAgB,CAAC;QACtD;QACA,iBAAiB;QACjB,MAAM,aAAa,CAAC,OAAO,kBAAkB,IAAI,CAAC,IAAI,CAAC,OAAO,iBAAiB,IAAI,CAAC;QACpF,IAAI,gBAAgB,aAAa,YAAY;YAC3C,MAAM,kBAAkB,CAAC,aAAa,gBAAgB,UAAU,IAAI;YACpE,SAAS,OAAO,CAAC,CAAC,MAAM;gBACtB,QAAQ,CAAC,UAAU,GAAG,OAAO;YAC/B;YACA,WAAW,OAAO,CAAC,CAAC,MAAM;gBACxB,UAAU,CAAC,UAAU,GAAG,OAAO;YACjC;QACF;IACF;IACA,OAAO,MAAM,CAAC,QAAQ;QACpB;QACA;QACA;QACA;IACF;IACA,IAAI,OAAO,cAAc,IAAI,OAAO,OAAO,IAAI,CAAC,OAAO,oBAAoB,EAAE;QAC3E,CAAA,GAAA,0IAAA,CAAA,IAAc,AAAD,EAAE,WAAW,mCAAmC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;QAChF,CAAA,GAAA,0IAAA,CAAA,IAAc,AAAD,EAAE,WAAW,kCAAkC,GAAG,OAAO,IAAI,GAAG,IAAI,eAAe,CAAC,gBAAgB,MAAM,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;QACpI,MAAM,gBAAgB,CAAC,OAAO,QAAQ,CAAC,EAAE;QACzC,MAAM,kBAAkB,CAAC,OAAO,UAAU,CAAC,EAAE;QAC7C,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA,IAAK,IAAI;QAC/C,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,IAAI;IACrD;IACA,IAAI,iBAAiB,sBAAsB;QACzC,OAAO,IAAI,CAAC;IACd;IACA,IAAI,SAAS,MAAM,KAAK,wBAAwB;QAC9C,IAAI,OAAO,MAAM,CAAC,aAAa,EAAE,OAAO,aAAa;QACrD,OAAO,IAAI,CAAC;IACd;IACA,IAAI,WAAW,MAAM,KAAK,0BAA0B;QAClD,OAAO,IAAI,CAAC;IACd;IACA,IAAI,OAAO,mBAAmB,EAAE;QAC9B,OAAO,kBAAkB;IAC3B;IACA,OAAO,IAAI,CAAC;IACZ,IAAI,CAAC,aAAa,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,MAAM,KAAK,WAAW,OAAO,MAAM,KAAK,MAAM,GAAG;QAC5F,MAAM,sBAAsB,GAAG,OAAO,sBAAsB,CAAC,eAAe,CAAC;QAC7E,MAAM,6BAA6B,OAAO,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC;QAChE,IAAI,gBAAgB,OAAO,uBAAuB,EAAE;YAClD,IAAI,CAAC,4BAA4B,OAAO,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;QAC3D,OAAO,IAAI,4BAA4B;YACrC,OAAO,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;QAC7B;IACF;AACF;AAEA,SAAS,iBAAiB,KAAK;IAC7B,MAAM,SAAS,IAAI;IACnB,MAAM,eAAe,EAAE;IACvB,MAAM,YAAY,OAAO,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO;IACjE,IAAI,YAAY;IAChB,IAAI;IACJ,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,aAAa,CAAC;IACvB,OAAO,IAAI,UAAU,MAAM;QACzB,OAAO,aAAa,CAAC,OAAO,MAAM,CAAC,KAAK;IAC1C;IACA,MAAM,kBAAkB,CAAA;QACtB,IAAI,WAAW;YACb,OAAO,OAAO,MAAM,CAAC,OAAO,mBAAmB,CAAC,OAAO;QACzD;QACA,OAAO,OAAO,MAAM,CAAC,MAAM;IAC7B;IACA,gCAAgC;IAChC,IAAI,OAAO,MAAM,CAAC,aAAa,KAAK,UAAU,OAAO,MAAM,CAAC,aAAa,GAAG,GAAG;QAC7E,IAAI,OAAO,MAAM,CAAC,cAAc,EAAE;YAChC,CAAC,OAAO,aAAa,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;gBACnC,aAAa,IAAI,CAAC;YACpB;QACF,OAAO;YACL,IAAK,IAAI,GAAG,IAAI,KAAK,IAAI,CAAC,OAAO,MAAM,CAAC,aAAa,GAAG,KAAK,EAAG;gBAC9D,MAAM,QAAQ,OAAO,WAAW,GAAG;gBACnC,IAAI,QAAQ,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,WAAW;gBAChD,aAAa,IAAI,CAAC,gBAAgB;YACpC;QACF;IACF,OAAO;QACL,aAAa,IAAI,CAAC,gBAAgB,OAAO,WAAW;IACtD;IAEA,6CAA6C;IAC7C,IAAK,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,KAAK,EAAG;QAC3C,IAAI,OAAO,YAAY,CAAC,EAAE,KAAK,aAAa;YAC1C,MAAM,SAAS,YAAY,CAAC,EAAE,CAAC,YAAY;YAC3C,YAAY,SAAS,YAAY,SAAS;QAC5C;IACF;IAEA,gBAAgB;IAChB,IAAI,aAAa,cAAc,GAAG,OAAO,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,UAAU,EAAE,CAAC;AACpF;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,MAAM,SAAS,OAAO,MAAM;IAC5B,2BAA2B;IAC3B,MAAM,cAAc,OAAO,SAAS,GAAG,OAAO,YAAY,KAAK,OAAO,SAAS,CAAC,UAAU,GAAG,OAAO,SAAS,CAAC,SAAS,GAAG;IAC1H,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,MAAM,CAAC,EAAE,CAAC,iBAAiB,GAAG,CAAC,OAAO,YAAY,KAAK,MAAM,CAAC,EAAE,CAAC,UAAU,GAAG,MAAM,CAAC,EAAE,CAAC,SAAS,IAAI,cAAc,OAAO,qBAAqB;IACjJ;AACF;AAEA,MAAM,uBAAuB,CAAC,SAAS,WAAW;IAChD,IAAI,aAAa,CAAC,QAAQ,SAAS,CAAC,QAAQ,CAAC,YAAY;QACvD,QAAQ,SAAS,CAAC,GAAG,CAAC;IACxB,OAAO,IAAI,CAAC,aAAa,QAAQ,SAAS,CAAC,QAAQ,CAAC,YAAY;QAC9D,QAAQ,SAAS,CAAC,MAAM,CAAC;IAC3B;AACF;AACA,SAAS,qBAAqB,SAAS;IACrC,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI;IACxC;IACA,MAAM,SAAS,IAAI;IACnB,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAM,EACJ,MAAM,EACN,cAAc,GAAG,EACjB,QAAQ,EACT,GAAG;IACJ,IAAI,OAAO,MAAM,KAAK,GAAG;IACzB,IAAI,OAAO,MAAM,CAAC,EAAE,CAAC,iBAAiB,KAAK,aAAa,OAAO,kBAAkB;IACjF,IAAI,eAAe,CAAC;IACpB,IAAI,KAAK,eAAe;IACxB,OAAO,oBAAoB,GAAG,EAAE;IAChC,OAAO,aAAa,GAAG,EAAE;IACzB,IAAI,eAAe,OAAO,YAAY;IACtC,IAAI,OAAO,iBAAiB,YAAY,aAAa,OAAO,CAAC,QAAQ,GAAG;QACtE,eAAe,WAAW,aAAa,OAAO,CAAC,KAAK,OAAO,MAAM,OAAO,IAAI;IAC9E,OAAO,IAAI,OAAO,iBAAiB,UAAU;QAC3C,eAAe,WAAW;IAC5B;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,MAAM,QAAQ,MAAM,CAAC,EAAE;QACvB,IAAI,cAAc,MAAM,iBAAiB;QACzC,IAAI,OAAO,OAAO,IAAI,OAAO,cAAc,EAAE;YAC3C,eAAe,MAAM,CAAC,EAAE,CAAC,iBAAiB;QAC5C;QACA,MAAM,gBAAgB,CAAC,eAAe,CAAC,OAAO,cAAc,GAAG,OAAO,YAAY,KAAK,CAAC,IAAI,WAAW,IAAI,CAAC,MAAM,eAAe,GAAG,YAAY;QAChJ,MAAM,wBAAwB,CAAC,eAAe,QAAQ,CAAC,EAAE,GAAG,CAAC,OAAO,cAAc,GAAG,OAAO,YAAY,KAAK,CAAC,IAAI,WAAW,IAAI,CAAC,MAAM,eAAe,GAAG,YAAY;QACtK,MAAM,cAAc,CAAC,CAAC,eAAe,WAAW;QAChD,MAAM,aAAa,cAAc,OAAO,eAAe,CAAC,EAAE;QAC1D,MAAM,iBAAiB,eAAe,KAAK,eAAe,OAAO,IAAI,GAAG,OAAO,eAAe,CAAC,EAAE;QACjG,MAAM,YAAY,eAAe,KAAK,cAAc,OAAO,IAAI,GAAG,KAAK,aAAa,KAAK,cAAc,OAAO,IAAI,IAAI,eAAe,KAAK,cAAc,OAAO,IAAI;QACnK,IAAI,WAAW;YACb,OAAO,aAAa,CAAC,IAAI,CAAC;YAC1B,OAAO,oBAAoB,CAAC,IAAI,CAAC;QACnC;QACA,qBAAqB,OAAO,WAAW,OAAO,iBAAiB;QAC/D,qBAAqB,OAAO,gBAAgB,OAAO,sBAAsB;QACzE,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB;QACxC,MAAM,gBAAgB,GAAG,MAAM,CAAC,wBAAwB;IAC1D;AACF;AAEA,SAAS,eAAe,SAAS;IAC/B,MAAM,SAAS,IAAI;IACnB,IAAI,OAAO,cAAc,aAAa;QACpC,MAAM,aAAa,OAAO,YAAY,GAAG,CAAC,IAAI;QAC9C,2BAA2B;QAC3B,YAAY,UAAU,OAAO,SAAS,IAAI,OAAO,SAAS,GAAG,cAAc;IAC7E;IACA,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAM,iBAAiB,OAAO,YAAY,KAAK,OAAO,YAAY;IAClE,IAAI,EACF,QAAQ,EACR,WAAW,EACX,KAAK,EACL,YAAY,EACb,GAAG;IACJ,MAAM,eAAe;IACrB,MAAM,SAAS;IACf,IAAI,mBAAmB,GAAG;QACxB,WAAW;QACX,cAAc;QACd,QAAQ;IACV,OAAO;QACL,WAAW,CAAC,YAAY,OAAO,YAAY,EAAE,IAAI;QACjD,MAAM,qBAAqB,KAAK,GAAG,CAAC,YAAY,OAAO,YAAY,MAAM;QACzE,MAAM,eAAe,KAAK,GAAG,CAAC,YAAY,OAAO,YAAY,MAAM;QACnE,cAAc,sBAAsB,YAAY;QAChD,QAAQ,gBAAgB,YAAY;QACpC,IAAI,oBAAoB,WAAW;QACnC,IAAI,cAAc,WAAW;IAC/B;IACA,IAAI,OAAO,IAAI,EAAE;QACf,MAAM,kBAAkB,OAAO,mBAAmB,CAAC;QACnD,MAAM,iBAAiB,OAAO,mBAAmB,CAAC,OAAO,MAAM,CAAC,MAAM,GAAG;QACzE,MAAM,sBAAsB,OAAO,UAAU,CAAC,gBAAgB;QAC9D,MAAM,qBAAqB,OAAO,UAAU,CAAC,eAAe;QAC5D,MAAM,eAAe,OAAO,UAAU,CAAC,OAAO,UAAU,CAAC,MAAM,GAAG,EAAE;QACpE,MAAM,eAAe,KAAK,GAAG,CAAC;QAC9B,IAAI,gBAAgB,qBAAqB;YACvC,eAAe,CAAC,eAAe,mBAAmB,IAAI;QACxD,OAAO;YACL,eAAe,CAAC,eAAe,eAAe,kBAAkB,IAAI;QACtE;QACA,IAAI,eAAe,GAAG,gBAAgB;IACxC;IACA,OAAO,MAAM,CAAC,QAAQ;QACpB;QACA;QACA;QACA;IACF;IACA,IAAI,OAAO,mBAAmB,IAAI,OAAO,cAAc,IAAI,OAAO,UAAU,EAAE,OAAO,oBAAoB,CAAC;IAC1G,IAAI,eAAe,CAAC,cAAc;QAChC,OAAO,IAAI,CAAC;IACd;IACA,IAAI,SAAS,CAAC,QAAQ;QACpB,OAAO,IAAI,CAAC;IACd;IACA,IAAI,gBAAgB,CAAC,eAAe,UAAU,CAAC,OAAO;QACpD,OAAO,IAAI,CAAC;IACd;IACA,OAAO,IAAI,CAAC,YAAY;AAC1B;AAEA,MAAM,qBAAqB,CAAC,SAAS,WAAW;IAC9C,IAAI,aAAa,CAAC,QAAQ,SAAS,CAAC,QAAQ,CAAC,YAAY;QACvD,QAAQ,SAAS,CAAC,GAAG,CAAC;IACxB,OAAO,IAAI,CAAC,aAAa,QAAQ,SAAS,CAAC,QAAQ,CAAC,YAAY;QAC9D,QAAQ,SAAS,CAAC,MAAM,CAAC;IAC3B;AACF;AACA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,MAAM,EACN,MAAM,EACN,QAAQ,EACR,WAAW,EACZ,GAAG;IACJ,MAAM,YAAY,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO;IAC1D,MAAM,cAAc,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG;IACrE,MAAM,mBAAmB,CAAA;QACvB,OAAO,CAAA,GAAA,0IAAA,CAAA,IAAe,AAAD,EAAE,UAAU,CAAC,CAAC,EAAE,OAAO,UAAU,GAAG,SAAS,cAAc,EAAE,UAAU,CAAC,CAAC,EAAE;IAClG;IACA,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,WAAW;QACb,IAAI,OAAO,IAAI,EAAE;YACf,IAAI,aAAa,cAAc,OAAO,OAAO,CAAC,YAAY;YAC1D,IAAI,aAAa,GAAG,aAAa,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG;YAChE,IAAI,cAAc,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM;YAC1F,cAAc,iBAAiB,CAAC,0BAA0B,EAAE,WAAW,EAAE,CAAC;QAC5E,OAAO;YACL,cAAc,iBAAiB,CAAC,0BAA0B,EAAE,YAAY,EAAE,CAAC;QAC7E;IACF,OAAO;QACL,IAAI,aAAa;YACf,cAAc,OAAO,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;YACxD,YAAY,OAAO,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK,cAAc;YACpE,YAAY,OAAO,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK,cAAc;QACtE,OAAO;YACL,cAAc,MAAM,CAAC,YAAY;QACnC;IACF;IACA,IAAI,aAAa;QACf,IAAI,CAAC,aAAa;YAChB,aAAa;YACb,YAAY,CAAA,GAAA,0IAAA,CAAA,IAAc,AAAD,EAAE,aAAa,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;YACjF,IAAI,OAAO,IAAI,IAAI,CAAC,WAAW;gBAC7B,YAAY,MAAM,CAAC,EAAE;YACvB;YAEA,aAAa;YACb,YAAY,CAAA,GAAA,0IAAA,CAAA,IAAc,AAAD,EAAE,aAAa,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;YACjF,IAAI,OAAO,IAAI,IAAI,CAAC,cAAc,GAAG;gBACnC,YAAY,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;YACvC;QACF;IACF;IACA,OAAO,OAAO,CAAC,CAAA;QACb,mBAAmB,SAAS,YAAY,aAAa,OAAO,gBAAgB;QAC5E,mBAAmB,SAAS,YAAY,WAAW,OAAO,cAAc;QACxE,mBAAmB,SAAS,YAAY,WAAW,OAAO,cAAc;IAC1E;IACA,OAAO,iBAAiB;AAC1B;AAEA,MAAM,uBAAuB,CAAC,QAAQ;IACpC,IAAI,CAAC,UAAU,OAAO,SAAS,IAAI,CAAC,OAAO,MAAM,EAAE;IACnD,MAAM,gBAAgB,IAAM,OAAO,SAAS,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,UAAU,EAAE;IAC9F,MAAM,UAAU,QAAQ,OAAO,CAAC;IAChC,IAAI,SAAS;QACX,IAAI,SAAS,QAAQ,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,kBAAkB,EAAE;QACzE,IAAI,CAAC,UAAU,OAAO,SAAS,EAAE;YAC/B,IAAI,QAAQ,UAAU,EAAE;gBACtB,SAAS,QAAQ,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,kBAAkB,EAAE;YAClF,OAAO;gBACL,aAAa;gBACb,sBAAsB;oBACpB,IAAI,QAAQ,UAAU,EAAE;wBACtB,SAAS,QAAQ,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,kBAAkB,EAAE;wBAChF,IAAI,QAAQ,OAAO,MAAM;oBAC3B;gBACF;YACF;QACF;QACA,IAAI,QAAQ,OAAO,MAAM;IAC3B;AACF;AACA,MAAM,SAAS,CAAC,QAAQ;IACtB,IAAI,CAAC,OAAO,MAAM,CAAC,MAAM,EAAE;IAC3B,MAAM,UAAU,OAAO,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;IACnD,IAAI,SAAS,QAAQ,eAAe,CAAC;AACvC;AACA,MAAM,UAAU,CAAA;IACd,IAAI,CAAC,UAAU,OAAO,SAAS,IAAI,CAAC,OAAO,MAAM,EAAE;IACnD,IAAI,SAAS,OAAO,MAAM,CAAC,mBAAmB;IAC9C,MAAM,MAAM,OAAO,MAAM,CAAC,MAAM;IAChC,IAAI,CAAC,OAAO,CAAC,UAAU,SAAS,GAAG;IACnC,SAAS,KAAK,GAAG,CAAC,QAAQ;IAC1B,MAAM,gBAAgB,OAAO,MAAM,CAAC,aAAa,KAAK,SAAS,OAAO,oBAAoB,KAAK,KAAK,IAAI,CAAC,OAAO,MAAM,CAAC,aAAa;IACpI,MAAM,cAAc,OAAO,WAAW;IACtC,IAAI,OAAO,MAAM,CAAC,IAAI,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG;QACrD,MAAM,eAAe;QACrB,MAAM,iBAAiB;YAAC,eAAe;SAAO;QAC9C,eAAe,IAAI,IAAI,MAAM,IAAI,CAAC;YAChC,QAAQ;QACV,GAAG,GAAG,CAAC,CAAC,GAAG;YACT,OAAO,eAAe,gBAAgB;QACxC;QACA,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,SAAS;YAC9B,IAAI,eAAe,QAAQ,CAAC,QAAQ,MAAM,GAAG,OAAO,QAAQ;QAC9D;QACA;IACF;IACA,MAAM,uBAAuB,cAAc,gBAAgB;IAC3D,IAAI,OAAO,MAAM,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,EAAE;QAC9C,IAAK,IAAI,IAAI,cAAc,QAAQ,KAAK,uBAAuB,QAAQ,KAAK,EAAG;YAC7E,MAAM,YAAY,CAAC,IAAI,MAAM,GAAG,IAAI;YACpC,IAAI,YAAY,eAAe,YAAY,sBAAsB,OAAO,QAAQ;QAClF;IACF,OAAO;QACL,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,cAAc,QAAQ,IAAI,KAAK,KAAK,GAAG,CAAC,uBAAuB,QAAQ,MAAM,IAAI,KAAK,EAAG;YAC7G,IAAI,MAAM,eAAe,CAAC,IAAI,wBAAwB,IAAI,WAAW,GAAG;gBACtE,OAAO,QAAQ;YACjB;QACF;IACF;AACF;AAEA,SAAS,0BAA0B,MAAM;IACvC,MAAM,EACJ,UAAU,EACV,MAAM,EACP,GAAG;IACJ,MAAM,YAAY,OAAO,YAAY,GAAG,OAAO,SAAS,GAAG,CAAC,OAAO,SAAS;IAC5E,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;QAC7C,IAAI,OAAO,UAAU,CAAC,IAAI,EAAE,KAAK,aAAa;YAC5C,IAAI,aAAa,UAAU,CAAC,EAAE,IAAI,YAAY,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI,GAAG;gBACzG,cAAc;YAChB,OAAO,IAAI,aAAa,UAAU,CAAC,EAAE,IAAI,YAAY,UAAU,CAAC,IAAI,EAAE,EAAE;gBACtE,cAAc,IAAI;YACpB;QACF,OAAO,IAAI,aAAa,UAAU,CAAC,EAAE,EAAE;YACrC,cAAc;QAChB;IACF;IACA,uBAAuB;IACvB,IAAI,OAAO,mBAAmB,EAAE;QAC9B,IAAI,cAAc,KAAK,OAAO,gBAAgB,aAAa,cAAc;IAC3E;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,cAAc;IACvC,MAAM,SAAS,IAAI;IACnB,MAAM,YAAY,OAAO,YAAY,GAAG,OAAO,SAAS,GAAG,CAAC,OAAO,SAAS;IAC5E,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,aAAa,aAAa,EAC1B,WAAW,iBAAiB,EAC5B,WAAW,iBAAiB,EAC7B,GAAG;IACJ,IAAI,cAAc;IAClB,IAAI;IACJ,MAAM,sBAAsB,CAAA;QAC1B,IAAI,YAAY,SAAS,OAAO,OAAO,CAAC,YAAY;QACpD,IAAI,YAAY,GAAG;YACjB,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG;QAC7C;QACA,IAAI,aAAa,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;YAC7C,aAAa,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM;QAC3C;QACA,OAAO;IACT;IACA,IAAI,OAAO,gBAAgB,aAAa;QACtC,cAAc,0BAA0B;IAC1C;IACA,IAAI,SAAS,OAAO,CAAC,cAAc,GAAG;QACpC,YAAY,SAAS,OAAO,CAAC;IAC/B,OAAO;QACL,MAAM,OAAO,KAAK,GAAG,CAAC,OAAO,kBAAkB,EAAE;QACjD,YAAY,OAAO,KAAK,KAAK,CAAC,CAAC,cAAc,IAAI,IAAI,OAAO,cAAc;IAC5E;IACA,IAAI,aAAa,SAAS,MAAM,EAAE,YAAY,SAAS,MAAM,GAAG;IAChE,IAAI,gBAAgB,iBAAiB,CAAC,OAAO,MAAM,CAAC,IAAI,EAAE;QACxD,IAAI,cAAc,mBAAmB;YACnC,OAAO,SAAS,GAAG;YACnB,OAAO,IAAI,CAAC;QACd;QACA;IACF;IACA,IAAI,gBAAgB,iBAAiB,OAAO,MAAM,CAAC,IAAI,IAAI,OAAO,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;QAC1G,OAAO,SAAS,GAAG,oBAAoB;QACvC;IACF;IACA,MAAM,cAAc,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG;IAErE,iBAAiB;IACjB,IAAI;IACJ,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,IAAI,EAAE;QAC3D,YAAY,oBAAoB;IAClC,OAAO,IAAI,aAAa;QACtB,MAAM,qBAAqB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;QAC5E,IAAI,mBAAmB,SAAS,mBAAmB,YAAY,CAAC,4BAA4B;QAC5F,IAAI,OAAO,KAAK,CAAC,mBAAmB;YAClC,mBAAmB,KAAK,GAAG,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC,qBAAqB;QACzE;QACA,YAAY,KAAK,KAAK,CAAC,mBAAmB,OAAO,IAAI,CAAC,IAAI;IAC5D,OAAO,IAAI,OAAO,MAAM,CAAC,YAAY,EAAE;QACrC,MAAM,aAAa,OAAO,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC;QAC3D,IAAI,YAAY;YACd,YAAY,SAAS,YAAY;QACnC,OAAO;YACL,YAAY;QACd;IACF,OAAO;QACL,YAAY;IACd;IACA,OAAO,MAAM,CAAC,QAAQ;QACpB;QACA;QACA;QACA;QACA;QACA;IACF;IACA,IAAI,OAAO,WAAW,EAAE;QACtB,QAAQ;IACV;IACA,OAAO,IAAI,CAAC;IACZ,OAAO,IAAI,CAAC;IACZ,IAAI,OAAO,WAAW,IAAI,OAAO,MAAM,CAAC,kBAAkB,EAAE;QAC1D,IAAI,sBAAsB,WAAW;YACnC,OAAO,IAAI,CAAC;QACd;QACA,OAAO,IAAI,CAAC;IACd;AACF;AAEA,SAAS,mBAAmB,EAAE,EAAE,IAAI;IAClC,MAAM,SAAS,IAAI;IACnB,MAAM,SAAS,OAAO,MAAM;IAC5B,IAAI,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,cAAc,CAAC;IAC5D,IAAI,CAAC,SAAS,OAAO,SAAS,IAAI,QAAQ,KAAK,MAAM,GAAG,KAAK,KAAK,QAAQ,CAAC,KAAK;QAC9E;eAAI,KAAK,KAAK,CAAC,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK,MAAM;SAAE,CAAC,OAAO,CAAC,CAAA;YACzD,IAAI,CAAC,SAAS,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG;gBACrF,QAAQ;YACV;QACF;IACF;IACA,IAAI,aAAa;IACjB,IAAI;IACJ,IAAI,OAAO;QACT,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,EAAE,KAAK,EAAG;YAChD,IAAI,OAAO,MAAM,CAAC,EAAE,KAAK,OAAO;gBAC9B,aAAa;gBACb,aAAa;gBACb;YACF;QACF;IACF;IACA,IAAI,SAAS,YAAY;QACvB,OAAO,YAAY,GAAG;QACtB,IAAI,OAAO,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;YACnD,OAAO,YAAY,GAAG,SAAS,MAAM,YAAY,CAAC,4BAA4B;QAChF,OAAO;YACL,OAAO,YAAY,GAAG;QACxB;IACF,OAAO;QACL,OAAO,YAAY,GAAG;QACtB,OAAO,YAAY,GAAG;QACtB;IACF;IACA,IAAI,OAAO,mBAAmB,IAAI,OAAO,YAAY,KAAK,aAAa,OAAO,YAAY,KAAK,OAAO,WAAW,EAAE;QACjH,OAAO,mBAAmB;IAC5B;AACF;AAEA,IAAI,SAAS;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF;AAEA,SAAS,mBAAmB,IAAI;IAC9B,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO,IAAI,CAAC,YAAY,KAAK,MAAM;IACrC;IACA,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,MAAM,EACN,cAAc,GAAG,EACjB,SAAS,EACT,SAAS,EACV,GAAG;IACJ,IAAI,OAAO,gBAAgB,EAAE;QAC3B,OAAO,MAAM,CAAC,YAAY;IAC5B;IACA,IAAI,OAAO,OAAO,EAAE;QAClB,OAAO;IACT;IACA,IAAI,mBAAmB,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,WAAW;IAC/C,oBAAoB,OAAO,qBAAqB;IAChD,IAAI,KAAK,mBAAmB,CAAC;IAC7B,OAAO,oBAAoB;AAC7B;AAEA,SAAS,aAAa,SAAS,EAAE,YAAY;IAC3C,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,cAAc,GAAG,EACjB,MAAM,EACN,SAAS,EACT,QAAQ,EACT,GAAG;IACJ,IAAI,IAAI;IACR,IAAI,IAAI;IACR,MAAM,IAAI;IACV,IAAI,OAAO,YAAY,IAAI;QACzB,IAAI,MAAM,CAAC,YAAY;IACzB,OAAO;QACL,IAAI;IACN;IACA,IAAI,OAAO,YAAY,EAAE;QACvB,IAAI,KAAK,KAAK,CAAC;QACf,IAAI,KAAK,KAAK,CAAC;IACjB;IACA,OAAO,iBAAiB,GAAG,OAAO,SAAS;IAC3C,OAAO,SAAS,GAAG,OAAO,YAAY,KAAK,IAAI;IAC/C,IAAI,OAAO,OAAO,EAAE;QAClB,SAAS,CAAC,OAAO,YAAY,KAAK,eAAe,YAAY,GAAG,OAAO,YAAY,KAAK,CAAC,IAAI,CAAC;IAChG,OAAO,IAAI,CAAC,OAAO,gBAAgB,EAAE;QACnC,IAAI,OAAO,YAAY,IAAI;YACzB,KAAK,OAAO,qBAAqB;QACnC,OAAO;YACL,KAAK,OAAO,qBAAqB;QACnC;QACA,UAAU,KAAK,CAAC,SAAS,GAAG,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,GAAG,CAAC;IACnE;IAEA,sCAAsC;IACtC,IAAI;IACJ,MAAM,iBAAiB,OAAO,YAAY,KAAK,OAAO,YAAY;IAClE,IAAI,mBAAmB,GAAG;QACxB,cAAc;IAChB,OAAO;QACL,cAAc,CAAC,YAAY,OAAO,YAAY,EAAE,IAAI;IACtD;IACA,IAAI,gBAAgB,UAAU;QAC5B,OAAO,cAAc,CAAC;IACxB;IACA,OAAO,IAAI,CAAC,gBAAgB,OAAO,SAAS,EAAE;AAChD;AAEA,SAAS;IACP,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC1B;AAEA,SAAS;IACP,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE;AACjD;AAEA,SAAS,YAAY,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,eAAe,EAAE,QAAQ;IAC5E,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY;IACd;IACA,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;IAC3B;IACA,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,IAAI,oBAAoB,KAAK,GAAG;QAC9B,kBAAkB;IACpB;IACA,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,MAAM,EACN,SAAS,EACV,GAAG;IACJ,IAAI,OAAO,SAAS,IAAI,OAAO,8BAA8B,EAAE;QAC7D,OAAO;IACT;IACA,MAAM,eAAe,OAAO,YAAY;IACxC,MAAM,eAAe,OAAO,YAAY;IACxC,IAAI;IACJ,IAAI,mBAAmB,YAAY,cAAc,eAAe;SAAkB,IAAI,mBAAmB,YAAY,cAAc,eAAe;SAAkB,eAAe;IAEnL,kBAAkB;IAClB,OAAO,cAAc,CAAC;IACtB,IAAI,OAAO,OAAO,EAAE;QAClB,MAAM,MAAM,OAAO,YAAY;QAC/B,IAAI,UAAU,GAAG;YACf,SAAS,CAAC,MAAM,eAAe,YAAY,GAAG,CAAC;QACjD,OAAO;YACL,IAAI,CAAC,OAAO,OAAO,CAAC,YAAY,EAAE;gBAChC,CAAA,GAAA,0IAAA,CAAA,IAAoB,AAAD,EAAE;oBACnB;oBACA,gBAAgB,CAAC;oBACjB,MAAM,MAAM,SAAS;gBACvB;gBACA,OAAO;YACT;YACA,UAAU,QAAQ,CAAC;gBACjB,CAAC,MAAM,SAAS,MAAM,EAAE,CAAC;gBACzB,UAAU;YACZ;QACF;QACA,OAAO;IACT;IACA,IAAI,UAAU,GAAG;QACf,OAAO,aAAa,CAAC;QACrB,OAAO,YAAY,CAAC;QACpB,IAAI,cAAc;YAChB,OAAO,IAAI,CAAC,yBAAyB,OAAO;YAC5C,OAAO,IAAI,CAAC;QACd;IACF,OAAO;QACL,OAAO,aAAa,CAAC;QACrB,OAAO,YAAY,CAAC;QACpB,IAAI,cAAc;YAChB,OAAO,IAAI,CAAC,yBAAyB,OAAO;YAC5C,OAAO,IAAI,CAAC;QACd;QACA,IAAI,CAAC,OAAO,SAAS,EAAE;YACrB,OAAO,SAAS,GAAG;YACnB,IAAI,CAAC,OAAO,iCAAiC,EAAE;gBAC7C,OAAO,iCAAiC,GAAG,SAAS,cAAc,CAAC;oBACjE,IAAI,CAAC,UAAU,OAAO,SAAS,EAAE;oBACjC,IAAI,EAAE,MAAM,KAAK,IAAI,EAAE;oBACvB,OAAO,SAAS,CAAC,mBAAmB,CAAC,iBAAiB,OAAO,iCAAiC;oBAC9F,OAAO,iCAAiC,GAAG;oBAC3C,OAAO,OAAO,iCAAiC;oBAC/C,OAAO,SAAS,GAAG;oBACnB,IAAI,cAAc;wBAChB,OAAO,IAAI,CAAC;oBACd;gBACF;YACF;YACA,OAAO,SAAS,CAAC,gBAAgB,CAAC,iBAAiB,OAAO,iCAAiC;QAC7F;IACF;IACA,OAAO;AACT;AAEA,IAAI,YAAY;IACd,cAAc;IACd;IACA;IACA;IACA;AACF;AAEA,SAAS,cAAc,QAAQ,EAAE,YAAY;IAC3C,MAAM,SAAS,IAAI;IACnB,IAAI,CAAC,OAAO,MAAM,CAAC,OAAO,EAAE;QAC1B,OAAO,SAAS,CAAC,KAAK,CAAC,kBAAkB,GAAG,GAAG,SAAS,EAAE,CAAC;QAC3D,OAAO,SAAS,CAAC,KAAK,CAAC,eAAe,GAAG,aAAa,IAAI,CAAC,GAAG,CAAC,GAAG;IACpE;IACA,OAAO,IAAI,CAAC,iBAAiB,UAAU;AACzC;AAEA,SAAS,eAAe,IAAI;IAC1B,IAAI,EACF,MAAM,EACN,YAAY,EACZ,SAAS,EACT,IAAI,EACL,GAAG;IACJ,MAAM,EACJ,WAAW,EACX,aAAa,EACd,GAAG;IACJ,IAAI,MAAM;IACV,IAAI,CAAC,KAAK;QACR,IAAI,cAAc,eAAe,MAAM;aAAY,IAAI,cAAc,eAAe,MAAM;aAAY,MAAM;IAC9G;IACA,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,MAAM;IAC/B,IAAI,gBAAgB,QAAQ,SAAS;QACnC,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,MAAM;IAC3C,OAAO,IAAI,gBAAgB,gBAAgB,eAAe;QACxD,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,MAAM;QAC1C,IAAI,QAAQ,QAAQ;YAClB,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,MAAM;QAC1C,OAAO;YACL,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,MAAM;QAC1C;IACF;AACF;AAEA,SAAS,gBAAgB,YAAY,EAAE,SAAS;IAC9C,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,MAAM,EACP,GAAG;IACJ,IAAI,OAAO,OAAO,EAAE;IACpB,IAAI,OAAO,UAAU,EAAE;QACrB,OAAO,gBAAgB;IACzB;IACA,eAAe;QACb;QACA;QACA;QACA,MAAM;IACR;AACF;AAEA,SAAS,cAAc,YAAY,EAAE,SAAS;IAC5C,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,MAAM,EACP,GAAG;IACJ,OAAO,SAAS,GAAG;IACnB,IAAI,OAAO,OAAO,EAAE;IACpB,OAAO,aAAa,CAAC;IACrB,eAAe;QACb;QACA;QACA;QACA,MAAM;IACR;AACF;AAEA,IAAI,aAAa;IACf;IACA;IACA;AACF;AAEA,SAAS,QAAQ,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO;IAC5D,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ;IACV;IACA,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,QAAQ,SAAS,OAAO;IAC1B;IACA,MAAM,SAAS,IAAI;IACnB,IAAI,aAAa;IACjB,IAAI,aAAa,GAAG,aAAa;IACjC,MAAM,EACJ,MAAM,EACN,QAAQ,EACR,UAAU,EACV,aAAa,EACb,WAAW,EACX,cAAc,GAAG,EACjB,SAAS,EACT,OAAO,EACR,GAAG;IACJ,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,OAAO,SAAS,IAAI,OAAO,SAAS,IAAI,OAAO,8BAA8B,EAAE;QACtH,OAAO;IACT;IACA,IAAI,OAAO,UAAU,aAAa;QAChC,QAAQ,OAAO,MAAM,CAAC,KAAK;IAC7B;IACA,MAAM,OAAO,KAAK,GAAG,CAAC,OAAO,MAAM,CAAC,kBAAkB,EAAE;IACxD,IAAI,YAAY,OAAO,KAAK,KAAK,CAAC,CAAC,aAAa,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc;IACpF,IAAI,aAAa,SAAS,MAAM,EAAE,YAAY,SAAS,MAAM,GAAG;IAChE,MAAM,YAAY,CAAC,QAAQ,CAAC,UAAU;IACtC,uBAAuB;IACvB,IAAI,OAAO,mBAAmB,EAAE;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;YAC7C,MAAM,sBAAsB,CAAC,KAAK,KAAK,CAAC,YAAY;YACpD,MAAM,iBAAiB,KAAK,KAAK,CAAC,UAAU,CAAC,EAAE,GAAG;YAClD,MAAM,qBAAqB,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG;YAC1D,IAAI,OAAO,UAAU,CAAC,IAAI,EAAE,KAAK,aAAa;gBAC5C,IAAI,uBAAuB,kBAAkB,sBAAsB,qBAAqB,CAAC,qBAAqB,cAAc,IAAI,GAAG;oBACjI,aAAa;gBACf,OAAO,IAAI,uBAAuB,kBAAkB,sBAAsB,oBAAoB;oBAC5F,aAAa,IAAI;gBACnB;YACF,OAAO,IAAI,uBAAuB,gBAAgB;gBAChD,aAAa;YACf;QACF;IACF;IACA,mBAAmB;IACnB,IAAI,OAAO,WAAW,IAAI,eAAe,aAAa;QACpD,IAAI,CAAC,OAAO,cAAc,IAAI,CAAC,MAAM,YAAY,OAAO,SAAS,IAAI,YAAY,OAAO,YAAY,KAAK,YAAY,OAAO,SAAS,IAAI,YAAY,OAAO,YAAY,EAAE,GAAG;YAC3K,OAAO;QACT;QACA,IAAI,CAAC,OAAO,cAAc,IAAI,YAAY,OAAO,SAAS,IAAI,YAAY,OAAO,YAAY,IAAI;YAC/F,IAAI,CAAC,eAAe,CAAC,MAAM,YAAY;gBACrC,OAAO;YACT;QACF;IACF;IACA,IAAI,eAAe,CAAC,iBAAiB,CAAC,KAAK,cAAc;QACvD,OAAO,IAAI,CAAC;IACd;IAEA,kBAAkB;IAClB,OAAO,cAAc,CAAC;IACtB,IAAI;IACJ,IAAI,aAAa,aAAa,YAAY;SAAY,IAAI,aAAa,aAAa,YAAY;SAAY,YAAY;IAExH,kBAAkB;IAClB,MAAM,YAAY,OAAO,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO;IACjE,MAAM,mBAAmB,aAAa;IACtC,eAAe;IACf,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,cAAc,OAAO,SAAS,IAAI,CAAC,OAAO,cAAc,OAAO,SAAS,GAAG;QAC3G,OAAO,iBAAiB,CAAC;QACzB,gBAAgB;QAChB,IAAI,OAAO,UAAU,EAAE;YACrB,OAAO,gBAAgB;QACzB;QACA,OAAO,mBAAmB;QAC1B,IAAI,OAAO,MAAM,KAAK,SAAS;YAC7B,OAAO,YAAY,CAAC;QACtB;QACA,IAAI,cAAc,SAAS;YACzB,OAAO,eAAe,CAAC,cAAc;YACrC,OAAO,aAAa,CAAC,cAAc;QACrC;QACA,OAAO;IACT;IACA,IAAI,OAAO,OAAO,EAAE;QAClB,MAAM,MAAM,OAAO,YAAY;QAC/B,MAAM,IAAI,MAAM,YAAY,CAAC;QAC7B,IAAI,UAAU,GAAG;YACf,IAAI,WAAW;gBACb,OAAO,SAAS,CAAC,KAAK,CAAC,cAAc,GAAG;gBACxC,OAAO,iBAAiB,GAAG;YAC7B;YACA,IAAI,aAAa,CAAC,OAAO,yBAAyB,IAAI,OAAO,MAAM,CAAC,YAAY,GAAG,GAAG;gBACpF,OAAO,yBAAyB,GAAG;gBACnC,sBAAsB;oBACpB,SAAS,CAAC,MAAM,eAAe,YAAY,GAAG;gBAChD;YACF,OAAO;gBACL,SAAS,CAAC,MAAM,eAAe,YAAY,GAAG;YAChD;YACA,IAAI,WAAW;gBACb,sBAAsB;oBACpB,OAAO,SAAS,CAAC,KAAK,CAAC,cAAc,GAAG;oBACxC,OAAO,iBAAiB,GAAG;gBAC7B;YACF;QACF,OAAO;YACL,IAAI,CAAC,OAAO,OAAO,CAAC,YAAY,EAAE;gBAChC,CAAA,GAAA,0IAAA,CAAA,IAAoB,AAAD,EAAE;oBACnB;oBACA,gBAAgB;oBAChB,MAAM,MAAM,SAAS;gBACvB;gBACA,OAAO;YACT;YACA,UAAU,QAAQ,CAAC;gBACjB,CAAC,MAAM,SAAS,MAAM,EAAE;gBACxB,UAAU;YACZ;QACF;QACA,OAAO;IACT;IACA,MAAM,UAAU;IAChB,MAAM,WAAW,QAAQ,QAAQ;IACjC,IAAI,aAAa,CAAC,WAAW,YAAY,OAAO,SAAS,EAAE;QACzD,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,OAAO;IACtC;IACA,OAAO,aAAa,CAAC;IACrB,OAAO,YAAY,CAAC;IACpB,OAAO,iBAAiB,CAAC;IACzB,OAAO,mBAAmB;IAC1B,OAAO,IAAI,CAAC,yBAAyB,OAAO;IAC5C,OAAO,eAAe,CAAC,cAAc;IACrC,IAAI,UAAU,GAAG;QACf,OAAO,aAAa,CAAC,cAAc;IACrC,OAAO,IAAI,CAAC,OAAO,SAAS,EAAE;QAC5B,OAAO,SAAS,GAAG;QACnB,IAAI,CAAC,OAAO,6BAA6B,EAAE;YACzC,OAAO,6BAA6B,GAAG,SAAS,cAAc,CAAC;gBAC7D,IAAI,CAAC,UAAU,OAAO,SAAS,EAAE;gBACjC,IAAI,EAAE,MAAM,KAAK,IAAI,EAAE;gBACvB,OAAO,SAAS,CAAC,mBAAmB,CAAC,iBAAiB,OAAO,6BAA6B;gBAC1F,OAAO,6BAA6B,GAAG;gBACvC,OAAO,OAAO,6BAA6B;gBAC3C,OAAO,aAAa,CAAC,cAAc;YACrC;QACF;QACA,OAAO,SAAS,CAAC,gBAAgB,CAAC,iBAAiB,OAAO,6BAA6B;IACzF;IACA,OAAO;AACT;AAEA,SAAS,YAAY,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,QAAQ;IACvD,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ;IACV;IACA,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,MAAM,gBAAgB,SAAS,OAAO;QACtC,QAAQ;IACV;IACA,MAAM,SAAS,IAAI;IACnB,IAAI,OAAO,SAAS,EAAE;IACtB,IAAI,OAAO,UAAU,aAAa;QAChC,QAAQ,OAAO,MAAM,CAAC,KAAK;IAC7B;IACA,MAAM,cAAc,OAAO,IAAI,IAAI,OAAO,MAAM,CAAC,IAAI,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG;IACnF,IAAI,WAAW;IACf,IAAI,OAAO,MAAM,CAAC,IAAI,EAAE;QACtB,IAAI,OAAO,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;YACnD,2BAA2B;YAC3B,WAAW,WAAW,OAAO,OAAO,CAAC,YAAY;QACnD,OAAO;YACL,IAAI;YACJ,IAAI,aAAa;gBACf,MAAM,aAAa,WAAW,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI;gBACrD,mBAAmB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,YAAY,CAAC,6BAA6B,MAAM,YAAY,MAAM;YAC7H,OAAO;gBACL,mBAAmB,OAAO,mBAAmB,CAAC;YAChD;YACA,MAAM,OAAO,cAAc,KAAK,IAAI,CAAC,OAAO,MAAM,CAAC,MAAM,GAAG,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,MAAM,CAAC,MAAM;YAC3G,MAAM,EACJ,cAAc,EACf,GAAG,OAAO,MAAM;YACjB,IAAI,gBAAgB,OAAO,MAAM,CAAC,aAAa;YAC/C,IAAI,kBAAkB,QAAQ;gBAC5B,gBAAgB,OAAO,oBAAoB;YAC7C,OAAO;gBACL,gBAAgB,KAAK,IAAI,CAAC,WAAW,OAAO,MAAM,CAAC,aAAa,EAAE;gBAClE,IAAI,kBAAkB,gBAAgB,MAAM,GAAG;oBAC7C,gBAAgB,gBAAgB;gBAClC;YACF;YACA,IAAI,cAAc,OAAO,mBAAmB;YAC5C,IAAI,gBAAgB;gBAClB,cAAc,eAAe,mBAAmB,KAAK,IAAI,CAAC,gBAAgB;YAC5E;YACA,IAAI,YAAY,kBAAkB,OAAO,MAAM,CAAC,aAAa,KAAK,UAAU,CAAC,aAAa;gBACxF,cAAc;YAChB;YACA,IAAI,aAAa;gBACf,MAAM,YAAY,iBAAiB,mBAAmB,OAAO,WAAW,GAAG,SAAS,SAAS,mBAAmB,OAAO,WAAW,GAAG,IAAI,OAAO,MAAM,CAAC,aAAa,GAAG,SAAS;gBAChL,OAAO,OAAO,CAAC;oBACb;oBACA,SAAS;oBACT,kBAAkB,cAAc,SAAS,mBAAmB,IAAI,mBAAmB,OAAO;oBAC1F,gBAAgB,cAAc,SAAS,OAAO,SAAS,GAAG;gBAC5D;YACF;YACA,IAAI,aAAa;gBACf,MAAM,aAAa,WAAW,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI;gBACrD,WAAW,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,YAAY,CAAC,6BAA6B,MAAM,YAAY,MAAM;YACrH,OAAO;gBACL,WAAW,OAAO,mBAAmB,CAAC;YACxC;QACF;IACF;IACA,sBAAsB;QACpB,OAAO,OAAO,CAAC,UAAU,OAAO,cAAc;IAChD;IACA,OAAO;AACT;AAEA,gCAAgC,GAChC,SAAS,UAAU,KAAK,EAAE,YAAY,EAAE,QAAQ;IAC9C,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACV,GAAG;IACJ,IAAI,CAAC,WAAW,OAAO,SAAS,EAAE,OAAO;IACzC,IAAI,OAAO,UAAU,aAAa;QAChC,QAAQ,OAAO,MAAM,CAAC,KAAK;IAC7B;IACA,IAAI,WAAW,OAAO,cAAc;IACpC,IAAI,OAAO,aAAa,KAAK,UAAU,OAAO,cAAc,KAAK,KAAK,OAAO,kBAAkB,EAAE;QAC/F,WAAW,KAAK,GAAG,CAAC,OAAO,oBAAoB,CAAC,WAAW,OAAO;IACpE;IACA,MAAM,YAAY,OAAO,WAAW,GAAG,OAAO,kBAAkB,GAAG,IAAI;IACvE,MAAM,YAAY,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO;IAC1D,IAAI,OAAO,IAAI,EAAE;QACf,IAAI,aAAa,CAAC,aAAa,OAAO,mBAAmB,EAAE,OAAO;QAClE,OAAO,OAAO,CAAC;YACb,WAAW;QACb;QACA,2BAA2B;QAC3B,OAAO,WAAW,GAAG,OAAO,SAAS,CAAC,UAAU;QAChD,IAAI,OAAO,WAAW,KAAK,OAAO,MAAM,CAAC,MAAM,GAAG,KAAK,OAAO,OAAO,EAAE;YACrE,sBAAsB;gBACpB,OAAO,OAAO,CAAC,OAAO,WAAW,GAAG,WAAW,OAAO,cAAc;YACtE;YACA,OAAO;QACT;IACF;IACA,IAAI,OAAO,MAAM,IAAI,OAAO,KAAK,EAAE;QACjC,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,cAAc;IAChD;IACA,OAAO,OAAO,OAAO,CAAC,OAAO,WAAW,GAAG,WAAW,OAAO,cAAc;AAC7E;AAEA,gCAAgC,GAChC,SAAS,UAAU,KAAK,EAAE,YAAY,EAAE,QAAQ;IAC9C,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,MAAM,EACN,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,OAAO,EACP,SAAS,EACV,GAAG;IACJ,IAAI,CAAC,WAAW,OAAO,SAAS,EAAE,OAAO;IACzC,IAAI,OAAO,UAAU,aAAa;QAChC,QAAQ,OAAO,MAAM,CAAC,KAAK;IAC7B;IACA,MAAM,YAAY,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO;IAC1D,IAAI,OAAO,IAAI,EAAE;QACf,IAAI,aAAa,CAAC,aAAa,OAAO,mBAAmB,EAAE,OAAO;QAClE,OAAO,OAAO,CAAC;YACb,WAAW;QACb;QACA,2BAA2B;QAC3B,OAAO,WAAW,GAAG,OAAO,SAAS,CAAC,UAAU;IAClD;IACA,MAAM,YAAY,eAAe,OAAO,SAAS,GAAG,CAAC,OAAO,SAAS;IACrE,SAAS,UAAU,GAAG;QACpB,IAAI,MAAM,GAAG,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;QACzC,OAAO,KAAK,KAAK,CAAC;IACpB;IACA,MAAM,sBAAsB,UAAU;IACtC,MAAM,qBAAqB,SAAS,GAAG,CAAC,CAAA,MAAO,UAAU;IACzD,MAAM,aAAa,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO;IAC7D,IAAI,WAAW,QAAQ,CAAC,mBAAmB,OAAO,CAAC,uBAAuB,EAAE;IAC5E,IAAI,OAAO,aAAa,eAAe,CAAC,OAAO,OAAO,IAAI,UAAU,GAAG;QACrE,IAAI;QACJ,SAAS,OAAO,CAAC,CAAC,MAAM;YACtB,IAAI,uBAAuB,MAAM;gBAC/B,mBAAmB;gBACnB,gBAAgB;YAClB;QACF;QACA,IAAI,OAAO,kBAAkB,aAAa;YACxC,WAAW,aAAa,QAAQ,CAAC,cAAc,GAAG,QAAQ,CAAC,gBAAgB,IAAI,gBAAgB,IAAI,cAAc;QACnH;IACF;IACA,IAAI,YAAY;IAChB,IAAI,OAAO,aAAa,aAAa;QACnC,YAAY,WAAW,OAAO,CAAC;QAC/B,IAAI,YAAY,GAAG,YAAY,OAAO,WAAW,GAAG;QACpD,IAAI,OAAO,aAAa,KAAK,UAAU,OAAO,cAAc,KAAK,KAAK,OAAO,kBAAkB,EAAE;YAC/F,YAAY,YAAY,OAAO,oBAAoB,CAAC,YAAY,QAAQ;YACxE,YAAY,KAAK,GAAG,CAAC,WAAW;QAClC;IACF;IACA,IAAI,OAAO,MAAM,IAAI,OAAO,WAAW,EAAE;QACvC,MAAM,YAAY,OAAO,MAAM,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,OAAO,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG;QACvJ,OAAO,OAAO,OAAO,CAAC,WAAW,OAAO,cAAc;IACxD,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,WAAW,KAAK,KAAK,OAAO,OAAO,EAAE;QACpE,sBAAsB;YACpB,OAAO,OAAO,CAAC,WAAW,OAAO,cAAc;QACjD;QACA,OAAO;IACT;IACA,OAAO,OAAO,OAAO,CAAC,WAAW,OAAO,cAAc;AACxD;AAEA,gCAAgC,GAChC,SAAS,WAAW,KAAK,EAAE,YAAY,EAAE,QAAQ;IAC/C,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,MAAM,SAAS,IAAI;IACnB,IAAI,OAAO,SAAS,EAAE;IACtB,IAAI,OAAO,UAAU,aAAa;QAChC,QAAQ,OAAO,MAAM,CAAC,KAAK;IAC7B;IACA,OAAO,OAAO,OAAO,CAAC,OAAO,WAAW,EAAE,OAAO,cAAc;AACjE;AAEA,gCAAgC,GAChC,SAAS,eAAe,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS;IAC9D,IAAI,iBAAiB,KAAK,GAAG;QAC3B,eAAe;IACjB;IACA,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY;IACd;IACA,MAAM,SAAS,IAAI;IACnB,IAAI,OAAO,SAAS,EAAE;IACtB,IAAI,OAAO,UAAU,aAAa;QAChC,QAAQ,OAAO,MAAM,CAAC,KAAK;IAC7B;IACA,IAAI,QAAQ,OAAO,WAAW;IAC9B,MAAM,OAAO,KAAK,GAAG,CAAC,OAAO,MAAM,CAAC,kBAAkB,EAAE;IACxD,MAAM,YAAY,OAAO,KAAK,KAAK,CAAC,CAAC,QAAQ,IAAI,IAAI,OAAO,MAAM,CAAC,cAAc;IACjF,MAAM,YAAY,OAAO,YAAY,GAAG,OAAO,SAAS,GAAG,CAAC,OAAO,SAAS;IAC5E,IAAI,aAAa,OAAO,QAAQ,CAAC,UAAU,EAAE;QAC3C,6EAA6E;QAC7E,qDAAqD;QACrD,MAAM,cAAc,OAAO,QAAQ,CAAC,UAAU;QAC9C,MAAM,WAAW,OAAO,QAAQ,CAAC,YAAY,EAAE;QAC/C,IAAI,YAAY,cAAc,CAAC,WAAW,WAAW,IAAI,WAAW;YAClE,SAAS,OAAO,MAAM,CAAC,cAAc;QACvC;IACF,OAAO;QACL,wEAAwE;QACxE,sDAAsD;QACtD,MAAM,WAAW,OAAO,QAAQ,CAAC,YAAY,EAAE;QAC/C,MAAM,cAAc,OAAO,QAAQ,CAAC,UAAU;QAC9C,IAAI,YAAY,YAAY,CAAC,cAAc,QAAQ,IAAI,WAAW;YAChE,SAAS,OAAO,MAAM,CAAC,cAAc;QACvC;IACF;IACA,QAAQ,KAAK,GAAG,CAAC,OAAO;IACxB,QAAQ,KAAK,GAAG,CAAC,OAAO,OAAO,UAAU,CAAC,MAAM,GAAG;IACnD,OAAO,OAAO,OAAO,CAAC,OAAO,OAAO,cAAc;AACpD;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,IAAI,OAAO,SAAS,EAAE;IACtB,MAAM,EACJ,MAAM,EACN,QAAQ,EACT,GAAG;IACJ,MAAM,gBAAgB,OAAO,aAAa,KAAK,SAAS,OAAO,oBAAoB,KAAK,OAAO,aAAa;IAC5G,IAAI,eAAe,OAAO,YAAY;IACtC,IAAI;IACJ,MAAM,gBAAgB,OAAO,SAAS,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,UAAU,EAAE;IACjF,IAAI,OAAO,IAAI,EAAE;QACf,IAAI,OAAO,SAAS,EAAE;QACtB,YAAY,SAAS,OAAO,YAAY,CAAC,YAAY,CAAC,4BAA4B;QAClF,IAAI,OAAO,cAAc,EAAE;YACzB,IAAI,eAAe,OAAO,YAAY,GAAG,gBAAgB,KAAK,eAAe,OAAO,MAAM,CAAC,MAAM,GAAG,OAAO,YAAY,GAAG,gBAAgB,GAAG;gBAC3I,OAAO,OAAO;gBACd,eAAe,OAAO,aAAa,CAAC,CAAA,GAAA,0IAAA,CAAA,IAAe,AAAD,EAAE,UAAU,GAAG,cAAc,0BAA0B,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE;gBAC5H,CAAA,GAAA,0IAAA,CAAA,IAAQ,AAAD,EAAE;oBACP,OAAO,OAAO,CAAC;gBACjB;YACF,OAAO;gBACL,OAAO,OAAO,CAAC;YACjB;QACF,OAAO,IAAI,eAAe,OAAO,MAAM,CAAC,MAAM,GAAG,eAAe;YAC9D,OAAO,OAAO;YACd,eAAe,OAAO,aAAa,CAAC,CAAA,GAAA,0IAAA,CAAA,IAAe,AAAD,EAAE,UAAU,GAAG,cAAc,0BAA0B,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE;YAC5H,CAAA,GAAA,0IAAA,CAAA,IAAQ,AAAD,EAAE;gBACP,OAAO,OAAO,CAAC;YACjB;QACF,OAAO;YACL,OAAO,OAAO,CAAC;QACjB;IACF,OAAO;QACL,OAAO,OAAO,CAAC;IACjB;AACF;AAEA,IAAI,QAAQ;IACV;IACA;IACA;IACA;IACA;IACA;IACA;AACF;AAEA,SAAS,WAAW,cAAc,EAAE,OAAO;IACzC,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,MAAM,EACN,QAAQ,EACT,GAAG;IACJ,IAAI,CAAC,OAAO,IAAI,IAAI,OAAO,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;IACrE,MAAM,aAAa;QACjB,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,IAAe,AAAD,EAAE,UAAU,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,cAAc,CAAC;QAC9E,OAAO,OAAO,CAAC,CAAC,IAAI;YAClB,GAAG,YAAY,CAAC,2BAA2B;QAC7C;IACF;IACA,MAAM,cAAc,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG;IACrE,MAAM,iBAAiB,OAAO,cAAc,GAAG,CAAC,cAAc,OAAO,IAAI,CAAC,IAAI,GAAG,CAAC;IAClF,MAAM,kBAAkB,OAAO,MAAM,CAAC,MAAM,GAAG,mBAAmB;IAClE,MAAM,iBAAiB,eAAe,OAAO,MAAM,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,IAAI,KAAK;IAClF,MAAM,iBAAiB,CAAA;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,KAAK,EAAG;YAC1C,MAAM,UAAU,OAAO,SAAS,GAAG,CAAA,GAAA,0IAAA,CAAA,IAAa,AAAD,EAAE,gBAAgB;gBAAC,OAAO,eAAe;aAAC,IAAI,CAAA,GAAA,0IAAA,CAAA,IAAa,AAAD,EAAE,OAAO;gBAAC,OAAO,UAAU;gBAAE,OAAO,eAAe;aAAC;YAC7J,OAAO,QAAQ,CAAC,MAAM,CAAC;QACzB;IACF;IACA,IAAI,iBAAiB;QACnB,IAAI,OAAO,kBAAkB,EAAE;YAC7B,MAAM,cAAc,iBAAiB,OAAO,MAAM,CAAC,MAAM,GAAG;YAC5D,eAAe;YACf,OAAO,YAAY;YACnB,OAAO,YAAY;QACrB,OAAO;YACL,CAAA,GAAA,0IAAA,CAAA,IAAW,AAAD,EAAE;QACd;QACA;IACF,OAAO,IAAI,gBAAgB;QACzB,IAAI,OAAO,kBAAkB,EAAE;YAC7B,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC,IAAI;YAC9E,eAAe;YACf,OAAO,YAAY;YACnB,OAAO,YAAY;QACrB,OAAO;YACL,CAAA,GAAA,0IAAA,CAAA,IAAW,AAAD,EAAE;QACd;QACA;IACF,OAAO;QACL;IACF;IACA,OAAO,OAAO,CAAC;QACb;QACA,WAAW,OAAO,cAAc,GAAG,YAAY;QAC/C;IACF;AACF;AAEA,SAAS,QAAQ,KAAK;IACpB,IAAI,EACF,cAAc,EACd,UAAU,IAAI,EACd,SAAS,EACT,YAAY,EACZ,gBAAgB,EAChB,OAAO,EACP,YAAY,EACZ,YAAY,EACb,GAAG,UAAU,KAAK,IAAI,CAAC,IAAI;IAC5B,MAAM,SAAS,IAAI;IACnB,IAAI,CAAC,OAAO,MAAM,CAAC,IAAI,EAAE;IACzB,OAAO,IAAI,CAAC;IACZ,MAAM,EACJ,MAAM,EACN,cAAc,EACd,cAAc,EACd,QAAQ,EACR,MAAM,EACP,GAAG;IACJ,MAAM,EACJ,cAAc,EACd,YAAY,EACb,GAAG;IACJ,OAAO,cAAc,GAAG;IACxB,OAAO,cAAc,GAAG;IACxB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,EAAE;QAC5C,IAAI,SAAS;YACX,IAAI,CAAC,OAAO,cAAc,IAAI,OAAO,SAAS,KAAK,GAAG;gBACpD,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,OAAO;YACzD,OAAO,IAAI,OAAO,cAAc,IAAI,OAAO,SAAS,GAAG,OAAO,aAAa,EAAE;gBAC3E,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,OAAO,SAAS,EAAE,GAAG,OAAO;YAC5E,OAAO,IAAI,OAAO,SAAS,KAAK,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC1D,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,YAAY,EAAE,GAAG,OAAO;YACxD;QACF;QACA,OAAO,cAAc,GAAG;QACxB,OAAO,cAAc,GAAG;QACxB,OAAO,IAAI,CAAC;QACZ;IACF;IACA,IAAI,gBAAgB,OAAO,aAAa;IACxC,IAAI,kBAAkB,QAAQ;QAC5B,gBAAgB,OAAO,oBAAoB;IAC7C,OAAO;QACL,gBAAgB,KAAK,IAAI,CAAC,WAAW,OAAO,aAAa,EAAE;QAC3D,IAAI,kBAAkB,gBAAgB,MAAM,GAAG;YAC7C,gBAAgB,gBAAgB;QAClC;IACF;IACA,MAAM,iBAAiB,OAAO,kBAAkB,GAAG,gBAAgB,OAAO,cAAc;IACxF,IAAI,eAAe;IACnB,IAAI,eAAe,mBAAmB,GAAG;QACvC,gBAAgB,iBAAiB,eAAe;IAClD;IACA,gBAAgB,OAAO,oBAAoB;IAC3C,OAAO,YAAY,GAAG;IACtB,MAAM,cAAc,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG;IACrE,IAAI,OAAO,MAAM,GAAG,gBAAgB,gBAAgB,OAAO,MAAM,CAAC,MAAM,KAAK,WAAW,OAAO,MAAM,GAAG,gBAAgB,eAAe,GAAG;QACxI,CAAA,GAAA,0IAAA,CAAA,IAAW,AAAD,EAAE;IACd,OAAO,IAAI,eAAe,OAAO,IAAI,CAAC,IAAI,KAAK,OAAO;QACpD,CAAA,GAAA,0IAAA,CAAA,IAAW,AAAD,EAAE;IACd;IACA,MAAM,uBAAuB,EAAE;IAC/B,MAAM,sBAAsB,EAAE;IAC9B,MAAM,OAAO,cAAc,KAAK,IAAI,CAAC,OAAO,MAAM,GAAG,OAAO,IAAI,CAAC,IAAI,IAAI,OAAO,MAAM;IACtF,MAAM,oBAAoB,WAAW,OAAO,eAAe,iBAAiB,CAAC;IAC7E,IAAI,cAAc,oBAAoB,eAAe,OAAO,WAAW;IACvE,IAAI,OAAO,qBAAqB,aAAa;QAC3C,mBAAmB,OAAO,aAAa,CAAC,OAAO,IAAI,CAAC,CAAA,KAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,gBAAgB;IACzG,OAAO;QACL,cAAc;IAChB;IACA,MAAM,SAAS,cAAc,UAAU,CAAC;IACxC,MAAM,SAAS,cAAc,UAAU,CAAC;IACxC,IAAI,kBAAkB;IACtB,IAAI,iBAAiB;IACrB,MAAM,iBAAiB,cAAc,MAAM,CAAC,iBAAiB,CAAC,MAAM,GAAG;IACvE,MAAM,0BAA0B,iBAAiB,CAAC,kBAAkB,OAAO,iBAAiB,cAAc,CAAC,gBAAgB,IAAI,MAAM,CAAC;IACtI,mCAAmC;IACnC,IAAI,0BAA0B,cAAc;QAC1C,kBAAkB,KAAK,GAAG,CAAC,eAAe,yBAAyB;QACnE,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,yBAAyB,KAAK,EAAG;YAClE,MAAM,QAAQ,IAAI,KAAK,KAAK,CAAC,IAAI,QAAQ;YACzC,IAAI,aAAa;gBACf,MAAM,oBAAoB,OAAO,QAAQ;gBACzC,IAAK,IAAI,IAAI,OAAO,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,EAAG;oBAC9C,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,mBAAmB,qBAAqB,IAAI,CAAC;gBACxE;YACA,0CAA0C;YAC1C,mFAAmF;YACnF,MAAM;YACR,OAAO;gBACL,qBAAqB,IAAI,CAAC,OAAO,QAAQ;YAC3C;QACF;IACF,OAAO,IAAI,0BAA0B,gBAAgB,OAAO,cAAc;QACxE,iBAAiB,KAAK,GAAG,CAAC,0BAA0B,CAAC,OAAO,eAAe,CAAC,GAAG;QAC/E,IAAI,mBAAmB;YACrB,iBAAiB,KAAK,GAAG,CAAC,gBAAgB,gBAAgB,OAAO,eAAe;QAClF;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,KAAK,EAAG;YAC1C,MAAM,QAAQ,IAAI,KAAK,KAAK,CAAC,IAAI,QAAQ;YACzC,IAAI,aAAa;gBACf,OAAO,OAAO,CAAC,CAAC,OAAO;oBACrB,IAAI,MAAM,MAAM,KAAK,OAAO,oBAAoB,IAAI,CAAC;gBACvD;YACF,OAAO;gBACL,oBAAoB,IAAI,CAAC;YAC3B;QACF;IACF;IACA,OAAO,mBAAmB,GAAG;IAC7B,sBAAsB;QACpB,OAAO,mBAAmB,GAAG;IAC/B;IACA,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,WAAW,OAAO,MAAM,GAAG,gBAAgB,eAAe,GAAG;QACxF,IAAI,oBAAoB,QAAQ,CAAC,mBAAmB;YAClD,oBAAoB,MAAM,CAAC,oBAAoB,OAAO,CAAC,mBAAmB;QAC5E;QACA,IAAI,qBAAqB,QAAQ,CAAC,mBAAmB;YACnD,qBAAqB,MAAM,CAAC,qBAAqB,OAAO,CAAC,mBAAmB;QAC9E;IACF;IACA,IAAI,QAAQ;QACV,qBAAqB,OAAO,CAAC,CAAA;YAC3B,MAAM,CAAC,MAAM,CAAC,iBAAiB,GAAG;YAClC,SAAS,OAAO,CAAC,MAAM,CAAC,MAAM;YAC9B,MAAM,CAAC,MAAM,CAAC,iBAAiB,GAAG;QACpC;IACF;IACA,IAAI,QAAQ;QACV,oBAAoB,OAAO,CAAC,CAAA;YAC1B,MAAM,CAAC,MAAM,CAAC,iBAAiB,GAAG;YAClC,SAAS,MAAM,CAAC,MAAM,CAAC,MAAM;YAC7B,MAAM,CAAC,MAAM,CAAC,iBAAiB,GAAG;QACpC;IACF;IACA,OAAO,YAAY;IACnB,IAAI,OAAO,aAAa,KAAK,QAAQ;QACnC,OAAO,YAAY;IACrB,OAAO,IAAI,eAAe,CAAC,qBAAqB,MAAM,GAAG,KAAK,UAAU,oBAAoB,MAAM,GAAG,KAAK,MAAM,GAAG;QACjH,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO;YAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,OAAO,OAAO,MAAM;QAC1D;IACF;IACA,IAAI,OAAO,mBAAmB,EAAE;QAC9B,OAAO,kBAAkB;IAC3B;IACA,IAAI,SAAS;QACX,IAAI,qBAAqB,MAAM,GAAG,KAAK,QAAQ;YAC7C,IAAI,OAAO,mBAAmB,aAAa;gBACzC,MAAM,wBAAwB,OAAO,UAAU,CAAC,YAAY;gBAC5D,MAAM,oBAAoB,OAAO,UAAU,CAAC,cAAc,gBAAgB;gBAC1E,MAAM,OAAO,oBAAoB;gBACjC,IAAI,cAAc;oBAChB,OAAO,YAAY,CAAC,OAAO,SAAS,GAAG;gBACzC,OAAO;oBACL,OAAO,OAAO,CAAC,cAAc,KAAK,IAAI,CAAC,kBAAkB,GAAG,OAAO;oBACnE,IAAI,cAAc;wBAChB,OAAO,eAAe,CAAC,cAAc,GAAG,OAAO,eAAe,CAAC,cAAc,GAAG;wBAChF,OAAO,eAAe,CAAC,gBAAgB,GAAG,OAAO,eAAe,CAAC,gBAAgB,GAAG;oBACtF;gBACF;YACF,OAAO;gBACL,IAAI,cAAc;oBAChB,MAAM,QAAQ,cAAc,qBAAqB,MAAM,GAAG,OAAO,IAAI,CAAC,IAAI,GAAG,qBAAqB,MAAM;oBACxG,OAAO,OAAO,CAAC,OAAO,WAAW,GAAG,OAAO,GAAG,OAAO;oBACrD,OAAO,eAAe,CAAC,gBAAgB,GAAG,OAAO,SAAS;gBAC5D;YACF;QACF,OAAO,IAAI,oBAAoB,MAAM,GAAG,KAAK,QAAQ;YACnD,IAAI,OAAO,mBAAmB,aAAa;gBACzC,MAAM,wBAAwB,OAAO,UAAU,CAAC,YAAY;gBAC5D,MAAM,oBAAoB,OAAO,UAAU,CAAC,cAAc,eAAe;gBACzE,MAAM,OAAO,oBAAoB;gBACjC,IAAI,cAAc;oBAChB,OAAO,YAAY,CAAC,OAAO,SAAS,GAAG;gBACzC,OAAO;oBACL,OAAO,OAAO,CAAC,cAAc,gBAAgB,GAAG,OAAO;oBACvD,IAAI,cAAc;wBAChB,OAAO,eAAe,CAAC,cAAc,GAAG,OAAO,eAAe,CAAC,cAAc,GAAG;wBAChF,OAAO,eAAe,CAAC,gBAAgB,GAAG,OAAO,eAAe,CAAC,gBAAgB,GAAG;oBACtF;gBACF;YACF,OAAO;gBACL,MAAM,QAAQ,cAAc,oBAAoB,MAAM,GAAG,OAAO,IAAI,CAAC,IAAI,GAAG,oBAAoB,MAAM;gBACtG,OAAO,OAAO,CAAC,OAAO,WAAW,GAAG,OAAO,GAAG,OAAO;YACvD;QACF;IACF;IACA,OAAO,cAAc,GAAG;IACxB,OAAO,cAAc,GAAG;IACxB,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,OAAO,IAAI,CAAC,cAAc;QACnE,MAAM,aAAa;YACjB;YACA;YACA;YACA;YACA,cAAc;QAChB;QACA,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,CAAC,OAAO,GAAG;YAC5C,OAAO,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;gBAChC,IAAI,CAAC,EAAE,SAAS,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC;oBAC3C,GAAG,UAAU;oBACb,SAAS,EAAE,MAAM,CAAC,aAAa,KAAK,OAAO,aAAa,GAAG,UAAU;gBACvE;YACF;QACF,OAAO,IAAI,OAAO,UAAU,CAAC,OAAO,YAAY,OAAO,WAAW,IAAI,OAAO,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;YAC3G,OAAO,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC;gBAChC,GAAG,UAAU;gBACb,SAAS,OAAO,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,KAAK,OAAO,aAAa,GAAG,UAAU;YAC/F;QACF;IACF;IACA,OAAO,IAAI,CAAC;AACd;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,MAAM,EACN,QAAQ,EACT,GAAG;IACJ,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,OAAO,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;IAClF,OAAO,YAAY;IACnB,MAAM,iBAAiB,EAAE;IACzB,OAAO,MAAM,CAAC,OAAO,CAAC,CAAA;QACpB,MAAM,QAAQ,OAAO,QAAQ,gBAAgB,KAAK,cAAc,QAAQ,YAAY,CAAC,6BAA6B,IAAI,QAAQ,gBAAgB;QAC9I,cAAc,CAAC,MAAM,GAAG;IAC1B;IACA,OAAO,MAAM,CAAC,OAAO,CAAC,CAAA;QACpB,QAAQ,eAAe,CAAC;IAC1B;IACA,eAAe,OAAO,CAAC,CAAA;QACrB,SAAS,MAAM,CAAC;IAClB;IACA,OAAO,YAAY;IACnB,OAAO,OAAO,CAAC,OAAO,SAAS,EAAE;AACnC;AAEA,IAAI,OAAO;IACT;IACA;IACA;AACF;AAEA,SAAS,cAAc,MAAM;IAC3B,MAAM,SAAS,IAAI;IACnB,IAAI,CAAC,OAAO,MAAM,CAAC,aAAa,IAAI,OAAO,MAAM,CAAC,aAAa,IAAI,OAAO,QAAQ,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE;IAC7G,MAAM,KAAK,OAAO,MAAM,CAAC,iBAAiB,KAAK,cAAc,OAAO,EAAE,GAAG,OAAO,SAAS;IACzF,IAAI,OAAO,SAAS,EAAE;QACpB,OAAO,mBAAmB,GAAG;IAC/B;IACA,GAAG,KAAK,CAAC,MAAM,GAAG;IAClB,GAAG,KAAK,CAAC,MAAM,GAAG,SAAS,aAAa;IACxC,IAAI,OAAO,SAAS,EAAE;QACpB,sBAAsB;YACpB,OAAO,mBAAmB,GAAG;QAC/B;IACF;AACF;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,IAAI,OAAO,MAAM,CAAC,aAAa,IAAI,OAAO,QAAQ,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE;QAC3E;IACF;IACA,IAAI,OAAO,SAAS,EAAE;QACpB,OAAO,mBAAmB,GAAG;IAC/B;IACA,MAAM,CAAC,OAAO,MAAM,CAAC,iBAAiB,KAAK,cAAc,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG;IAC5F,IAAI,OAAO,SAAS,EAAE;QACpB,sBAAsB;YACpB,OAAO,mBAAmB,GAAG;QAC/B;IACF;AACF;AAEA,IAAI,aAAa;IACf;IACA;AACF;AAEA,0IAA0I;AAC1I,SAAS,eAAe,QAAQ,EAAE,IAAI;IACpC,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO,IAAI;IACb;IACA,SAAS,cAAc,EAAE;QACvB,IAAI,CAAC,MAAM,OAAO,CAAA,GAAA,yJAAA,CAAA,IAAW,AAAD,OAAO,OAAO,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD,KAAK,OAAO;QAC9D,IAAI,GAAG,YAAY,EAAE,KAAK,GAAG,YAAY;QACzC,MAAM,QAAQ,GAAG,OAAO,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,GAAG,WAAW,EAAE;YAC7B,OAAO;QACT;QACA,OAAO,SAAS,cAAc,GAAG,WAAW,GAAG,IAAI;IACrD;IACA,OAAO,cAAc;AACvB;AACA,SAAS,iBAAiB,MAAM,EAAE,KAAK,EAAE,MAAM;IAC7C,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,MAAM,EACJ,MAAM,EACP,GAAG;IACJ,MAAM,qBAAqB,OAAO,kBAAkB;IACpD,MAAM,qBAAqB,OAAO,kBAAkB;IACpD,IAAI,sBAAsB,CAAC,UAAU,sBAAsB,UAAU,QAAO,UAAU,GAAG,kBAAkB,GAAG;QAC5G,IAAI,uBAAuB,WAAW;YACpC,MAAM,cAAc;YACpB,OAAO;QACT;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,aAAa,KAAK;IACzB,MAAM,SAAS,IAAI;IACnB,MAAM,YAAW,CAAA,GAAA,yJAAA,CAAA,IAAW,AAAD;IAC3B,IAAI,IAAI;IACR,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa;IACxC,MAAM,OAAO,OAAO,eAAe;IACnC,IAAI,EAAE,IAAI,KAAK,eAAe;QAC5B,IAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK,EAAE,SAAS,EAAE;YAC7D;QACF;QACA,KAAK,SAAS,GAAG,EAAE,SAAS;IAC9B,OAAO,IAAI,EAAE,IAAI,KAAK,gBAAgB,EAAE,aAAa,CAAC,MAAM,KAAK,GAAG;QAClE,KAAK,OAAO,GAAG,EAAE,aAAa,CAAC,EAAE,CAAC,UAAU;IAC9C;IACA,IAAI,EAAE,IAAI,KAAK,cAAc;QAC3B,4BAA4B;QAC5B,iBAAiB,QAAQ,GAAG,EAAE,aAAa,CAAC,EAAE,CAAC,KAAK;QACpD;IACF;IACA,MAAM,EACJ,MAAM,EACN,OAAO,EACP,OAAO,EACR,GAAG;IACJ,IAAI,CAAC,SAAS;IACd,IAAI,CAAC,OAAO,aAAa,IAAI,EAAE,WAAW,KAAK,SAAS;IACxD,IAAI,OAAO,SAAS,IAAI,OAAO,8BAA8B,EAAE;QAC7D;IACF;IACA,IAAI,CAAC,OAAO,SAAS,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;QACtD,OAAO,OAAO;IAChB;IACA,IAAI,WAAW,EAAE,MAAM;IACvB,IAAI,OAAO,iBAAiB,KAAK,WAAW;QAC1C,IAAI,CAAC,CAAA,GAAA,0IAAA,CAAA,IAAgB,AAAD,EAAE,UAAU,OAAO,SAAS,GAAG;IACrD;IACA,IAAI,WAAW,KAAK,EAAE,KAAK,KAAK,GAAG;IACnC,IAAI,YAAY,KAAK,EAAE,MAAM,GAAG,GAAG;IACnC,IAAI,KAAK,SAAS,IAAI,KAAK,OAAO,EAAE;IAEpC,6CAA6C;IAC7C,MAAM,uBAAuB,CAAC,CAAC,OAAO,cAAc,IAAI,OAAO,cAAc,KAAK;IAClF,2BAA2B;IAC3B,MAAM,YAAY,EAAE,YAAY,GAAG,EAAE,YAAY,KAAK,EAAE,IAAI;IAC5D,IAAI,wBAAwB,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,UAAU,IAAI,WAAW;QACxE,WAAW,SAAS,CAAC,EAAE;IACzB;IACA,MAAM,oBAAoB,OAAO,iBAAiB,GAAG,OAAO,iBAAiB,GAAG,CAAC,CAAC,EAAE,OAAO,cAAc,EAAE;IAC3G,MAAM,iBAAiB,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,UAAU;IAEzD,sGAAsG;IACtG,IAAI,OAAO,SAAS,IAAI,CAAC,iBAAiB,eAAe,mBAAmB,YAAY,SAAS,OAAO,CAAC,kBAAkB,GAAG;QAC5H,OAAO,UAAU,GAAG;QACpB;IACF;IACA,IAAI,OAAO,YAAY,EAAE;QACvB,IAAI,CAAC,SAAS,OAAO,CAAC,OAAO,YAAY,GAAG;IAC9C;IACA,QAAQ,QAAQ,GAAG,EAAE,KAAK;IAC1B,QAAQ,QAAQ,GAAG,EAAE,KAAK;IAC1B,MAAM,SAAS,QAAQ,QAAQ;IAC/B,MAAM,SAAS,QAAQ,QAAQ;IAE/B,gGAAgG;IAEhG,IAAI,CAAC,iBAAiB,QAAQ,GAAG,SAAS;QACxC;IACF;IACA,OAAO,MAAM,CAAC,MAAM;QAClB,WAAW;QACX,SAAS;QACT,qBAAqB;QACrB,aAAa;QACb,aAAa;IACf;IACA,QAAQ,MAAM,GAAG;IACjB,QAAQ,MAAM,GAAG;IACjB,KAAK,cAAc,GAAG,CAAA,GAAA,0IAAA,CAAA,IAAG,AAAD;IACxB,OAAO,UAAU,GAAG;IACpB,OAAO,UAAU;IACjB,OAAO,cAAc,GAAG;IACxB,IAAI,OAAO,SAAS,GAAG,GAAG,KAAK,kBAAkB,GAAG;IACpD,IAAI,iBAAiB;IACrB,IAAI,SAAS,OAAO,CAAC,KAAK,iBAAiB,GAAG;QAC5C,iBAAiB;QACjB,IAAI,SAAS,QAAQ,KAAK,UAAU;YAClC,KAAK,SAAS,GAAG;QACnB;IACF;IACA,IAAI,UAAS,aAAa,IAAI,UAAS,aAAa,CAAC,OAAO,CAAC,KAAK,iBAAiB,KAAK,UAAS,aAAa,KAAK,YAAY,CAAC,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,WAAW,CAAC,SAAS,OAAO,CAAC,KAAK,iBAAiB,CAAC,GAAG;QACpO,UAAS,aAAa,CAAC,IAAI;IAC7B;IACA,MAAM,uBAAuB,kBAAkB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IACvG,IAAI,CAAC,OAAO,6BAA6B,IAAI,oBAAoB,KAAK,CAAC,SAAS,iBAAiB,EAAE;QACjG,EAAE,cAAc;IAClB;IACA,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAO,SAAS,IAAI,CAAC,OAAO,OAAO,EAAE;QACxG,OAAO,QAAQ,CAAC,YAAY;IAC9B;IACA,OAAO,IAAI,CAAC,cAAc;AAC5B;AAEA,SAAS,YAAY,KAAK;IACxB,MAAM,YAAW,CAAA,GAAA,yJAAA,CAAA,IAAW,AAAD;IAC3B,MAAM,SAAS,IAAI;IACnB,MAAM,OAAO,OAAO,eAAe;IACnC,MAAM,EACJ,MAAM,EACN,OAAO,EACP,cAAc,GAAG,EACjB,OAAO,EACR,GAAG;IACJ,IAAI,CAAC,SAAS;IACd,IAAI,CAAC,OAAO,aAAa,IAAI,MAAM,WAAW,KAAK,SAAS;IAC5D,IAAI,IAAI;IACR,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa;IACxC,IAAI,EAAE,IAAI,KAAK,eAAe;QAC5B,IAAI,KAAK,OAAO,KAAK,MAAM,QAAQ,sCAAsC;QACzE,MAAM,KAAK,EAAE,SAAS;QACtB,IAAI,OAAO,KAAK,SAAS,EAAE;IAC7B;IACA,IAAI;IACJ,IAAI,EAAE,IAAI,KAAK,aAAa;QAC1B,cAAc;eAAI,EAAE,cAAc;SAAC,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,KAAK,OAAO;QAC3E,IAAI,CAAC,eAAe,YAAY,UAAU,KAAK,KAAK,OAAO,EAAE;IAC/D,OAAO;QACL,cAAc;IAChB;IACA,IAAI,CAAC,KAAK,SAAS,EAAE;QACnB,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,EAAE;YACxC,OAAO,IAAI,CAAC,qBAAqB;QACnC;QACA;IACF;IACA,MAAM,QAAQ,YAAY,KAAK;IAC/B,MAAM,QAAQ,YAAY,KAAK;IAC/B,IAAI,EAAE,uBAAuB,EAAE;QAC7B,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;QACjB;IACF;IACA,IAAI,CAAC,OAAO,cAAc,EAAE;QAC1B,IAAI,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,iBAAiB,GAAG;YAC7C,OAAO,UAAU,GAAG;QACtB;QACA,IAAI,KAAK,SAAS,EAAE;YAClB,OAAO,MAAM,CAAC,SAAS;gBACrB,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,KAAK,cAAc,GAAG,CAAA,GAAA,0IAAA,CAAA,IAAG,AAAD;QAC1B;QACA;IACF;IACA,IAAI,OAAO,mBAAmB,IAAI,CAAC,OAAO,IAAI,EAAE;QAC9C,IAAI,OAAO,UAAU,IAAI;YACvB,WAAW;YACX,IAAI,QAAQ,QAAQ,MAAM,IAAI,OAAO,SAAS,IAAI,OAAO,YAAY,MAAM,QAAQ,QAAQ,MAAM,IAAI,OAAO,SAAS,IAAI,OAAO,YAAY,IAAI;gBAC9I,KAAK,SAAS,GAAG;gBACjB,KAAK,OAAO,GAAG;gBACf;YACF;QACF,OAAO,IAAI,OAAO,CAAC,QAAQ,QAAQ,MAAM,IAAI,CAAC,OAAO,SAAS,IAAI,OAAO,YAAY,MAAM,QAAQ,QAAQ,MAAM,IAAI,CAAC,OAAO,SAAS,IAAI,OAAO,YAAY,EAAE,GAAG;YAChK;QACF,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,QAAQ,MAAM,IAAI,OAAO,SAAS,IAAI,OAAO,YAAY,MAAM,QAAQ,QAAQ,MAAM,IAAI,OAAO,SAAS,IAAI,OAAO,YAAY,EAAE,GAAG;YAC/J;QACF;IACF;IACA,IAAI,UAAS,aAAa,IAAI,UAAS,aAAa,CAAC,OAAO,CAAC,KAAK,iBAAiB,KAAK,UAAS,aAAa,KAAK,EAAE,MAAM,IAAI,EAAE,WAAW,KAAK,SAAS;QACxJ,UAAS,aAAa,CAAC,IAAI;IAC7B;IACA,IAAI,UAAS,aAAa,EAAE;QAC1B,IAAI,EAAE,MAAM,KAAK,UAAS,aAAa,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,iBAAiB,GAAG;YACnF,KAAK,OAAO,GAAG;YACf,OAAO,UAAU,GAAG;YACpB;QACF;IACF;IACA,IAAI,KAAK,mBAAmB,EAAE;QAC5B,OAAO,IAAI,CAAC,aAAa;IAC3B;IACA,QAAQ,SAAS,GAAG,QAAQ,QAAQ;IACpC,QAAQ,SAAS,GAAG,QAAQ,QAAQ;IACpC,QAAQ,QAAQ,GAAG;IACnB,QAAQ,QAAQ,GAAG;IACnB,MAAM,QAAQ,QAAQ,QAAQ,GAAG,QAAQ,MAAM;IAC/C,MAAM,QAAQ,QAAQ,QAAQ,GAAG,QAAQ,MAAM;IAC/C,IAAI,OAAO,MAAM,CAAC,SAAS,IAAI,KAAK,IAAI,CAAC,SAAS,IAAI,SAAS,KAAK,OAAO,MAAM,CAAC,SAAS,EAAE;IAC7F,IAAI,OAAO,KAAK,WAAW,KAAK,aAAa;QAC3C,IAAI;QACJ,IAAI,OAAO,YAAY,MAAM,QAAQ,QAAQ,KAAK,QAAQ,MAAM,IAAI,OAAO,UAAU,MAAM,QAAQ,QAAQ,KAAK,QAAQ,MAAM,EAAE;YAC9H,KAAK,WAAW,GAAG;QACrB,OAAO;YACL,2BAA2B;YAC3B,IAAI,QAAQ,QAAQ,QAAQ,SAAS,IAAI;gBACvC,aAAa,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,UAAU,MAAM,KAAK,EAAE;gBACzE,KAAK,WAAW,GAAG,OAAO,YAAY,KAAK,aAAa,OAAO,UAAU,GAAG,KAAK,aAAa,OAAO,UAAU;YACjH;QACF;IACF;IACA,IAAI,KAAK,WAAW,EAAE;QACpB,OAAO,IAAI,CAAC,qBAAqB;IACnC;IACA,IAAI,OAAO,KAAK,WAAW,KAAK,aAAa;QAC3C,IAAI,QAAQ,QAAQ,KAAK,QAAQ,MAAM,IAAI,QAAQ,QAAQ,KAAK,QAAQ,MAAM,EAAE;YAC9E,KAAK,WAAW,GAAG;QACrB;IACF;IACA,IAAI,KAAK,WAAW,IAAI,EAAE,IAAI,KAAK,eAAe,KAAK,+BAA+B,EAAE;QACtF,KAAK,SAAS,GAAG;QACjB;IACF;IACA,IAAI,CAAC,KAAK,WAAW,EAAE;QACrB;IACF;IACA,OAAO,UAAU,GAAG;IACpB,IAAI,CAAC,OAAO,OAAO,IAAI,EAAE,UAAU,EAAE;QACnC,EAAE,cAAc;IAClB;IACA,IAAI,OAAO,wBAAwB,IAAI,CAAC,OAAO,MAAM,EAAE;QACrD,EAAE,eAAe;IACnB;IACA,IAAI,OAAO,OAAO,YAAY,KAAK,QAAQ;IAC3C,IAAI,cAAc,OAAO,YAAY,KAAK,QAAQ,QAAQ,GAAG,QAAQ,SAAS,GAAG,QAAQ,QAAQ,GAAG,QAAQ,SAAS;IACrH,IAAI,OAAO,cAAc,EAAE;QACzB,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC;QACrC,cAAc,KAAK,GAAG,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,CAAC;IACrD;IACA,QAAQ,IAAI,GAAG;IACf,QAAQ,OAAO,UAAU;IACzB,IAAI,KAAK;QACP,OAAO,CAAC;QACR,cAAc,CAAC;IACjB;IACA,MAAM,uBAAuB,OAAO,gBAAgB;IACpD,OAAO,cAAc,GAAG,OAAO,IAAI,SAAS;IAC5C,OAAO,gBAAgB,GAAG,cAAc,IAAI,SAAS;IACrD,MAAM,SAAS,OAAO,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO;IACpD,MAAM,eAAe,OAAO,gBAAgB,KAAK,UAAU,OAAO,cAAc,IAAI,OAAO,gBAAgB,KAAK,UAAU,OAAO,cAAc;IAC/I,IAAI,CAAC,KAAK,OAAO,EAAE;QACjB,IAAI,UAAU,cAAc;YAC1B,OAAO,OAAO,CAAC;gBACb,WAAW,OAAO,cAAc;YAClC;QACF;QACA,KAAK,cAAc,GAAG,OAAO,YAAY;QACzC,OAAO,aAAa,CAAC;QACrB,IAAI,OAAO,SAAS,EAAE;YACpB,MAAM,MAAM,IAAI,OAAO,WAAW,CAAC,iBAAiB;gBAClD,SAAS;gBACT,YAAY;gBACZ,QAAQ;oBACN,mBAAmB;gBACrB;YACF;YACA,OAAO,SAAS,CAAC,aAAa,CAAC;QACjC;QACA,KAAK,mBAAmB,GAAG;QAC3B,cAAc;QACd,IAAI,OAAO,UAAU,IAAI,CAAC,OAAO,cAAc,KAAK,QAAQ,OAAO,cAAc,KAAK,IAAI,GAAG;YAC3F,OAAO,aAAa,CAAC;QACvB;QACA,OAAO,IAAI,CAAC,mBAAmB;IACjC;IACA,IAAI;IACJ,IAAI,OAAO,OAAO;IAClB,IAAI,OAAO,cAAc,KAAK,SAAS,KAAK,OAAO,IAAI,KAAK,kBAAkB,IAAI,yBAAyB,OAAO,gBAAgB,IAAI,UAAU,gBAAgB,KAAK,GAAG,CAAC,SAAS,GAAG;QACnL,OAAO,MAAM,CAAC,SAAS;YACrB,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,UAAU;YACV,gBAAgB,KAAK,gBAAgB;QACvC;QACA,KAAK,aAAa,GAAG;QACrB,KAAK,cAAc,GAAG,KAAK,gBAAgB;QAC3C;IACF;IACA,OAAO,IAAI,CAAC,cAAc;IAC1B,KAAK,OAAO,GAAG;IACf,KAAK,gBAAgB,GAAG,OAAO,KAAK,cAAc;IAClD,IAAI,sBAAsB;IAC1B,IAAI,kBAAkB,OAAO,eAAe;IAC5C,IAAI,OAAO,mBAAmB,EAAE;QAC9B,kBAAkB;IACpB;IACA,IAAI,OAAO,GAAG;QACZ,IAAI,UAAU,gBAAgB,CAAC,aAAa,KAAK,kBAAkB,IAAI,KAAK,gBAAgB,GAAG,CAAC,OAAO,cAAc,GAAG,OAAO,YAAY,KAAK,OAAO,eAAe,CAAC,OAAO,WAAW,GAAG,EAAE,GAAG,CAAC,OAAO,aAAa,KAAK,UAAU,OAAO,MAAM,CAAC,MAAM,GAAG,OAAO,aAAa,IAAI,IAAI,OAAO,eAAe,CAAC,OAAO,WAAW,GAAG,EAAE,GAAG,OAAO,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,OAAO,MAAM,CAAC,YAAY,GAAG,OAAO,YAAY,EAAE,GAAG;YAC9Z,OAAO,OAAO,CAAC;gBACb,WAAW;gBACX,cAAc;gBACd,kBAAkB;YACpB;QACF;QACA,IAAI,KAAK,gBAAgB,GAAG,OAAO,YAAY,IAAI;YACjD,sBAAsB;YACtB,IAAI,OAAO,UAAU,EAAE;gBACrB,KAAK,gBAAgB,GAAG,OAAO,YAAY,KAAK,IAAI,CAAC,CAAC,OAAO,YAAY,KAAK,KAAK,cAAc,GAAG,IAAI,KAAK;YAC/G;QACF;IACF,OAAO,IAAI,OAAO,GAAG;QACnB,IAAI,UAAU,gBAAgB,CAAC,aAAa,KAAK,kBAAkB,IAAI,KAAK,gBAAgB,GAAG,CAAC,OAAO,cAAc,GAAG,OAAO,YAAY,KAAK,OAAO,eAAe,CAAC,OAAO,eAAe,CAAC,MAAM,GAAG,EAAE,GAAG,OAAO,MAAM,CAAC,YAAY,GAAG,CAAC,OAAO,aAAa,KAAK,UAAU,OAAO,MAAM,CAAC,MAAM,GAAG,OAAO,aAAa,IAAI,IAAI,OAAO,eAAe,CAAC,OAAO,eAAe,CAAC,MAAM,GAAG,EAAE,GAAG,OAAO,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,OAAO,YAAY,EAAE,GAAG;YACpb,OAAO,OAAO,CAAC;gBACb,WAAW;gBACX,cAAc;gBACd,kBAAkB,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,OAAO,aAAa,KAAK,SAAS,OAAO,oBAAoB,KAAK,KAAK,IAAI,CAAC,WAAW,OAAO,aAAa,EAAE,IAAI;YAC7J;QACF;QACA,IAAI,KAAK,gBAAgB,GAAG,OAAO,YAAY,IAAI;YACjD,sBAAsB;YACtB,IAAI,OAAO,UAAU,EAAE;gBACrB,KAAK,gBAAgB,GAAG,OAAO,YAAY,KAAK,IAAI,CAAC,OAAO,YAAY,KAAK,KAAK,cAAc,GAAG,IAAI,KAAK;YAC9G;QACF;IACF;IACA,IAAI,qBAAqB;QACvB,EAAE,uBAAuB,GAAG;IAC9B;IAEA,mBAAmB;IACnB,IAAI,CAAC,OAAO,cAAc,IAAI,OAAO,cAAc,KAAK,UAAU,KAAK,gBAAgB,GAAG,KAAK,cAAc,EAAE;QAC7G,KAAK,gBAAgB,GAAG,KAAK,cAAc;IAC7C;IACA,IAAI,CAAC,OAAO,cAAc,IAAI,OAAO,cAAc,KAAK,UAAU,KAAK,gBAAgB,GAAG,KAAK,cAAc,EAAE;QAC7G,KAAK,gBAAgB,GAAG,KAAK,cAAc;IAC7C;IACA,IAAI,CAAC,OAAO,cAAc,IAAI,CAAC,OAAO,cAAc,EAAE;QACpD,KAAK,gBAAgB,GAAG,KAAK,cAAc;IAC7C;IAEA,YAAY;IACZ,IAAI,OAAO,SAAS,GAAG,GAAG;QACxB,IAAI,KAAK,GAAG,CAAC,QAAQ,OAAO,SAAS,IAAI,KAAK,kBAAkB,EAAE;YAChE,IAAI,CAAC,KAAK,kBAAkB,EAAE;gBAC5B,KAAK,kBAAkB,GAAG;gBAC1B,QAAQ,MAAM,GAAG,QAAQ,QAAQ;gBACjC,QAAQ,MAAM,GAAG,QAAQ,QAAQ;gBACjC,KAAK,gBAAgB,GAAG,KAAK,cAAc;gBAC3C,QAAQ,IAAI,GAAG,OAAO,YAAY,KAAK,QAAQ,QAAQ,GAAG,QAAQ,MAAM,GAAG,QAAQ,QAAQ,GAAG,QAAQ,MAAM;gBAC5G;YACF;QACF,OAAO;YACL,KAAK,gBAAgB,GAAG,KAAK,cAAc;YAC3C;QACF;IACF;IACA,IAAI,CAAC,OAAO,YAAY,IAAI,OAAO,OAAO,EAAE;IAE5C,mCAAmC;IACnC,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAO,mBAAmB,EAAE;QAC/F,OAAO,iBAAiB;QACxB,OAAO,mBAAmB;IAC5B;IACA,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,IAAI,OAAO,QAAQ,EAAE;QACjE,OAAO,QAAQ,CAAC,WAAW;IAC7B;IACA,kBAAkB;IAClB,OAAO,cAAc,CAAC,KAAK,gBAAgB;IAC3C,mBAAmB;IACnB,OAAO,YAAY,CAAC,KAAK,gBAAgB;AAC3C;AAEA,SAAS,WAAW,KAAK;IACvB,MAAM,SAAS,IAAI;IACnB,MAAM,OAAO,OAAO,eAAe;IACnC,IAAI,IAAI;IACR,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa;IACxC,IAAI;IACJ,MAAM,eAAe,EAAE,IAAI,KAAK,cAAc,EAAE,IAAI,KAAK;IACzD,IAAI,CAAC,cAAc;QACjB,IAAI,KAAK,OAAO,KAAK,MAAM,QAAQ,sCAAsC;QACzE,IAAI,EAAE,SAAS,KAAK,KAAK,SAAS,EAAE;QACpC,cAAc;IAChB,OAAO;QACL,cAAc;eAAI,EAAE,cAAc;SAAC,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,KAAK,OAAO;QAC3E,IAAI,CAAC,eAAe,YAAY,UAAU,KAAK,KAAK,OAAO,EAAE;IAC/D;IACA,IAAI;QAAC;QAAiB;QAAc;QAAgB;KAAc,CAAC,QAAQ,CAAC,EAAE,IAAI,GAAG;QACnF,MAAM,UAAU;YAAC;YAAiB;SAAc,CAAC,QAAQ,CAAC,EAAE,IAAI,KAAK,CAAC,OAAO,OAAO,CAAC,QAAQ,IAAI,OAAO,OAAO,CAAC,SAAS;QACzH,IAAI,CAAC,SAAS;YACZ;QACF;IACF;IACA,KAAK,SAAS,GAAG;IACjB,KAAK,OAAO,GAAG;IACf,MAAM,EACJ,MAAM,EACN,OAAO,EACP,cAAc,GAAG,EACjB,UAAU,EACV,OAAO,EACR,GAAG;IACJ,IAAI,CAAC,SAAS;IACd,IAAI,CAAC,OAAO,aAAa,IAAI,EAAE,WAAW,KAAK,SAAS;IACxD,IAAI,KAAK,mBAAmB,EAAE;QAC5B,OAAO,IAAI,CAAC,YAAY;IAC1B;IACA,KAAK,mBAAmB,GAAG;IAC3B,IAAI,CAAC,KAAK,SAAS,EAAE;QACnB,IAAI,KAAK,OAAO,IAAI,OAAO,UAAU,EAAE;YACrC,OAAO,aAAa,CAAC;QACvB;QACA,KAAK,OAAO,GAAG;QACf,KAAK,WAAW,GAAG;QACnB;IACF;IAEA,qBAAqB;IACrB,IAAI,OAAO,UAAU,IAAI,KAAK,OAAO,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,cAAc,KAAK,QAAQ,OAAO,cAAc,KAAK,IAAI,GAAG;QAC7H,OAAO,aAAa,CAAC;IACvB;IAEA,YAAY;IACZ,MAAM,eAAe,CAAA,GAAA,0IAAA,CAAA,IAAG,AAAD;IACvB,MAAM,WAAW,eAAe,KAAK,cAAc;IAEnD,wBAAwB;IACxB,IAAI,OAAO,UAAU,EAAE;QACrB,MAAM,WAAW,EAAE,IAAI,IAAI,EAAE,YAAY,IAAI,EAAE,YAAY;QAC3D,OAAO,kBAAkB,CAAC,YAAY,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;QAC/D,OAAO,IAAI,CAAC,aAAa;QACzB,IAAI,WAAW,OAAO,eAAe,KAAK,aAAa,GAAG,KAAK;YAC7D,OAAO,IAAI,CAAC,yBAAyB;QACvC;IACF;IACA,KAAK,aAAa,GAAG,CAAA,GAAA,0IAAA,CAAA,IAAG,AAAD;IACvB,CAAA,GAAA,0IAAA,CAAA,IAAQ,AAAD,EAAE;QACP,IAAI,CAAC,OAAO,SAAS,EAAE,OAAO,UAAU,GAAG;IAC7C;IACA,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,OAAO,cAAc,IAAI,QAAQ,IAAI,KAAK,KAAK,CAAC,KAAK,aAAa,IAAI,KAAK,gBAAgB,KAAK,KAAK,cAAc,IAAI,CAAC,KAAK,aAAa,EAAE;QACnL,KAAK,SAAS,GAAG;QACjB,KAAK,OAAO,GAAG;QACf,KAAK,WAAW,GAAG;QACnB;IACF;IACA,KAAK,SAAS,GAAG;IACjB,KAAK,OAAO,GAAG;IACf,KAAK,WAAW,GAAG;IACnB,IAAI;IACJ,IAAI,OAAO,YAAY,EAAE;QACvB,aAAa,MAAM,OAAO,SAAS,GAAG,CAAC,OAAO,SAAS;IACzD,OAAO;QACL,aAAa,CAAC,KAAK,gBAAgB;IACrC;IACA,IAAI,OAAO,OAAO,EAAE;QAClB;IACF;IACA,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE;QAC9C,OAAO,QAAQ,CAAC,UAAU,CAAC;YACzB;QACF;QACA;IACF;IAEA,qBAAqB;IACrB,MAAM,cAAc,cAAc,CAAC,OAAO,YAAY,MAAM,CAAC,OAAO,MAAM,CAAC,IAAI;IAC/E,IAAI,YAAY;IAChB,IAAI,YAAY,OAAO,eAAe,CAAC,EAAE;IACzC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,IAAI,OAAO,kBAAkB,GAAG,IAAI,OAAO,cAAc,CAAE;QACrG,MAAM,YAAY,IAAI,OAAO,kBAAkB,GAAG,IAAI,IAAI,OAAO,cAAc;QAC/E,IAAI,OAAO,UAAU,CAAC,IAAI,UAAU,KAAK,aAAa;YACpD,IAAI,eAAe,cAAc,UAAU,CAAC,EAAE,IAAI,aAAa,UAAU,CAAC,IAAI,UAAU,EAAE;gBACxF,YAAY;gBACZ,YAAY,UAAU,CAAC,IAAI,UAAU,GAAG,UAAU,CAAC,EAAE;YACvD;QACF,OAAO,IAAI,eAAe,cAAc,UAAU,CAAC,EAAE,EAAE;YACrD,YAAY;YACZ,YAAY,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,GAAG,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;QACnF;IACF;IACA,IAAI,mBAAmB;IACvB,IAAI,kBAAkB;IACtB,IAAI,OAAO,MAAM,EAAE;QACjB,IAAI,OAAO,WAAW,EAAE;YACtB,kBAAkB,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,OAAO,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG;QAC3I,OAAO,IAAI,OAAO,KAAK,EAAE;YACvB,mBAAmB;QACrB;IACF;IACA,0BAA0B;IAC1B,MAAM,QAAQ,CAAC,aAAa,UAAU,CAAC,UAAU,IAAI;IACrD,MAAM,YAAY,YAAY,OAAO,kBAAkB,GAAG,IAAI,IAAI,OAAO,cAAc;IACvF,IAAI,WAAW,OAAO,YAAY,EAAE;QAClC,eAAe;QACf,IAAI,CAAC,OAAO,UAAU,EAAE;YACtB,OAAO,OAAO,CAAC,OAAO,WAAW;YACjC;QACF;QACA,IAAI,OAAO,cAAc,KAAK,QAAQ;YACpC,IAAI,SAAS,OAAO,eAAe,EAAE,OAAO,OAAO,CAAC,OAAO,MAAM,IAAI,OAAO,KAAK,GAAG,mBAAmB,YAAY;iBAAgB,OAAO,OAAO,CAAC;QACpJ;QACA,IAAI,OAAO,cAAc,KAAK,QAAQ;YACpC,IAAI,QAAQ,IAAI,OAAO,eAAe,EAAE;gBACtC,OAAO,OAAO,CAAC,YAAY;YAC7B,OAAO,IAAI,oBAAoB,QAAQ,QAAQ,KAAK,KAAK,GAAG,CAAC,SAAS,OAAO,eAAe,EAAE;gBAC5F,OAAO,OAAO,CAAC;YACjB,OAAO;gBACL,OAAO,OAAO,CAAC;YACjB;QACF;IACF,OAAO;QACL,eAAe;QACf,IAAI,CAAC,OAAO,WAAW,EAAE;YACvB,OAAO,OAAO,CAAC,OAAO,WAAW;YACjC;QACF;QACA,MAAM,oBAAoB,OAAO,UAAU,IAAI,CAAC,EAAE,MAAM,KAAK,OAAO,UAAU,CAAC,MAAM,IAAI,EAAE,MAAM,KAAK,OAAO,UAAU,CAAC,MAAM;QAC9H,IAAI,CAAC,mBAAmB;YACtB,IAAI,OAAO,cAAc,KAAK,QAAQ;gBACpC,OAAO,OAAO,CAAC,qBAAqB,OAAO,mBAAmB,YAAY;YAC5E;YACA,IAAI,OAAO,cAAc,KAAK,QAAQ;gBACpC,OAAO,OAAO,CAAC,oBAAoB,OAAO,kBAAkB;YAC9D;QACF,OAAO,IAAI,EAAE,MAAM,KAAK,OAAO,UAAU,CAAC,MAAM,EAAE;YAChD,OAAO,OAAO,CAAC,YAAY;QAC7B,OAAO;YACL,OAAO,OAAO,CAAC;QACjB;IACF;AACF;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,MAAM,EACN,EAAE,EACH,GAAG;IACJ,IAAI,MAAM,GAAG,WAAW,KAAK,GAAG;IAEhC,cAAc;IACd,IAAI,OAAO,WAAW,EAAE;QACtB,OAAO,aAAa;IACtB;IAEA,aAAa;IACb,MAAM,EACJ,cAAc,EACd,cAAc,EACd,QAAQ,EACT,GAAG;IACJ,MAAM,YAAY,OAAO,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO;IAEjE,0BAA0B;IAC1B,OAAO,cAAc,GAAG;IACxB,OAAO,cAAc,GAAG;IACxB,OAAO,UAAU;IACjB,OAAO,YAAY;IACnB,OAAO,mBAAmB;IAC1B,MAAM,gBAAgB,aAAa,OAAO,IAAI;IAC9C,IAAI,CAAC,OAAO,aAAa,KAAK,UAAU,OAAO,aAAa,GAAG,CAAC,KAAK,OAAO,KAAK,IAAI,CAAC,OAAO,WAAW,IAAI,CAAC,OAAO,MAAM,CAAC,cAAc,IAAI,CAAC,eAAe;QAC3J,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO;IACrD,OAAO;QACL,IAAI,OAAO,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW;YACpC,OAAO,WAAW,CAAC,OAAO,SAAS,EAAE,GAAG,OAAO;QACjD,OAAO;YACL,OAAO,OAAO,CAAC,OAAO,WAAW,EAAE,GAAG,OAAO;QAC/C;IACF;IACA,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE;QACxE,aAAa,OAAO,QAAQ,CAAC,aAAa;QAC1C,OAAO,QAAQ,CAAC,aAAa,GAAG,WAAW;YACzC,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE;gBACxE,OAAO,QAAQ,CAAC,MAAM;YACxB;QACF,GAAG;IACL;IACA,4BAA4B;IAC5B,OAAO,cAAc,GAAG;IACxB,OAAO,cAAc,GAAG;IACxB,IAAI,OAAO,MAAM,CAAC,aAAa,IAAI,aAAa,OAAO,QAAQ,EAAE;QAC/D,OAAO,aAAa;IACtB;AACF;AAEA,SAAS,QAAQ,CAAC;IAChB,MAAM,SAAS,IAAI;IACnB,IAAI,CAAC,OAAO,OAAO,EAAE;IACrB,IAAI,CAAC,OAAO,UAAU,EAAE;QACtB,IAAI,OAAO,MAAM,CAAC,aAAa,EAAE,EAAE,cAAc;QACjD,IAAI,OAAO,MAAM,CAAC,wBAAwB,IAAI,OAAO,SAAS,EAAE;YAC9D,EAAE,eAAe;YACjB,EAAE,wBAAwB;QAC5B;IACF;AACF;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,SAAS,EACT,YAAY,EACZ,OAAO,EACR,GAAG;IACJ,IAAI,CAAC,SAAS;IACd,OAAO,iBAAiB,GAAG,OAAO,SAAS;IAC3C,IAAI,OAAO,YAAY,IAAI;QACzB,OAAO,SAAS,GAAG,CAAC,UAAU,UAAU;IAC1C,OAAO;QACL,OAAO,SAAS,GAAG,CAAC,UAAU,SAAS;IACzC;IACA,2BAA2B;IAC3B,IAAI,OAAO,SAAS,KAAK,GAAG,OAAO,SAAS,GAAG;IAC/C,OAAO,iBAAiB;IACxB,OAAO,mBAAmB;IAC1B,IAAI;IACJ,MAAM,iBAAiB,OAAO,YAAY,KAAK,OAAO,YAAY;IAClE,IAAI,mBAAmB,GAAG;QACxB,cAAc;IAChB,OAAO;QACL,cAAc,CAAC,OAAO,SAAS,GAAG,OAAO,YAAY,EAAE,IAAI;IAC7D;IACA,IAAI,gBAAgB,OAAO,QAAQ,EAAE;QACnC,OAAO,cAAc,CAAC,eAAe,CAAC,OAAO,SAAS,GAAG,OAAO,SAAS;IAC3E;IACA,OAAO,IAAI,CAAC,gBAAgB,OAAO,SAAS,EAAE;AAChD;AAEA,SAAS,OAAO,CAAC;IACf,MAAM,SAAS,IAAI;IACnB,qBAAqB,QAAQ,EAAE,MAAM;IACrC,IAAI,OAAO,MAAM,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,aAAa,KAAK,UAAU,CAAC,OAAO,MAAM,CAAC,UAAU,EAAE;QAChG;IACF;IACA,OAAO,MAAM;AACf;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,IAAI,OAAO,6BAA6B,EAAE;IAC1C,OAAO,6BAA6B,GAAG;IACvC,IAAI,OAAO,MAAM,CAAC,mBAAmB,EAAE;QACrC,OAAO,EAAE,CAAC,KAAK,CAAC,WAAW,GAAG;IAChC;AACF;AAEA,MAAM,SAAS,CAAC,QAAQ;IACtB,MAAM,YAAW,CAAA,GAAA,yJAAA,CAAA,IAAW,AAAD;IAC3B,MAAM,EACJ,MAAM,EACN,EAAE,EACF,SAAS,EACT,MAAM,EACP,GAAG;IACJ,MAAM,UAAU,CAAC,CAAC,OAAO,MAAM;IAC/B,MAAM,YAAY,WAAW,OAAO,qBAAqB;IACzD,MAAM,eAAe;IACrB,IAAI,CAAC,MAAM,OAAO,OAAO,UAAU;IAEnC,eAAe;IACf,SAAQ,CAAC,UAAU,CAAC,cAAc,OAAO,oBAAoB,EAAE;QAC7D,SAAS;QACT;IACF;IACA,EAAE,CAAC,UAAU,CAAC,cAAc,OAAO,YAAY,EAAE;QAC/C,SAAS;IACX;IACA,EAAE,CAAC,UAAU,CAAC,eAAe,OAAO,YAAY,EAAE;QAChD,SAAS;IACX;IACA,SAAQ,CAAC,UAAU,CAAC,aAAa,OAAO,WAAW,EAAE;QACnD,SAAS;QACT;IACF;IACA,SAAQ,CAAC,UAAU,CAAC,eAAe,OAAO,WAAW,EAAE;QACrD,SAAS;QACT;IACF;IACA,SAAQ,CAAC,UAAU,CAAC,YAAY,OAAO,UAAU,EAAE;QACjD,SAAS;IACX;IACA,SAAQ,CAAC,UAAU,CAAC,aAAa,OAAO,UAAU,EAAE;QAClD,SAAS;IACX;IACA,SAAQ,CAAC,UAAU,CAAC,iBAAiB,OAAO,UAAU,EAAE;QACtD,SAAS;IACX;IACA,SAAQ,CAAC,UAAU,CAAC,eAAe,OAAO,UAAU,EAAE;QACpD,SAAS;IACX;IACA,SAAQ,CAAC,UAAU,CAAC,cAAc,OAAO,UAAU,EAAE;QACnD,SAAS;IACX;IACA,SAAQ,CAAC,UAAU,CAAC,gBAAgB,OAAO,UAAU,EAAE;QACrD,SAAS;IACX;IACA,SAAQ,CAAC,UAAU,CAAC,eAAe,OAAO,UAAU,EAAE;QACpD,SAAS;IACX;IAEA,uBAAuB;IACvB,IAAI,OAAO,aAAa,IAAI,OAAO,wBAAwB,EAAE;QAC3D,EAAE,CAAC,UAAU,CAAC,SAAS,OAAO,OAAO,EAAE;IACzC;IACA,IAAI,OAAO,OAAO,EAAE;QAClB,SAAS,CAAC,UAAU,CAAC,UAAU,OAAO,QAAQ;IAChD;IAEA,iBAAiB;IACjB,IAAI,OAAO,oBAAoB,EAAE;QAC/B,MAAM,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,OAAO,OAAO,GAAG,4CAA4C,yBAAyB,UAAU;IACrI,OAAO;QACL,MAAM,CAAC,aAAa,CAAC,kBAAkB,UAAU;IACnD;IAEA,gBAAgB;IAChB,EAAE,CAAC,UAAU,CAAC,QAAQ,OAAO,MAAM,EAAE;QACnC,SAAS;IACX;AACF;AACA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,MAAM,EACP,GAAG;IACJ,OAAO,YAAY,GAAG,aAAa,IAAI,CAAC;IACxC,OAAO,WAAW,GAAG,YAAY,IAAI,CAAC;IACtC,OAAO,UAAU,GAAG,WAAW,IAAI,CAAC;IACpC,OAAO,oBAAoB,GAAG,qBAAqB,IAAI,CAAC;IACxD,IAAI,OAAO,OAAO,EAAE;QAClB,OAAO,QAAQ,GAAG,SAAS,IAAI,CAAC;IAClC;IACA,OAAO,OAAO,GAAG,QAAQ,IAAI,CAAC;IAC9B,OAAO,MAAM,GAAG,OAAO,IAAI,CAAC;IAC5B,OAAO,QAAQ;AACjB;AACA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,OAAO,QAAQ;AACjB;AACA,IAAI,WAAW;IACb;IACA;AACF;AAEA,MAAM,gBAAgB,CAAC,QAAQ;IAC7B,OAAO,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG;AAC1D;AACA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,SAAS,EACT,WAAW,EACX,MAAM,EACN,EAAE,EACH,GAAG;IACJ,MAAM,cAAc,OAAO,WAAW;IACtC,IAAI,CAAC,eAAe,eAAe,OAAO,IAAI,CAAC,aAAa,MAAM,KAAK,GAAG;IAC1E,MAAM,YAAW,CAAA,GAAA,yJAAA,CAAA,IAAW,AAAD;IAE3B,kEAAkE;IAClE,MAAM,kBAAkB,OAAO,eAAe,KAAK,YAAY,CAAC,OAAO,eAAe,GAAG,OAAO,eAAe,GAAG;IAClH,MAAM,sBAAsB;QAAC;QAAU;KAAY,CAAC,QAAQ,CAAC,OAAO,eAAe,KAAK,CAAC,OAAO,eAAe,GAAG,OAAO,EAAE,GAAG,UAAS,aAAa,CAAC,OAAO,eAAe;IAC3K,MAAM,aAAa,OAAO,aAAa,CAAC,aAAa,iBAAiB;IACtE,IAAI,CAAC,cAAc,OAAO,iBAAiB,KAAK,YAAY;IAC5D,MAAM,uBAAuB,cAAc,cAAc,WAAW,CAAC,WAAW,GAAG;IACnF,MAAM,mBAAmB,wBAAwB,OAAO,cAAc;IACtE,MAAM,cAAc,cAAc,QAAQ;IAC1C,MAAM,aAAa,cAAc,QAAQ;IACzC,MAAM,gBAAgB,OAAO,MAAM,CAAC,UAAU;IAC9C,MAAM,eAAe,iBAAiB,UAAU;IAChD,MAAM,aAAa,OAAO,OAAO;IACjC,IAAI,eAAe,CAAC,YAAY;QAC9B,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,OAAO,sBAAsB,CAAC,IAAI,CAAC,EAAE,GAAG,OAAO,sBAAsB,CAAC,WAAW,CAAC;QACzG,OAAO,oBAAoB;IAC7B,OAAO,IAAI,CAAC,eAAe,YAAY;QACrC,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,sBAAsB,CAAC,IAAI,CAAC;QACvD,IAAI,iBAAiB,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,iBAAiB,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU;YACzI,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,sBAAsB,CAAC,WAAW,CAAC;QAChE;QACA,OAAO,oBAAoB;IAC7B;IACA,IAAI,iBAAiB,CAAC,cAAc;QAClC,OAAO,eAAe;IACxB,OAAO,IAAI,CAAC,iBAAiB,cAAc;QACzC,OAAO,aAAa;IACtB;IAEA,2CAA2C;IAC3C;QAAC;QAAc;QAAc;KAAY,CAAC,OAAO,CAAC,CAAA;QAChD,IAAI,OAAO,gBAAgB,CAAC,KAAK,KAAK,aAAa;QACnD,MAAM,mBAAmB,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO;QAC7D,MAAM,kBAAkB,gBAAgB,CAAC,KAAK,IAAI,gBAAgB,CAAC,KAAK,CAAC,OAAO;QAChF,IAAI,oBAAoB,CAAC,iBAAiB;YACxC,MAAM,CAAC,KAAK,CAAC,OAAO;QACtB;QACA,IAAI,CAAC,oBAAoB,iBAAiB;YACxC,MAAM,CAAC,KAAK,CAAC,MAAM;QACrB;IACF;IACA,MAAM,mBAAmB,iBAAiB,SAAS,IAAI,iBAAiB,SAAS,KAAK,OAAO,SAAS;IACtG,MAAM,cAAc,OAAO,IAAI,IAAI,CAAC,iBAAiB,aAAa,KAAK,OAAO,aAAa,IAAI,gBAAgB;IAC/G,MAAM,UAAU,OAAO,IAAI;IAC3B,IAAI,oBAAoB,aAAa;QACnC,OAAO,eAAe;IACxB;IACA,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,OAAO,MAAM,EAAE;IACtB,MAAM,YAAY,OAAO,MAAM,CAAC,OAAO;IACvC,MAAM,UAAU,OAAO,MAAM,CAAC,IAAI;IAClC,OAAO,MAAM,CAAC,QAAQ;QACpB,gBAAgB,OAAO,MAAM,CAAC,cAAc;QAC5C,gBAAgB,OAAO,MAAM,CAAC,cAAc;QAC5C,gBAAgB,OAAO,MAAM,CAAC,cAAc;IAC9C;IACA,IAAI,cAAc,CAAC,WAAW;QAC5B,OAAO,OAAO;IAChB,OAAO,IAAI,CAAC,cAAc,WAAW;QACnC,OAAO,MAAM;IACf;IACA,OAAO,iBAAiB,GAAG;IAC3B,OAAO,IAAI,CAAC,qBAAqB;IACjC,IAAI,aAAa;QACf,IAAI,aAAa;YACf,OAAO,WAAW;YAClB,OAAO,UAAU,CAAC;YAClB,OAAO,YAAY;QACrB,OAAO,IAAI,CAAC,WAAW,SAAS;YAC9B,OAAO,UAAU,CAAC;YAClB,OAAO,YAAY;QACrB,OAAO,IAAI,WAAW,CAAC,SAAS;YAC9B,OAAO,WAAW;QACpB;IACF;IACA,OAAO,IAAI,CAAC,cAAc;AAC5B;AAEA,SAAS,cAAc,WAAW,EAAE,IAAI,EAAE,WAAW;IACnD,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO;IACT;IACA,IAAI,CAAC,eAAe,SAAS,eAAe,CAAC,aAAa,OAAO;IACjE,IAAI,aAAa;IACjB,MAAM,UAAS,CAAA,GAAA,yJAAA,CAAA,IAAS,AAAD;IACvB,MAAM,gBAAgB,SAAS,WAAW,QAAO,WAAW,GAAG,YAAY,YAAY;IACvF,MAAM,SAAS,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,CAAA;QAC1C,IAAI,OAAO,UAAU,YAAY,MAAM,OAAO,CAAC,SAAS,GAAG;YACzD,MAAM,WAAW,WAAW,MAAM,MAAM,CAAC;YACzC,MAAM,QAAQ,gBAAgB;YAC9B,OAAO;gBACL;gBACA;YACF;QACF;QACA,OAAO;YACL,OAAO;YACP;QACF;IACF;IACA,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,SAAS,EAAE,KAAK,EAAE,MAAM,SAAS,EAAE,KAAK,EAAE;IAChE,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACzC,MAAM,EACJ,KAAK,EACL,KAAK,EACN,GAAG,MAAM,CAAC,EAAE;QACb,IAAI,SAAS,UAAU;YACrB,IAAI,QAAO,UAAU,CAAC,CAAC,YAAY,EAAE,MAAM,GAAG,CAAC,EAAE,OAAO,EAAE;gBACxD,aAAa;YACf;QACF,OAAO,IAAI,SAAS,YAAY,WAAW,EAAE;YAC3C,aAAa;QACf;IACF;IACA,OAAO,cAAc;AACvB;AAEA,IAAI,cAAc;IAChB;IACA;AACF;AAEA,SAAS,eAAe,OAAO,EAAE,MAAM;IACrC,MAAM,gBAAgB,EAAE;IACxB,QAAQ,OAAO,CAAC,CAAA;QACd,IAAI,OAAO,SAAS,UAAU;YAC5B,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;gBACxB,IAAI,IAAI,CAAC,WAAW,EAAE;oBACpB,cAAc,IAAI,CAAC,SAAS;gBAC9B;YACF;QACF,OAAO,IAAI,OAAO,SAAS,UAAU;YACnC,cAAc,IAAI,CAAC,SAAS;QAC9B;IACF;IACA,OAAO;AACT;AACA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,UAAU,EACV,MAAM,EACN,GAAG,EACH,EAAE,EACF,MAAM,EACP,GAAG;IACJ,kBAAkB;IAClB,MAAM,WAAW,eAAe;QAAC;QAAe,OAAO,SAAS;QAAE;YAChE,aAAa,OAAO,MAAM,CAAC,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO;QAChE;QAAG;YACD,cAAc,OAAO,UAAU;QACjC;QAAG;YACD,OAAO;QACT;QAAG;YACD,QAAQ,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG;QAC5C;QAAG;YACD,eAAe,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,KAAK;QAC7E;QAAG;YACD,WAAW,OAAO,OAAO;QAC3B;QAAG;YACD,OAAO,OAAO,GAAG;QACnB;QAAG;YACD,YAAY,OAAO,OAAO;QAC5B;QAAG;YACD,YAAY,OAAO,OAAO,IAAI,OAAO,cAAc;QACrD;QAAG;YACD,kBAAkB,OAAO,mBAAmB;QAC9C;KAAE,EAAE,OAAO,sBAAsB;IACjC,WAAW,IAAI,IAAI;IACnB,GAAG,SAAS,CAAC,GAAG,IAAI;IACpB,OAAO,oBAAoB;AAC7B;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,EAAE,EACF,UAAU,EACX,GAAG;IACJ,IAAI,CAAC,MAAM,OAAO,OAAO,UAAU;IACnC,GAAG,SAAS,CAAC,MAAM,IAAI;IACvB,OAAO,oBAAoB;AAC7B;AAEA,IAAI,UAAU;IACZ;IACA;AACF;AAEA,SAAS;IACP,MAAM,SAAS,IAAI;IACnB,MAAM,EACJ,UAAU,SAAS,EACnB,MAAM,EACP,GAAG;IACJ,MAAM,EACJ,kBAAkB,EACnB,GAAG;IACJ,IAAI,oBAAoB;QACtB,MAAM,iBAAiB,OAAO,MAAM,CAAC,MAAM,GAAG;QAC9C,MAAM,qBAAqB,OAAO,UAAU,CAAC,eAAe,GAAG,OAAO,eAAe,CAAC,eAAe,GAAG,qBAAqB;QAC7H,OAAO,QAAQ,GAAG,OAAO,IAAI,GAAG;IAClC,OAAO;QACL,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,MAAM,KAAK;IAC/C;IACA,IAAI,OAAO,cAAc,KAAK,MAAM;QAClC,OAAO,cAAc,GAAG,CAAC,OAAO,QAAQ;IAC1C;IACA,IAAI,OAAO,cAAc,KAAK,MAAM;QAClC,OAAO,cAAc,GAAG,CAAC,OAAO,QAAQ;IAC1C;IACA,IAAI,aAAa,cAAc,OAAO,QAAQ,EAAE;QAC9C,OAAO,KAAK,GAAG;IACjB;IACA,IAAI,cAAc,OAAO,QAAQ,EAAE;QACjC,OAAO,IAAI,CAAC,OAAO,QAAQ,GAAG,SAAS;IACzC;AACF;AACA,IAAI,kBAAkB;IACpB;AACF;AAEA,IAAI,WAAW;IACb,MAAM;IACN,WAAW;IACX,gBAAgB;IAChB,uBAAuB;IACvB,mBAAmB;IACnB,cAAc;IACd,OAAO;IACP,SAAS;IACT,sBAAsB;IACtB,gBAAgB;IAChB,QAAQ;IACR,gBAAgB;IAChB,cAAc;IACd,SAAS;IACT,mBAAmB;IACnB,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,EAAE;IACF,gCAAgC;IAChC,MAAM;IACN,WAAW;IACX,KAAK;IACL,sEAAsE;IACtE,oBAAoB;IACpB,oBAAoB;IACpB,aAAa;IACb,YAAY;IACZ,oBAAoB;IACpB,gBAAgB;IAChB,oBAAoB;IACpB,kBAAkB;IAClB,UAAU;IACV,QAAQ;IACR,uDAAuD;IAEvD,cAAc;IACd,aAAa;IACb,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,oBAAoB;IACpB,oBAAoB;IACpB,gBAAgB;IAChB,sBAAsB;IACtB,oBAAoB;IACpB,QAAQ;IACR,mBAAmB;IACnB,QAAQ;IACR,qBAAqB;IACrB,0BAA0B;IAC1B,iEAAiE;IACjE,eAAe;IACf,eAAe;IACf,cAAc;IACd,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,eAAe;IACf,aAAa;IACb,YAAY;IACZ,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,gBAAgB;IAChB,WAAW;IACX,0BAA0B;IAC1B,0BAA0B;IAC1B,+BAA+B;IAC/B,qBAAqB;IACrB,6BAA6B;IAC7B,mBAAmB;IACnB,aAAa;IACb,YAAY;IACZ,iBAAiB;IACjB,WAAW;IACX,qBAAqB;IACrB,SAAS;IACT,YAAY;IACZ,SAAS;IACT,eAAe;IACf,0BAA0B;IAC1B,qBAAqB;IACrB,OAAO;IACP,MAAM;IACN,oBAAoB;IACpB,sBAAsB;IACtB,qBAAqB;IACrB,SAAS;IACT,QAAQ;IACR,qBAAqB;IACrB,gBAAgB;IAChB,gBAAgB;IAChB,cAAc;IACd,oBAAoB;IACpB,WAAW;IACX,gBAAgB;IAChB,mBAAmB;IACnB,oBAAoB;IACpB,kBAAkB;IAClB,yBAAyB;IACzB,KAAK;IACL,wBAAwB;IACxB,MAAM;IACN,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,mBAAmB;IACnB,wBAAwB;IACxB,gBAAgB;IAChB,gBAAgB;IAChB,cAAc;IACd,oBAAoB;IACpB,qBAAqB;IACrB,YAAY;IACZ,oBAAoB;IACpB,YAAY;IACZ,cAAc;AAChB;AAEA,SAAS,mBAAmB,MAAM,EAAE,gBAAgB;IAClD,OAAO,SAAS,aAAa,GAAG;QAC9B,IAAI,QAAQ,KAAK,GAAG;YAClB,MAAM,CAAC;QACT;QACA,MAAM,kBAAkB,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;QAC3C,MAAM,eAAe,GAAG,CAAC,gBAAgB;QACzC,IAAI,OAAO,iBAAiB,YAAY,iBAAiB,MAAM;YAC7D,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,kBAAkB;YACzB;QACF;QACA,IAAI,MAAM,CAAC,gBAAgB,KAAK,MAAM;YACpC,MAAM,CAAC,gBAAgB,GAAG;gBACxB,SAAS;YACX;QACF;QACA,IAAI,oBAAoB,gBAAgB,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE;YACxK,MAAM,CAAC,gBAAgB,CAAC,IAAI,GAAG;QACjC;QACA,IAAI;YAAC;YAAc;SAAY,CAAC,OAAO,CAAC,oBAAoB,KAAK,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE,EAAE;YAC1J,MAAM,CAAC,gBAAgB,CAAC,IAAI,GAAG;QACjC;QACA,IAAI,CAAC,CAAC,mBAAmB,UAAU,aAAa,YAAY,GAAG;YAC7D,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,kBAAkB;YACzB;QACF;QACA,IAAI,OAAO,MAAM,CAAC,gBAAgB,KAAK,YAAY,CAAC,CAAC,aAAa,MAAM,CAAC,gBAAgB,GAAG;YAC1F,MAAM,CAAC,gBAAgB,CAAC,OAAO,GAAG;QACpC;QACA,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,GAAG;YACtD,SAAS;QACX;QACA,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,kBAAkB;IAC3B;AACF;AAEA,mCAAmC,GACnC,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;IACA,eAAe;IACf;AACF;AACA,MAAM,mBAAmB,CAAC;AAC1B,MAAM;IACJ,aAAc;QACZ,IAAI;QACJ,IAAI;QACJ,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC9B;QACA,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC,EAAE,CAAC,WAAW,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,OAAO,UAAU;YACjH,SAAS,IAAI,CAAC,EAAE;QAClB,OAAO;YACL,CAAC,IAAI,OAAO,GAAG;QACjB;QACA,IAAI,CAAC,QAAQ,SAAS,CAAC;QACvB,SAAS,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,CAAC,GAAG;QACpB,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG;QAClC,MAAM,YAAW,CAAA,GAAA,yJAAA,CAAA,IAAW,AAAD;QAC3B,IAAI,OAAO,EAAE,IAAI,OAAO,OAAO,EAAE,KAAK,YAAY,UAAS,gBAAgB,CAAC,OAAO,EAAE,EAAE,MAAM,GAAG,GAAG;YACjG,MAAM,UAAU,EAAE;YAClB,UAAS,gBAAgB,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAA;gBAC3C,MAAM,YAAY,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,CAAC,GAAG,QAAQ;oBACnC,IAAI;gBACN;gBACA,QAAQ,IAAI,CAAC,IAAI,OAAO;YAC1B;YACA,iDAAiD;YACjD,OAAO;QACT;QAEA,kBAAkB;QAClB,MAAM,SAAS,IAAI;QACnB,OAAO,UAAU,GAAG;QACpB,OAAO,OAAO,GAAG;QACjB,OAAO,MAAM,GAAG,UAAU;YACxB,WAAW,OAAO,SAAS;QAC7B;QACA,OAAO,OAAO,GAAG;QACjB,OAAO,eAAe,GAAG,CAAC;QAC1B,OAAO,kBAAkB,GAAG,EAAE;QAC9B,OAAO,OAAO,GAAG;eAAI,OAAO,WAAW;SAAC;QACxC,IAAI,OAAO,OAAO,IAAI,MAAM,OAAO,CAAC,OAAO,OAAO,GAAG;YACnD,OAAO,OAAO,CAAC,IAAI,IAAI,OAAO,OAAO;QACvC;QACA,MAAM,mBAAmB,CAAC;QAC1B,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA;YACrB,IAAI;gBACF;gBACA;gBACA,cAAc,mBAAmB,QAAQ;gBACzC,IAAI,OAAO,EAAE,CAAC,IAAI,CAAC;gBACnB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;gBACvB,KAAK,OAAO,GAAG,CAAC,IAAI,CAAC;gBACrB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;YACzB;QACF;QAEA,sCAAsC;QACtC,MAAM,eAAe,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,CAAC,GAAG,UAAU;QAE1C,qCAAqC;QACrC,OAAO,MAAM,GAAG,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,CAAC,GAAG,cAAc,kBAAkB;QAC3D,OAAO,cAAc,GAAG,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,CAAC,GAAG,OAAO,MAAM;QAChD,OAAO,YAAY,GAAG,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,CAAC,GAAG;QAEjC,sBAAsB;QACtB,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,EAAE,EAAE;YACrC,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;gBACpC,OAAO,EAAE,CAAC,WAAW,OAAO,MAAM,CAAC,EAAE,CAAC,UAAU;YAClD;QACF;QACA,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,KAAK,EAAE;YACxC,OAAO,KAAK,CAAC,OAAO,MAAM,CAAC,KAAK;QAClC;QAEA,gBAAgB;QAChB,OAAO,MAAM,CAAC,QAAQ;YACpB,SAAS,OAAO,MAAM,CAAC,OAAO;YAC9B;YACA,UAAU;YACV,YAAY,EAAE;YACd,SAAS;YACT,QAAQ,EAAE;YACV,YAAY,EAAE;YACd,UAAU,EAAE;YACZ,iBAAiB,EAAE;YACnB,cAAc;YACd;gBACE,OAAO,OAAO,MAAM,CAAC,SAAS,KAAK;YACrC;YACA;gBACE,OAAO,OAAO,MAAM,CAAC,SAAS,KAAK;YACrC;YACA,UAAU;YACV,aAAa;YACb,WAAW;YACX,EAAE;YACF,aAAa;YACb,OAAO;YACP,QAAQ;YACR,WAAW;YACX,mBAAmB;YACnB,UAAU;YACV,UAAU;YACV,WAAW;YACX;gBACE,0CAA0C;gBAC1C,2DAA2D;gBAC3D,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK,MAAM,KAAK;YACrD;YACA,QAAQ;YACR,gBAAgB,OAAO,MAAM,CAAC,cAAc;YAC5C,gBAAgB,OAAO,MAAM,CAAC,cAAc;YAC5C,eAAe;YACf,iBAAiB;gBACf,WAAW;gBACX,SAAS;gBACT,qBAAqB;gBACrB,gBAAgB;gBAChB,aAAa;gBACb,kBAAkB;gBAClB,gBAAgB;gBAChB,oBAAoB;gBACpB,yBAAyB;gBACzB,mBAAmB,OAAO,MAAM,CAAC,iBAAiB;gBAClD,kBAAkB;gBAClB,eAAe;gBACf,cAAc;gBACd,aAAa;gBACb,YAAY,EAAE;gBACd,qBAAqB;gBACrB,aAAa;gBACb,WAAW;gBACX,SAAS;YACX;YACA,SAAS;YACT,YAAY;YACZ,UAAU;YACV,gBAAgB,OAAO,MAAM,CAAC,cAAc;YAC5C,SAAS;gBACP,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,MAAM;YACR;YACA,SAAS;YACT,cAAc,EAAE;YAChB,cAAc;QAChB;QACA,OAAO,IAAI,CAAC;QAEZ,OAAO;QACP,IAAI,OAAO,MAAM,CAAC,IAAI,EAAE;YACtB,OAAO,IAAI;QACb;QAEA,sBAAsB;QACtB,iDAAiD;QACjD,OAAO;IACT;IACA,kBAAkB,QAAQ,EAAE;QAC1B,IAAI,IAAI,CAAC,YAAY,IAAI;YACvB,OAAO;QACT;QACA,kBAAkB;QAClB,OAAO,CAAA;YACL,SAAS;YACT,cAAc;YACd,kBAAkB;YAClB,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAChB,iBAAiB;YACjB,eAAe;QACjB,CAAA,CAAC,CAAC,SAAS;IACb;IACA,cAAc,OAAO,EAAE;QACrB,MAAM,EACJ,QAAQ,EACR,MAAM,EACP,GAAG,IAAI;QACR,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,IAAe,AAAD,EAAE,UAAU,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,cAAc,CAAC;QAC9E,MAAM,kBAAkB,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,MAAM,CAAC,EAAE;QAC9C,OAAO,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,WAAW;IACjC;IACA,oBAAoB,KAAK,EAAE;QACzB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,YAAY,CAAC,6BAA6B,MAAM;IAChH;IACA,eAAe;QACb,MAAM,SAAS,IAAI;QACnB,MAAM,EACJ,QAAQ,EACR,MAAM,EACP,GAAG;QACJ,OAAO,MAAM,GAAG,CAAA,GAAA,0IAAA,CAAA,IAAe,AAAD,EAAE,UAAU,CAAC,CAAC,EAAE,OAAO,UAAU,CAAC,cAAc,CAAC;IACjF;IACA,SAAS;QACP,MAAM,SAAS,IAAI;QACnB,IAAI,OAAO,OAAO,EAAE;QACpB,OAAO,OAAO,GAAG;QACjB,IAAI,OAAO,MAAM,CAAC,UAAU,EAAE;YAC5B,OAAO,aAAa;QACtB;QACA,OAAO,IAAI,CAAC;IACd;IACA,UAAU;QACR,MAAM,SAAS,IAAI;QACnB,IAAI,CAAC,OAAO,OAAO,EAAE;QACrB,OAAO,OAAO,GAAG;QACjB,IAAI,OAAO,MAAM,CAAC,UAAU,EAAE;YAC5B,OAAO,eAAe;QACxB;QACA,OAAO,IAAI,CAAC;IACd;IACA,YAAY,QAAQ,EAAE,KAAK,EAAE;QAC3B,MAAM,SAAS,IAAI;QACnB,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,IAAI;QAC3C,MAAM,MAAM,OAAO,YAAY;QAC/B,MAAM,MAAM,OAAO,YAAY;QAC/B,MAAM,UAAU,CAAC,MAAM,GAAG,IAAI,WAAW;QACzC,OAAO,WAAW,CAAC,SAAS,OAAO,UAAU,cAAc,IAAI;QAC/D,OAAO,iBAAiB;QACxB,OAAO,mBAAmB;IAC5B;IACA,uBAAuB;QACrB,MAAM,SAAS,IAAI;QACnB,IAAI,CAAC,OAAO,MAAM,CAAC,YAAY,IAAI,CAAC,OAAO,EAAE,EAAE;QAC/C,MAAM,MAAM,OAAO,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA;YAChD,OAAO,UAAU,OAAO,CAAC,cAAc,KAAK,UAAU,OAAO,CAAC,OAAO,MAAM,CAAC,sBAAsB,MAAM;QAC1G;QACA,OAAO,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC;IAC5C;IACA,gBAAgB,OAAO,EAAE;QACvB,MAAM,SAAS,IAAI;QACnB,IAAI,OAAO,SAAS,EAAE,OAAO;QAC7B,OAAO,QAAQ,SAAS,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA;YACzC,OAAO,UAAU,OAAO,CAAC,oBAAoB,KAAK,UAAU,OAAO,CAAC,OAAO,MAAM,CAAC,UAAU,MAAM;QACpG,GAAG,IAAI,CAAC;IACV;IACA,oBAAoB;QAClB,MAAM,SAAS,IAAI;QACnB,IAAI,CAAC,OAAO,MAAM,CAAC,YAAY,IAAI,CAAC,OAAO,EAAE,EAAE;QAC/C,MAAM,UAAU,EAAE;QAClB,OAAO,MAAM,CAAC,OAAO,CAAC,CAAA;YACpB,MAAM,aAAa,OAAO,eAAe,CAAC;YAC1C,QAAQ,IAAI,CAAC;gBACX;gBACA;YACF;YACA,OAAO,IAAI,CAAC,eAAe,SAAS;QACtC;QACA,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IACA,qBAAqB,IAAI,EAAE,KAAK,EAAE;QAChC,IAAI,SAAS,KAAK,GAAG;YACnB,OAAO;QACT;QACA,IAAI,UAAU,KAAK,GAAG;YACpB,QAAQ;QACV;QACA,MAAM,SAAS,IAAI;QACnB,MAAM,EACJ,MAAM,EACN,MAAM,EACN,UAAU,EACV,eAAe,EACf,MAAM,UAAU,EAChB,WAAW,EACZ,GAAG;QACJ,IAAI,MAAM;QACV,IAAI,OAAO,OAAO,aAAa,KAAK,UAAU,OAAO,OAAO,aAAa;QACzE,IAAI,OAAO,cAAc,EAAE;YACzB,IAAI,YAAY,MAAM,CAAC,YAAY,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,eAAe,IAAI;YACvF,IAAI;YACJ,IAAK,IAAI,IAAI,cAAc,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;gBACvD,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,WAAW;oBAC3B,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe;oBAChD,OAAO;oBACP,IAAI,YAAY,YAAY,YAAY;gBAC1C;YACF;YACA,IAAK,IAAI,IAAI,cAAc,GAAG,KAAK,GAAG,KAAK,EAAG;gBAC5C,IAAI,MAAM,CAAC,EAAE,IAAI,CAAC,WAAW;oBAC3B,aAAa,MAAM,CAAC,EAAE,CAAC,eAAe;oBACtC,OAAO;oBACP,IAAI,YAAY,YAAY,YAAY;gBAC1C;YACF;QACF,OAAO;YACL,2BAA2B;YAC3B,IAAI,SAAS,WAAW;gBACtB,IAAK,IAAI,IAAI,cAAc,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;oBACvD,MAAM,cAAc,QAAQ,UAAU,CAAC,EAAE,GAAG,eAAe,CAAC,EAAE,GAAG,UAAU,CAAC,YAAY,GAAG,aAAa,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,YAAY,GAAG;oBAClJ,IAAI,aAAa;wBACf,OAAO;oBACT;gBACF;YACF,OAAO;gBACL,WAAW;gBACX,IAAK,IAAI,IAAI,cAAc,GAAG,KAAK,GAAG,KAAK,EAAG;oBAC5C,MAAM,cAAc,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,EAAE,GAAG;oBAC9D,IAAI,aAAa;wBACf,OAAO;oBACT;gBACF;YACF;QACF;QACA,OAAO;IACT;IACA,SAAS;QACP,MAAM,SAAS,IAAI;QACnB,IAAI,CAAC,UAAU,OAAO,SAAS,EAAE;QACjC,MAAM,EACJ,QAAQ,EACR,MAAM,EACP,GAAG;QACJ,cAAc;QACd,IAAI,OAAO,WAAW,EAAE;YACtB,OAAO,aAAa;QACtB;QACA;eAAI,OAAO,EAAE,CAAC,gBAAgB,CAAC;SAAoB,CAAC,OAAO,CAAC,CAAA;YAC1D,IAAI,QAAQ,QAAQ,EAAE;gBACpB,qBAAqB,QAAQ;YAC/B;QACF;QACA,OAAO,UAAU;QACjB,OAAO,YAAY;QACnB,OAAO,cAAc;QACrB,OAAO,mBAAmB;QAC1B,SAAS;YACP,MAAM,iBAAiB,OAAO,YAAY,GAAG,OAAO,SAAS,GAAG,CAAC,IAAI,OAAO,SAAS;YACrF,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,gBAAgB,OAAO,YAAY,KAAK,OAAO,YAAY;YAClG,OAAO,YAAY,CAAC;YACpB,OAAO,iBAAiB;YACxB,OAAO,mBAAmB;QAC5B;QACA,IAAI;QACJ,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,IAAI,CAAC,OAAO,OAAO,EAAE;YACjE;YACA,IAAI,OAAO,UAAU,EAAE;gBACrB,OAAO,gBAAgB;YACzB;QACF,OAAO;YACL,IAAI,CAAC,OAAO,aAAa,KAAK,UAAU,OAAO,aAAa,GAAG,CAAC,KAAK,OAAO,KAAK,IAAI,CAAC,OAAO,cAAc,EAAE;gBAC3G,MAAM,SAAS,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,GAAG,OAAO,OAAO,CAAC,MAAM,GAAG,OAAO,MAAM;gBAC/F,aAAa,OAAO,OAAO,CAAC,OAAO,MAAM,GAAG,GAAG,GAAG,OAAO;YAC3D,OAAO;gBACL,aAAa,OAAO,OAAO,CAAC,OAAO,WAAW,EAAE,GAAG,OAAO;YAC5D;YACA,IAAI,CAAC,YAAY;gBACf;YACF;QACF;QACA,IAAI,OAAO,aAAa,IAAI,aAAa,OAAO,QAAQ,EAAE;YACxD,OAAO,aAAa;QACtB;QACA,OAAO,IAAI,CAAC;IACd;IACA,gBAAgB,YAAY,EAAE,UAAU,EAAE;QACxC,IAAI,eAAe,KAAK,GAAG;YACzB,aAAa;QACf;QACA,MAAM,SAAS,IAAI;QACnB,MAAM,mBAAmB,OAAO,MAAM,CAAC,SAAS;QAChD,IAAI,CAAC,cAAc;YACjB,2BAA2B;YAC3B,eAAe,qBAAqB,eAAe,aAAa;QAClE;QACA,IAAI,iBAAiB,oBAAoB,iBAAiB,gBAAgB,iBAAiB,YAAY;YACrG,OAAO;QACT;QACA,OAAO,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,sBAAsB,GAAG,kBAAkB;QACvF,OAAO,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,MAAM,CAAC,sBAAsB,GAAG,cAAc;QAChF,OAAO,oBAAoB;QAC3B,OAAO,MAAM,CAAC,SAAS,GAAG;QAC1B,OAAO,MAAM,CAAC,OAAO,CAAC,CAAA;YACpB,IAAI,iBAAiB,YAAY;gBAC/B,QAAQ,KAAK,CAAC,KAAK,GAAG;YACxB,OAAO;gBACL,QAAQ,KAAK,CAAC,MAAM,GAAG;YACzB;QACF;QACA,OAAO,IAAI,CAAC;QACZ,IAAI,YAAY,OAAO,MAAM;QAC7B,OAAO;IACT;IACA,wBAAwB,SAAS,EAAE;QACjC,MAAM,SAAS,IAAI;QACnB,IAAI,OAAO,GAAG,IAAI,cAAc,SAAS,CAAC,OAAO,GAAG,IAAI,cAAc,OAAO;QAC7E,OAAO,GAAG,GAAG,cAAc;QAC3B,OAAO,YAAY,GAAG,OAAO,MAAM,CAAC,SAAS,KAAK,gBAAgB,OAAO,GAAG;QAC5E,IAAI,OAAO,GAAG,EAAE;YACd,OAAO,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,MAAM,CAAC,sBAAsB,CAAC,GAAG,CAAC;YACpE,OAAO,EAAE,CAAC,GAAG,GAAG;QAClB,OAAO;YACL,OAAO,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,sBAAsB,CAAC,GAAG,CAAC;YACvE,OAAO,EAAE,CAAC,GAAG,GAAG;QAClB;QACA,OAAO,MAAM;IACf;IACA,MAAM,OAAO,EAAE;QACb,MAAM,SAAS,IAAI;QACnB,IAAI,OAAO,OAAO,EAAE,OAAO;QAE3B,UAAU;QACV,IAAI,KAAK,WAAW,OAAO,MAAM,CAAC,EAAE;QACpC,IAAI,OAAO,OAAO,UAAU;YAC1B,KAAK,SAAS,aAAa,CAAC;QAC9B;QACA,IAAI,CAAC,IAAI;YACP,OAAO;QACT;QACA,GAAG,MAAM,GAAG;QACZ,IAAI,GAAG,UAAU,IAAI,GAAG,UAAU,CAAC,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,KAAK,OAAO,MAAM,CAAC,qBAAqB,CAAC,WAAW,IAAI;YAC5H,OAAO,SAAS,GAAG;QACrB;QACA,MAAM,qBAAqB;YACzB,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,IAAI,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM;QAC7E;QACA,MAAM,aAAa;YACjB,IAAI,MAAM,GAAG,UAAU,IAAI,GAAG,UAAU,CAAC,aAAa,EAAE;gBACtD,MAAM,MAAM,GAAG,UAAU,CAAC,aAAa,CAAC;gBACxC,sCAAsC;gBACtC,OAAO;YACT;YACA,OAAO,CAAA,GAAA,0IAAA,CAAA,IAAe,AAAD,EAAE,IAAI,qBAAqB,CAAC,EAAE;QACrD;QACA,eAAe;QACf,IAAI,YAAY;QAChB,IAAI,CAAC,aAAa,OAAO,MAAM,CAAC,cAAc,EAAE;YAC9C,YAAY,CAAA,GAAA,0IAAA,CAAA,IAAa,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC,YAAY;YAC3D,GAAG,MAAM,CAAC;YACV,CAAA,GAAA,0IAAA,CAAA,IAAe,AAAD,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC,CAAA;gBAC1D,UAAU,MAAM,CAAC;YACnB;QACF;QACA,OAAO,MAAM,CAAC,QAAQ;YACpB;YACA;YACA,UAAU,OAAO,SAAS,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,GAAG,GAAG,UAAU,CAAC,IAAI,GAAG;YACpF,QAAQ,OAAO,SAAS,GAAG,GAAG,UAAU,CAAC,IAAI,GAAG;YAChD,SAAS;YACT,MAAM;YACN,KAAK,GAAG,GAAG,CAAC,WAAW,OAAO,SAAS,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,IAAI,iBAAiB;YACzE,cAAc,OAAO,MAAM,CAAC,SAAS,KAAK,gBAAgB,CAAC,GAAG,GAAG,CAAC,WAAW,OAAO,SAAS,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,IAAI,iBAAiB,KAAK;YACpI,UAAU,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,WAAW,eAAe;QACnD;QACA,OAAO;IACT;IACA,KAAK,EAAE,EAAE;QACP,MAAM,SAAS,IAAI;QACnB,IAAI,OAAO,WAAW,EAAE,OAAO;QAC/B,MAAM,UAAU,OAAO,KAAK,CAAC;QAC7B,IAAI,YAAY,OAAO,OAAO;QAC9B,OAAO,IAAI,CAAC;QAEZ,iBAAiB;QACjB,IAAI,OAAO,MAAM,CAAC,WAAW,EAAE;YAC7B,OAAO,aAAa;QACtB;QAEA,cAAc;QACd,OAAO,UAAU;QAEjB,cAAc;QACd,OAAO,UAAU;QAEjB,gBAAgB;QAChB,OAAO,YAAY;QACnB,IAAI,OAAO,MAAM,CAAC,aAAa,EAAE;YAC/B,OAAO,aAAa;QACtB;QAEA,kBAAkB;QAClB,IAAI,OAAO,MAAM,CAAC,UAAU,IAAI,OAAO,OAAO,EAAE;YAC9C,OAAO,aAAa;QACtB;QAEA,yBAAyB;QACzB,IAAI,OAAO,MAAM,CAAC,IAAI,IAAI,OAAO,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;YACzE,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,YAAY,GAAG,OAAO,OAAO,CAAC,YAAY,EAAE,GAAG,OAAO,MAAM,CAAC,kBAAkB,EAAE,OAAO;QACvH,OAAO;YACL,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,YAAY,EAAE,GAAG,OAAO,MAAM,CAAC,kBAAkB,EAAE,OAAO;QACzF;QAEA,cAAc;QACd,IAAI,OAAO,MAAM,CAAC,IAAI,EAAE;YACtB,OAAO,UAAU,CAAC,WAAW;QAC/B;QAEA,gBAAgB;QAChB,OAAO,YAAY;QACnB,MAAM,eAAe;eAAI,OAAO,EAAE,CAAC,gBAAgB,CAAC;SAAoB;QACxE,IAAI,OAAO,SAAS,EAAE;YACpB,aAAa,IAAI,IAAI,OAAO,MAAM,CAAC,gBAAgB,CAAC;QACtD;QACA,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,QAAQ,QAAQ,EAAE;gBACpB,qBAAqB,QAAQ;YAC/B,OAAO;gBACL,QAAQ,gBAAgB,CAAC,QAAQ,CAAA;oBAC/B,qBAAqB,QAAQ,EAAE,MAAM;gBACvC;YACF;QACF;QACA,QAAQ;QAER,YAAY;QACZ,OAAO,WAAW,GAAG;QACrB,QAAQ;QAER,OAAO;QACP,OAAO,IAAI,CAAC;QACZ,OAAO,IAAI,CAAC;QACZ,OAAO;IACT;IACA,QAAQ,cAAc,EAAE,WAAW,EAAE;QACnC,IAAI,mBAAmB,KAAK,GAAG;YAC7B,iBAAiB;QACnB;QACA,IAAI,gBAAgB,KAAK,GAAG;YAC1B,cAAc;QAChB;QACA,MAAM,SAAS,IAAI;QACnB,MAAM,EACJ,MAAM,EACN,EAAE,EACF,SAAS,EACT,MAAM,EACP,GAAG;QACJ,IAAI,OAAO,OAAO,MAAM,KAAK,eAAe,OAAO,SAAS,EAAE;YAC5D,OAAO;QACT;QACA,OAAO,IAAI,CAAC;QAEZ,YAAY;QACZ,OAAO,WAAW,GAAG;QAErB,gBAAgB;QAChB,OAAO,YAAY;QAEnB,eAAe;QACf,IAAI,OAAO,IAAI,EAAE;YACf,OAAO,WAAW;QACpB;QAEA,iBAAiB;QACjB,IAAI,aAAa;YACf,OAAO,aAAa;YACpB,IAAI,MAAM,OAAO,OAAO,UAAU;gBAChC,GAAG,eAAe,CAAC;YACrB;YACA,IAAI,WAAW;gBACb,UAAU,eAAe,CAAC;YAC5B;YACA,IAAI,UAAU,OAAO,MAAM,EAAE;gBAC3B,OAAO,OAAO,CAAC,CAAA;oBACb,QAAQ,SAAS,CAAC,MAAM,CAAC,OAAO,iBAAiB,EAAE,OAAO,sBAAsB,EAAE,OAAO,gBAAgB,EAAE,OAAO,cAAc,EAAE,OAAO,cAAc;oBACvJ,QAAQ,eAAe,CAAC;oBACxB,QAAQ,eAAe,CAAC;gBAC1B;YACF;QACF;QACA,OAAO,IAAI,CAAC;QAEZ,wBAAwB;QACxB,OAAO,IAAI,CAAC,OAAO,eAAe,EAAE,OAAO,CAAC,CAAA;YAC1C,OAAO,GAAG,CAAC;QACb;QACA,IAAI,mBAAmB,OAAO;YAC5B,IAAI,OAAO,EAAE,IAAI,OAAO,OAAO,EAAE,KAAK,UAAU;gBAC9C,OAAO,EAAE,CAAC,MAAM,GAAG;YACrB;YACA,CAAA,GAAA,0IAAA,CAAA,IAAW,AAAD,EAAE;QACd;QACA,OAAO,SAAS,GAAG;QACnB,OAAO;IACT;IACA,OAAO,eAAe,WAAW,EAAE;QACjC,CAAA,GAAA,0IAAA,CAAA,IAAM,AAAD,EAAE,kBAAkB;IAC3B;IACA,WAAW,mBAAmB;QAC5B,OAAO;IACT;IACA,WAAW,WAAW;QACpB,OAAO;IACT;IACA,OAAO,cAAc,GAAG,EAAE;QACxB,IAAI,CAAC,OAAO,SAAS,CAAC,WAAW,EAAE,OAAO,SAAS,CAAC,WAAW,GAAG,EAAE;QACpE,MAAM,UAAU,OAAO,SAAS,CAAC,WAAW;QAC5C,IAAI,OAAO,QAAQ,cAAc,QAAQ,OAAO,CAAC,OAAO,GAAG;YACzD,QAAQ,IAAI,CAAC;QACf;IACF;IACA,OAAO,IAAI,MAAM,EAAE;QACjB,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,OAAO,OAAO,CAAC,CAAA,IAAK,OAAO,aAAa,CAAC;YACzC,OAAO;QACT;QACA,OAAO,aAAa,CAAC;QACrB,OAAO;IACT;AACF;AACA,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;IAC9B,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,OAAO,CAAC,CAAA;QAC9C,OAAO,SAAS,CAAC,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC,YAAY;IACzE;AACF;AACA,OAAO,GAAG,CAAC;IAAC;IAAQ;CAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/node_modules/swiper/shared/update-swiper.mjs"], "sourcesContent": ["import { s as setInnerHTML } from './utils.mjs';\n\n/* underscore in name -> watch for changes */\nconst paramsList = ['eventsPrefix', 'injectStyles', 'injectStylesUrls', 'modules', 'init', '_direction', 'oneWayMovement', 'swiperElementNodeName', 'touchEventsTarget', 'initialSlide', '_speed', 'cssMode', 'updateOnWindowResize', 'resizeObserver', 'nested', 'focusableElements', '_enabled', '_width', '_height', 'preventInteractionOnTransition', 'userAgent', 'url', '_edgeSwipeDetection', '_edgeSwipeThreshold', '_freeMode', '_autoHeight', 'setWrapperSize', 'virtualTranslate', '_effect', 'breakpoints', 'breakpointsBase', '_spaceBetween', '_slidesPerView', 'maxBackfaceHiddenSlides', '_grid', '_slidesPerGroup', '_slidesPerGroupSkip', '_slidesPerGroupAuto', '_centeredSlides', '_centeredSlidesBounds', '_slidesOffsetBefore', '_slidesOffsetAfter', 'normalizeSlideIndex', '_centerInsufficientSlides', '_watchOverflow', 'roundLengths', 'touchRatio', 'touchAngle', 'simulateTouch', '_shortSwipes', '_longSwipes', 'longSwipesRatio', 'longSwipesMs', '_followFinger', 'allowTouchMove', '_threshold', 'touchMoveStopPropagation', 'touchStartPreventDefault', 'touchStartForcePreventDefault', 'touchReleaseOnEdges', 'uniqueNavElements', '_resistance', '_resistanceRatio', '_watchSlidesProgress', '_grabCursor', 'preventClicks', 'preventClicksPropagation', '_slideToClickedSlide', '_loop', 'loopAdditionalSlides', 'loopAddBlankSlides', 'loopPreventsSliding', '_rewind', '_allowSlidePrev', '_allowSlideNext', '_swipeHandler', '_noSwiping', 'noSwipingClass', 'noSwipingSelector', 'passiveListeners', 'containerModifierClass', 'slideClass', 'slideActiveClass', 'slideVisibleClass', 'slideFullyVisibleClass', 'slideNextClass', 'slidePrevClass', 'slideBlankClass', 'wrapperClass', 'lazyPreloaderClass', 'lazyPreloadPrevNext', 'runCallbacksOnInit', 'observer', 'observeParents', 'observeSlideChildren',\n// modules\n'a11y', '_autoplay', '_controller', 'coverflowEffect', 'cubeEffect', 'fadeEffect', 'flipEffect', 'creativeEffect', 'cardsEffect', 'hashNavigation', 'history', 'keyboard', 'mousewheel', '_navigation', '_pagination', 'parallax', '_scrollbar', '_thumbs', 'virtual', 'zoom', 'control'];\n\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object' && !o.__swiper__;\n}\nfunction extend(target, src) {\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      if (src[key].__swiper__) target[key] = src[key];else extend(target[key], src[key]);\n    } else {\n      target[key] = src[key];\n    }\n  });\n}\nfunction needsNavigation(params) {\n  if (params === void 0) {\n    params = {};\n  }\n  return params.navigation && typeof params.navigation.nextEl === 'undefined' && typeof params.navigation.prevEl === 'undefined';\n}\nfunction needsPagination(params) {\n  if (params === void 0) {\n    params = {};\n  }\n  return params.pagination && typeof params.pagination.el === 'undefined';\n}\nfunction needsScrollbar(params) {\n  if (params === void 0) {\n    params = {};\n  }\n  return params.scrollbar && typeof params.scrollbar.el === 'undefined';\n}\nfunction uniqueClasses(classNames) {\n  if (classNames === void 0) {\n    classNames = '';\n  }\n  const classes = classNames.split(' ').map(c => c.trim()).filter(c => !!c);\n  const unique = [];\n  classes.forEach(c => {\n    if (unique.indexOf(c) < 0) unique.push(c);\n  });\n  return unique.join(' ');\n}\nfunction attrToProp(attrName) {\n  if (attrName === void 0) {\n    attrName = '';\n  }\n  return attrName.replace(/-[a-z]/g, l => l.toUpperCase().replace('-', ''));\n}\nfunction wrapperClass(className) {\n  if (className === void 0) {\n    className = '';\n  }\n  if (!className) return 'swiper-wrapper';\n  if (!className.includes('swiper-wrapper')) return `swiper-wrapper ${className}`;\n  return className;\n}\n\nfunction updateSwiper(_ref) {\n  let {\n    swiper,\n    slides,\n    passedParams,\n    changedParams,\n    nextEl,\n    prevEl,\n    scrollbarEl,\n    paginationEl\n  } = _ref;\n  const updateParams = changedParams.filter(key => key !== 'children' && key !== 'direction' && key !== 'wrapperClass');\n  const {\n    params: currentParams,\n    pagination,\n    navigation,\n    scrollbar,\n    virtual,\n    thumbs\n  } = swiper;\n  let needThumbsInit;\n  let needControllerInit;\n  let needPaginationInit;\n  let needScrollbarInit;\n  let needNavigationInit;\n  let loopNeedDestroy;\n  let loopNeedEnable;\n  let loopNeedReloop;\n  if (changedParams.includes('thumbs') && passedParams.thumbs && passedParams.thumbs.swiper && !passedParams.thumbs.swiper.destroyed && currentParams.thumbs && (!currentParams.thumbs.swiper || currentParams.thumbs.swiper.destroyed)) {\n    needThumbsInit = true;\n  }\n  if (changedParams.includes('controller') && passedParams.controller && passedParams.controller.control && currentParams.controller && !currentParams.controller.control) {\n    needControllerInit = true;\n  }\n  if (changedParams.includes('pagination') && passedParams.pagination && (passedParams.pagination.el || paginationEl) && (currentParams.pagination || currentParams.pagination === false) && pagination && !pagination.el) {\n    needPaginationInit = true;\n  }\n  if (changedParams.includes('scrollbar') && passedParams.scrollbar && (passedParams.scrollbar.el || scrollbarEl) && (currentParams.scrollbar || currentParams.scrollbar === false) && scrollbar && !scrollbar.el) {\n    needScrollbarInit = true;\n  }\n  if (changedParams.includes('navigation') && passedParams.navigation && (passedParams.navigation.prevEl || prevEl) && (passedParams.navigation.nextEl || nextEl) && (currentParams.navigation || currentParams.navigation === false) && navigation && !navigation.prevEl && !navigation.nextEl) {\n    needNavigationInit = true;\n  }\n  const destroyModule = mod => {\n    if (!swiper[mod]) return;\n    swiper[mod].destroy();\n    if (mod === 'navigation') {\n      if (swiper.isElement) {\n        swiper[mod].prevEl.remove();\n        swiper[mod].nextEl.remove();\n      }\n      currentParams[mod].prevEl = undefined;\n      currentParams[mod].nextEl = undefined;\n      swiper[mod].prevEl = undefined;\n      swiper[mod].nextEl = undefined;\n    } else {\n      if (swiper.isElement) {\n        swiper[mod].el.remove();\n      }\n      currentParams[mod].el = undefined;\n      swiper[mod].el = undefined;\n    }\n  };\n  if (changedParams.includes('loop') && swiper.isElement) {\n    if (currentParams.loop && !passedParams.loop) {\n      loopNeedDestroy = true;\n    } else if (!currentParams.loop && passedParams.loop) {\n      loopNeedEnable = true;\n    } else {\n      loopNeedReloop = true;\n    }\n  }\n  updateParams.forEach(key => {\n    if (isObject(currentParams[key]) && isObject(passedParams[key])) {\n      Object.assign(currentParams[key], passedParams[key]);\n      if ((key === 'navigation' || key === 'pagination' || key === 'scrollbar') && 'enabled' in passedParams[key] && !passedParams[key].enabled) {\n        destroyModule(key);\n      }\n    } else {\n      const newValue = passedParams[key];\n      if ((newValue === true || newValue === false) && (key === 'navigation' || key === 'pagination' || key === 'scrollbar')) {\n        if (newValue === false) {\n          destroyModule(key);\n        }\n      } else {\n        currentParams[key] = passedParams[key];\n      }\n    }\n  });\n  if (updateParams.includes('controller') && !needControllerInit && swiper.controller && swiper.controller.control && currentParams.controller && currentParams.controller.control) {\n    swiper.controller.control = currentParams.controller.control;\n  }\n  if (changedParams.includes('children') && slides && virtual && currentParams.virtual.enabled) {\n    virtual.slides = slides;\n    virtual.update(true);\n  } else if (changedParams.includes('virtual') && virtual && currentParams.virtual.enabled) {\n    if (slides) virtual.slides = slides;\n    virtual.update(true);\n  }\n  if (changedParams.includes('children') && slides && currentParams.loop) {\n    loopNeedReloop = true;\n  }\n  if (needThumbsInit) {\n    const initialized = thumbs.init();\n    if (initialized) thumbs.update(true);\n  }\n  if (needControllerInit) {\n    swiper.controller.control = currentParams.controller.control;\n  }\n  if (needPaginationInit) {\n    if (swiper.isElement && (!paginationEl || typeof paginationEl === 'string')) {\n      paginationEl = document.createElement('div');\n      paginationEl.classList.add('swiper-pagination');\n      paginationEl.part.add('pagination');\n      swiper.el.appendChild(paginationEl);\n    }\n    if (paginationEl) currentParams.pagination.el = paginationEl;\n    pagination.init();\n    pagination.render();\n    pagination.update();\n  }\n  if (needScrollbarInit) {\n    if (swiper.isElement && (!scrollbarEl || typeof scrollbarEl === 'string')) {\n      scrollbarEl = document.createElement('div');\n      scrollbarEl.classList.add('swiper-scrollbar');\n      scrollbarEl.part.add('scrollbar');\n      swiper.el.appendChild(scrollbarEl);\n    }\n    if (scrollbarEl) currentParams.scrollbar.el = scrollbarEl;\n    scrollbar.init();\n    scrollbar.updateSize();\n    scrollbar.setTranslate();\n  }\n  if (needNavigationInit) {\n    if (swiper.isElement) {\n      if (!nextEl || typeof nextEl === 'string') {\n        nextEl = document.createElement('div');\n        nextEl.classList.add('swiper-button-next');\n        setInnerHTML(nextEl, swiper.hostEl.constructor.nextButtonSvg);\n        nextEl.part.add('button-next');\n        swiper.el.appendChild(nextEl);\n      }\n      if (!prevEl || typeof prevEl === 'string') {\n        prevEl = document.createElement('div');\n        prevEl.classList.add('swiper-button-prev');\n        setInnerHTML(prevEl, swiper.hostEl.constructor.prevButtonSvg);\n        prevEl.part.add('button-prev');\n        swiper.el.appendChild(prevEl);\n      }\n    }\n    if (nextEl) currentParams.navigation.nextEl = nextEl;\n    if (prevEl) currentParams.navigation.prevEl = prevEl;\n    navigation.init();\n    navigation.update();\n  }\n  if (changedParams.includes('allowSlideNext')) {\n    swiper.allowSlideNext = passedParams.allowSlideNext;\n  }\n  if (changedParams.includes('allowSlidePrev')) {\n    swiper.allowSlidePrev = passedParams.allowSlidePrev;\n  }\n  if (changedParams.includes('direction')) {\n    swiper.changeDirection(passedParams.direction, false);\n  }\n  if (loopNeedDestroy || loopNeedReloop) {\n    swiper.loopDestroy();\n  }\n  if (loopNeedEnable || loopNeedReloop) {\n    swiper.loopCreate();\n  }\n  swiper.update();\n}\n\nexport { needsPagination as a, needsScrollbar as b, attrToProp as c, uniqueClasses as d, extend as e, isObject as i, needsNavigation as n, paramsList as p, updateSwiper as u, wrapperClass as w };\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAEA,2CAA2C,GAC3C,MAAM,aAAa;IAAC;IAAgB;IAAgB;IAAoB;IAAW;IAAQ;IAAc;IAAkB;IAAyB;IAAqB;IAAgB;IAAU;IAAW;IAAwB;IAAkB;IAAU;IAAqB;IAAY;IAAU;IAAW;IAAkC;IAAa;IAAO;IAAuB;IAAuB;IAAa;IAAe;IAAkB;IAAoB;IAAW;IAAe;IAAmB;IAAiB;IAAkB;IAA2B;IAAS;IAAmB;IAAuB;IAAuB;IAAmB;IAAyB;IAAuB;IAAsB;IAAuB;IAA6B;IAAkB;IAAgB;IAAc;IAAc;IAAiB;IAAgB;IAAe;IAAmB;IAAgB;IAAiB;IAAkB;IAAc;IAA4B;IAA4B;IAAiC;IAAuB;IAAqB;IAAe;IAAoB;IAAwB;IAAe;IAAiB;IAA4B;IAAwB;IAAS;IAAwB;IAAsB;IAAuB;IAAW;IAAmB;IAAmB;IAAiB;IAAc;IAAkB;IAAqB;IAAoB;IAA0B;IAAc;IAAoB;IAAqB;IAA0B;IAAkB;IAAkB;IAAmB;IAAgB;IAAsB;IAAuB;IAAsB;IAAY;IAAkB;IACluD,UAAU;IACV;IAAQ;IAAa;IAAe;IAAmB;IAAc;IAAc;IAAc;IAAkB;IAAe;IAAkB;IAAW;IAAY;IAAc;IAAe;IAAe;IAAY;IAAc;IAAW;IAAW;IAAQ;CAAU;AAEzR,SAAS,SAAS,CAAC;IACjB,OAAO,OAAO,MAAM,YAAY,MAAM,QAAQ,EAAE,WAAW,IAAI,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,YAAY,CAAC,EAAE,UAAU;AAC7I;AACA,SAAS,OAAO,MAAM,EAAE,GAAG;IACzB,MAAM,WAAW;QAAC;QAAa;QAAe;KAAY;IAC1D,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,CAAA,MAAO,SAAS,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,CAAA;QAChE,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,aAAa,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;aAAM,IAAI,SAAS,GAAG,CAAC,IAAI,KAAK,SAAS,MAAM,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;YACvJ,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;iBAAM,OAAO,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;QACnF,OAAO;YACL,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QACxB;IACF;AACF;AACA,SAAS,gBAAgB,MAAM;IAC7B,IAAI,WAAW,KAAK,GAAG;QACrB,SAAS,CAAC;IACZ;IACA,OAAO,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,CAAC,MAAM,KAAK,eAAe,OAAO,OAAO,UAAU,CAAC,MAAM,KAAK;AACrH;AACA,SAAS,gBAAgB,MAAM;IAC7B,IAAI,WAAW,KAAK,GAAG;QACrB,SAAS,CAAC;IACZ;IACA,OAAO,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,CAAC,EAAE,KAAK;AAC9D;AACA,SAAS,eAAe,MAAM;IAC5B,IAAI,WAAW,KAAK,GAAG;QACrB,SAAS,CAAC;IACZ;IACA,OAAO,OAAO,SAAS,IAAI,OAAO,OAAO,SAAS,CAAC,EAAE,KAAK;AAC5D;AACA,SAAS,cAAc,UAAU;IAC/B,IAAI,eAAe,KAAK,GAAG;QACzB,aAAa;IACf;IACA,MAAM,UAAU,WAAW,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC;IACvE,MAAM,SAAS,EAAE;IACjB,QAAQ,OAAO,CAAC,CAAA;QACd,IAAI,OAAO,OAAO,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC;IACzC;IACA,OAAO,OAAO,IAAI,CAAC;AACrB;AACA,SAAS,WAAW,QAAQ;IAC1B,IAAI,aAAa,KAAK,GAAG;QACvB,WAAW;IACb;IACA,OAAO,SAAS,OAAO,CAAC,WAAW,CAAA,IAAK,EAAE,WAAW,GAAG,OAAO,CAAC,KAAK;AACvE;AACA,SAAS,aAAa,SAAS;IAC7B,IAAI,cAAc,KAAK,GAAG;QACxB,YAAY;IACd;IACA,IAAI,CAAC,WAAW,OAAO;IACvB,IAAI,CAAC,UAAU,QAAQ,CAAC,mBAAmB,OAAO,CAAC,eAAe,EAAE,WAAW;IAC/E,OAAO;AACT;AAEA,SAAS,aAAa,IAAI;IACxB,IAAI,EACF,MAAM,EACN,MAAM,EACN,YAAY,EACZ,aAAa,EACb,MAAM,EACN,MAAM,EACN,WAAW,EACX,YAAY,EACb,GAAG;IACJ,MAAM,eAAe,cAAc,MAAM,CAAC,CAAA,MAAO,QAAQ,cAAc,QAAQ,eAAe,QAAQ;IACtG,MAAM,EACJ,QAAQ,aAAa,EACrB,UAAU,EACV,UAAU,EACV,SAAS,EACT,OAAO,EACP,MAAM,EACP,GAAG;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,cAAc,QAAQ,CAAC,aAAa,aAAa,MAAM,IAAI,aAAa,MAAM,CAAC,MAAM,IAAI,CAAC,aAAa,MAAM,CAAC,MAAM,CAAC,SAAS,IAAI,cAAc,MAAM,IAAI,CAAC,CAAC,cAAc,MAAM,CAAC,MAAM,IAAI,cAAc,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG;QACrO,iBAAiB;IACnB;IACA,IAAI,cAAc,QAAQ,CAAC,iBAAiB,aAAa,UAAU,IAAI,aAAa,UAAU,CAAC,OAAO,IAAI,cAAc,UAAU,IAAI,CAAC,cAAc,UAAU,CAAC,OAAO,EAAE;QACvK,qBAAqB;IACvB;IACA,IAAI,cAAc,QAAQ,CAAC,iBAAiB,aAAa,UAAU,IAAI,CAAC,aAAa,UAAU,CAAC,EAAE,IAAI,YAAY,KAAK,CAAC,cAAc,UAAU,IAAI,cAAc,UAAU,KAAK,KAAK,KAAK,cAAc,CAAC,WAAW,EAAE,EAAE;QACvN,qBAAqB;IACvB;IACA,IAAI,cAAc,QAAQ,CAAC,gBAAgB,aAAa,SAAS,IAAI,CAAC,aAAa,SAAS,CAAC,EAAE,IAAI,WAAW,KAAK,CAAC,cAAc,SAAS,IAAI,cAAc,SAAS,KAAK,KAAK,KAAK,aAAa,CAAC,UAAU,EAAE,EAAE;QAC/M,oBAAoB;IACtB;IACA,IAAI,cAAc,QAAQ,CAAC,iBAAiB,aAAa,UAAU,IAAI,CAAC,aAAa,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,CAAC,aAAa,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,CAAC,cAAc,UAAU,IAAI,cAAc,UAAU,KAAK,KAAK,KAAK,cAAc,CAAC,WAAW,MAAM,IAAI,CAAC,WAAW,MAAM,EAAE;QAC7R,qBAAqB;IACvB;IACA,MAAM,gBAAgB,CAAA;QACpB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;QAClB,MAAM,CAAC,IAAI,CAAC,OAAO;QACnB,IAAI,QAAQ,cAAc;YACxB,IAAI,OAAO,SAAS,EAAE;gBACpB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;gBACzB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;YAC3B;YACA,aAAa,CAAC,IAAI,CAAC,MAAM,GAAG;YAC5B,aAAa,CAAC,IAAI,CAAC,MAAM,GAAG;YAC5B,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG;YACrB,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG;QACvB,OAAO;YACL,IAAI,OAAO,SAAS,EAAE;gBACpB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM;YACvB;YACA,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG;YACxB,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG;QACnB;IACF;IACA,IAAI,cAAc,QAAQ,CAAC,WAAW,OAAO,SAAS,EAAE;QACtD,IAAI,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE;YAC5C,kBAAkB;QACpB,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,aAAa,IAAI,EAAE;YACnD,iBAAiB;QACnB,OAAO;YACL,iBAAiB;QACnB;IACF;IACA,aAAa,OAAO,CAAC,CAAA;QACnB,IAAI,SAAS,aAAa,CAAC,IAAI,KAAK,SAAS,YAAY,CAAC,IAAI,GAAG;YAC/D,OAAO,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI;YACnD,IAAI,CAAC,QAAQ,gBAAgB,QAAQ,gBAAgB,QAAQ,WAAW,KAAK,aAAa,YAAY,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE;gBACzI,cAAc;YAChB;QACF,OAAO;YACL,MAAM,WAAW,YAAY,CAAC,IAAI;YAClC,IAAI,CAAC,aAAa,QAAQ,aAAa,KAAK,KAAK,CAAC,QAAQ,gBAAgB,QAAQ,gBAAgB,QAAQ,WAAW,GAAG;gBACtH,IAAI,aAAa,OAAO;oBACtB,cAAc;gBAChB;YACF,OAAO;gBACL,aAAa,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI;YACxC;QACF;IACF;IACA,IAAI,aAAa,QAAQ,CAAC,iBAAiB,CAAC,sBAAsB,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,OAAO,IAAI,cAAc,UAAU,IAAI,cAAc,UAAU,CAAC,OAAO,EAAE;QAChL,OAAO,UAAU,CAAC,OAAO,GAAG,cAAc,UAAU,CAAC,OAAO;IAC9D;IACA,IAAI,cAAc,QAAQ,CAAC,eAAe,UAAU,WAAW,cAAc,OAAO,CAAC,OAAO,EAAE;QAC5F,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,CAAC;IACjB,OAAO,IAAI,cAAc,QAAQ,CAAC,cAAc,WAAW,cAAc,OAAO,CAAC,OAAO,EAAE;QACxF,IAAI,QAAQ,QAAQ,MAAM,GAAG;QAC7B,QAAQ,MAAM,CAAC;IACjB;IACA,IAAI,cAAc,QAAQ,CAAC,eAAe,UAAU,cAAc,IAAI,EAAE;QACtE,iBAAiB;IACnB;IACA,IAAI,gBAAgB;QAClB,MAAM,cAAc,OAAO,IAAI;QAC/B,IAAI,aAAa,OAAO,MAAM,CAAC;IACjC;IACA,IAAI,oBAAoB;QACtB,OAAO,UAAU,CAAC,OAAO,GAAG,cAAc,UAAU,CAAC,OAAO;IAC9D;IACA,IAAI,oBAAoB;QACtB,IAAI,OAAO,SAAS,IAAI,CAAC,CAAC,gBAAgB,OAAO,iBAAiB,QAAQ,GAAG;YAC3E,eAAe,SAAS,aAAa,CAAC;YACtC,aAAa,SAAS,CAAC,GAAG,CAAC;YAC3B,aAAa,IAAI,CAAC,GAAG,CAAC;YACtB,OAAO,EAAE,CAAC,WAAW,CAAC;QACxB;QACA,IAAI,cAAc,cAAc,UAAU,CAAC,EAAE,GAAG;QAChD,WAAW,IAAI;QACf,WAAW,MAAM;QACjB,WAAW,MAAM;IACnB;IACA,IAAI,mBAAmB;QACrB,IAAI,OAAO,SAAS,IAAI,CAAC,CAAC,eAAe,OAAO,gBAAgB,QAAQ,GAAG;YACzE,cAAc,SAAS,aAAa,CAAC;YACrC,YAAY,SAAS,CAAC,GAAG,CAAC;YAC1B,YAAY,IAAI,CAAC,GAAG,CAAC;YACrB,OAAO,EAAE,CAAC,WAAW,CAAC;QACxB;QACA,IAAI,aAAa,cAAc,SAAS,CAAC,EAAE,GAAG;QAC9C,UAAU,IAAI;QACd,UAAU,UAAU;QACpB,UAAU,YAAY;IACxB;IACA,IAAI,oBAAoB;QACtB,IAAI,OAAO,SAAS,EAAE;YACpB,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;gBACzC,SAAS,SAAS,aAAa,CAAC;gBAChC,OAAO,SAAS,CAAC,GAAG,CAAC;gBACrB,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,QAAQ,OAAO,MAAM,CAAC,WAAW,CAAC,aAAa;gBAC5D,OAAO,IAAI,CAAC,GAAG,CAAC;gBAChB,OAAO,EAAE,CAAC,WAAW,CAAC;YACxB;YACA,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;gBACzC,SAAS,SAAS,aAAa,CAAC;gBAChC,OAAO,SAAS,CAAC,GAAG,CAAC;gBACrB,CAAA,GAAA,0IAAA,CAAA,IAAY,AAAD,EAAE,QAAQ,OAAO,MAAM,CAAC,WAAW,CAAC,aAAa;gBAC5D,OAAO,IAAI,CAAC,GAAG,CAAC;gBAChB,OAAO,EAAE,CAAC,WAAW,CAAC;YACxB;QACF;QACA,IAAI,QAAQ,cAAc,UAAU,CAAC,MAAM,GAAG;QAC9C,IAAI,QAAQ,cAAc,UAAU,CAAC,MAAM,GAAG;QAC9C,WAAW,IAAI;QACf,WAAW,MAAM;IACnB;IACA,IAAI,cAAc,QAAQ,CAAC,mBAAmB;QAC5C,OAAO,cAAc,GAAG,aAAa,cAAc;IACrD;IACA,IAAI,cAAc,QAAQ,CAAC,mBAAmB;QAC5C,OAAO,cAAc,GAAG,aAAa,cAAc;IACrD;IACA,IAAI,cAAc,QAAQ,CAAC,cAAc;QACvC,OAAO,eAAe,CAAC,aAAa,SAAS,EAAE;IACjD;IACA,IAAI,mBAAmB,gBAAgB;QACrC,OAAO,WAAW;IACpB;IACA,IAAI,kBAAkB,gBAAgB;QACpC,OAAO,UAAU;IACnB;IACA,OAAO,MAAM;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/node_modules/swiper/shared/update-on-virtual-data.mjs"], "sourcesContent": ["import { e as extend, p as paramsList, i as isObject, n as needsNavigation, a as needsPagination, b as needsScrollbar } from './update-swiper.mjs';\nimport { d as defaults } from './swiper-core.mjs';\n\nfunction getParams(obj, splitEvents) {\n  if (obj === void 0) {\n    obj = {};\n  }\n  if (splitEvents === void 0) {\n    splitEvents = true;\n  }\n  const params = {\n    on: {}\n  };\n  const events = {};\n  const passedParams = {};\n  extend(params, defaults);\n  params._emitClasses = true;\n  params.init = false;\n  const rest = {};\n  const allowedParams = paramsList.map(key => key.replace(/_/, ''));\n  const plainObj = Object.assign({}, obj);\n  Object.keys(plainObj).forEach(key => {\n    if (typeof obj[key] === 'undefined') return;\n    if (allowedParams.indexOf(key) >= 0) {\n      if (isObject(obj[key])) {\n        params[key] = {};\n        passedParams[key] = {};\n        extend(params[key], obj[key]);\n        extend(passedParams[key], obj[key]);\n      } else {\n        params[key] = obj[key];\n        passedParams[key] = obj[key];\n      }\n    } else if (key.search(/on[A-Z]/) === 0 && typeof obj[key] === 'function') {\n      if (splitEvents) {\n        events[`${key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n      } else {\n        params.on[`${key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n      }\n    } else {\n      rest[key] = obj[key];\n    }\n  });\n  ['navigation', 'pagination', 'scrollbar'].forEach(key => {\n    if (params[key] === true) params[key] = {};\n    if (params[key] === false) delete params[key];\n  });\n  return {\n    params,\n    passedParams,\n    rest,\n    events\n  };\n}\n\nfunction mountSwiper(_ref, swiperParams) {\n  let {\n    el,\n    nextEl,\n    prevEl,\n    paginationEl,\n    scrollbarEl,\n    swiper\n  } = _ref;\n  if (needsNavigation(swiperParams) && nextEl && prevEl) {\n    swiper.params.navigation.nextEl = nextEl;\n    swiper.originalParams.navigation.nextEl = nextEl;\n    swiper.params.navigation.prevEl = prevEl;\n    swiper.originalParams.navigation.prevEl = prevEl;\n  }\n  if (needsPagination(swiperParams) && paginationEl) {\n    swiper.params.pagination.el = paginationEl;\n    swiper.originalParams.pagination.el = paginationEl;\n  }\n  if (needsScrollbar(swiperParams) && scrollbarEl) {\n    swiper.params.scrollbar.el = scrollbarEl;\n    swiper.originalParams.scrollbar.el = scrollbarEl;\n  }\n  swiper.init(el);\n}\n\nfunction getChangedParams(swiperParams, oldParams, children, oldChildren, getKey) {\n  const keys = [];\n  if (!oldParams) return keys;\n  const addKey = key => {\n    if (keys.indexOf(key) < 0) keys.push(key);\n  };\n  if (children && oldChildren) {\n    const oldChildrenKeys = oldChildren.map(getKey);\n    const childrenKeys = children.map(getKey);\n    if (oldChildrenKeys.join('') !== childrenKeys.join('')) addKey('children');\n    if (oldChildren.length !== children.length) addKey('children');\n  }\n  const watchParams = paramsList.filter(key => key[0] === '_').map(key => key.replace(/_/, ''));\n  watchParams.forEach(key => {\n    if (key in swiperParams && key in oldParams) {\n      if (isObject(swiperParams[key]) && isObject(oldParams[key])) {\n        const newKeys = Object.keys(swiperParams[key]);\n        const oldKeys = Object.keys(oldParams[key]);\n        if (newKeys.length !== oldKeys.length) {\n          addKey(key);\n        } else {\n          newKeys.forEach(newKey => {\n            if (swiperParams[key][newKey] !== oldParams[key][newKey]) {\n              addKey(key);\n            }\n          });\n          oldKeys.forEach(oldKey => {\n            if (swiperParams[key][oldKey] !== oldParams[key][oldKey]) addKey(key);\n          });\n        }\n      } else if (swiperParams[key] !== oldParams[key]) {\n        addKey(key);\n      }\n    }\n  });\n  return keys;\n}\n\nconst updateOnVirtualData = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params.virtual || swiper.params.virtual && !swiper.params.virtual.enabled) return;\n  swiper.updateSlides();\n  swiper.updateProgress();\n  swiper.updateSlidesClasses();\n  swiper.emit('_virtualUpdated');\n  if (swiper.parallax && swiper.params.parallax && swiper.params.parallax.enabled) {\n    swiper.parallax.setTranslate();\n  }\n};\n\nexport { getChangedParams as a, getParams as g, mountSwiper as m, updateOnVirtualData as u };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,SAAS,UAAU,GAAG,EAAE,WAAW;IACjC,IAAI,QAAQ,KAAK,GAAG;QAClB,MAAM,CAAC;IACT;IACA,IAAI,gBAAgB,KAAK,GAAG;QAC1B,cAAc;IAChB;IACA,MAAM,SAAS;QACb,IAAI,CAAC;IACP;IACA,MAAM,SAAS,CAAC;IAChB,MAAM,eAAe,CAAC;IACtB,CAAA,GAAA,qJAAA,CAAA,IAAM,AAAD,EAAE,QAAQ,mJAAA,CAAA,IAAQ;IACvB,OAAO,YAAY,GAAG;IACtB,OAAO,IAAI,GAAG;IACd,MAAM,OAAO,CAAC;IACd,MAAM,gBAAgB,qJAAA,CAAA,IAAU,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,OAAO,CAAC,KAAK;IAC7D,MAAM,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG;IACnC,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,CAAA;QAC5B,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,aAAa;QACrC,IAAI,cAAc,OAAO,CAAC,QAAQ,GAAG;YACnC,IAAI,CAAA,GAAA,qJAAA,CAAA,IAAQ,AAAD,EAAE,GAAG,CAAC,IAAI,GAAG;gBACtB,MAAM,CAAC,IAAI,GAAG,CAAC;gBACf,YAAY,CAAC,IAAI,GAAG,CAAC;gBACrB,CAAA,GAAA,qJAAA,CAAA,IAAM,AAAD,EAAE,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;gBAC5B,CAAA,GAAA,qJAAA,CAAA,IAAM,AAAD,EAAE,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YACpC,OAAO;gBACL,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;gBACtB,YAAY,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;YAC9B;QACF,OAAO,IAAI,IAAI,MAAM,CAAC,eAAe,KAAK,OAAO,GAAG,CAAC,IAAI,KAAK,YAAY;YACxE,IAAI,aAAa;gBACf,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,WAAW,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI;YAC9D,OAAO;gBACL,OAAO,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,WAAW,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI;YACjE;QACF,OAAO;YACL,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QACtB;IACF;IACA;QAAC;QAAc;QAAc;KAAY,CAAC,OAAO,CAAC,CAAA;QAChD,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,MAAM,CAAC,IAAI,GAAG,CAAC;QACzC,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,OAAO,MAAM,CAAC,IAAI;IAC/C;IACA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,YAAY,IAAI,EAAE,YAAY;IACrC,IAAI,EACF,EAAE,EACF,MAAM,EACN,MAAM,EACN,YAAY,EACZ,WAAW,EACX,MAAM,EACP,GAAG;IACJ,IAAI,CAAA,GAAA,qJAAA,CAAA,IAAe,AAAD,EAAE,iBAAiB,UAAU,QAAQ;QACrD,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG;QAClC,OAAO,cAAc,CAAC,UAAU,CAAC,MAAM,GAAG;QAC1C,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG;QAClC,OAAO,cAAc,CAAC,UAAU,CAAC,MAAM,GAAG;IAC5C;IACA,IAAI,CAAA,GAAA,qJAAA,CAAA,IAAe,AAAD,EAAE,iBAAiB,cAAc;QACjD,OAAO,MAAM,CAAC,UAAU,CAAC,EAAE,GAAG;QAC9B,OAAO,cAAc,CAAC,UAAU,CAAC,EAAE,GAAG;IACxC;IACA,IAAI,CAAA,GAAA,qJAAA,CAAA,IAAc,AAAD,EAAE,iBAAiB,aAAa;QAC/C,OAAO,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG;QAC7B,OAAO,cAAc,CAAC,SAAS,CAAC,EAAE,GAAG;IACvC;IACA,OAAO,IAAI,CAAC;AACd;AAEA,SAAS,iBAAiB,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM;IAC9E,MAAM,OAAO,EAAE;IACf,IAAI,CAAC,WAAW,OAAO;IACvB,MAAM,SAAS,CAAA;QACb,IAAI,KAAK,OAAO,CAAC,OAAO,GAAG,KAAK,IAAI,CAAC;IACvC;IACA,IAAI,YAAY,aAAa;QAC3B,MAAM,kBAAkB,YAAY,GAAG,CAAC;QACxC,MAAM,eAAe,SAAS,GAAG,CAAC;QAClC,IAAI,gBAAgB,IAAI,CAAC,QAAQ,aAAa,IAAI,CAAC,KAAK,OAAO;QAC/D,IAAI,YAAY,MAAM,KAAK,SAAS,MAAM,EAAE,OAAO;IACrD;IACA,MAAM,cAAc,qJAAA,CAAA,IAAU,CAAC,MAAM,CAAC,CAAA,MAAO,GAAG,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,OAAO,CAAC,KAAK;IACzF,YAAY,OAAO,CAAC,CAAA;QAClB,IAAI,OAAO,gBAAgB,OAAO,WAAW;YAC3C,IAAI,CAAA,GAAA,qJAAA,CAAA,IAAQ,AAAD,EAAE,YAAY,CAAC,IAAI,KAAK,CAAA,GAAA,qJAAA,CAAA,IAAQ,AAAD,EAAE,SAAS,CAAC,IAAI,GAAG;gBAC3D,MAAM,UAAU,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI;gBAC7C,MAAM,UAAU,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI;gBAC1C,IAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE;oBACrC,OAAO;gBACT,OAAO;oBACL,QAAQ,OAAO,CAAC,CAAA;wBACd,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE;4BACxD,OAAO;wBACT;oBACF;oBACA,QAAQ,OAAO,CAAC,CAAA;wBACd,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;oBACnE;gBACF;YACF,OAAO,IAAI,YAAY,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE;gBAC/C,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AAEA,MAAM,sBAAsB,CAAA;IAC1B,IAAI,CAAC,UAAU,OAAO,SAAS,IAAI,CAAC,OAAO,MAAM,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;IACtH,OAAO,YAAY;IACnB,OAAO,cAAc;IACrB,OAAO,mBAAmB;IAC1B,OAAO,IAAI,CAAC;IACZ,IAAI,OAAO,QAAQ,IAAI,OAAO,MAAM,CAAC,QAAQ,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE;QAC/E,OAAO,QAAQ,CAAC,YAAY;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4734, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/node_modules/swiper/swiper-react.mjs"], "sourcesContent": ["/**\n * Swiper React 11.2.8\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2025 <PERSON>\n *\n * Released under the MIT License\n *\n * Released on: May 23, 2025\n */\n\nimport React, { useEffect, useLayoutEffect, useContext, createContext, forwardRef, useState, useRef } from 'react';\nimport { S as Swiper$1 } from './shared/swiper-core.mjs';\nimport { g as getParams, m as mountSwiper, a as getChangedParams, u as updateOnVirtualData } from './shared/update-on-virtual-data.mjs';\nimport { d as uniqueClasses, w as wrapperClass, n as needsNavigation, b as needsScrollbar, a as needsPagination, e as extend, u as updateSwiper } from './shared/update-swiper.mjs';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nfunction isChildSwiperSlide(child) {\n  return child.type && child.type.displayName && child.type.displayName.includes('SwiperSlide');\n}\nfunction processChildren(c) {\n  const slides = [];\n  React.Children.toArray(c).forEach(child => {\n    if (isChildSwiperSlide(child)) {\n      slides.push(child);\n    } else if (child.props && child.props.children) {\n      processChildren(child.props.children).forEach(slide => slides.push(slide));\n    }\n  });\n  return slides;\n}\nfunction getChildren(c) {\n  const slides = [];\n  const slots = {\n    'container-start': [],\n    'container-end': [],\n    'wrapper-start': [],\n    'wrapper-end': []\n  };\n  React.Children.toArray(c).forEach(child => {\n    if (isChildSwiperSlide(child)) {\n      slides.push(child);\n    } else if (child.props && child.props.slot && slots[child.props.slot]) {\n      slots[child.props.slot].push(child);\n    } else if (child.props && child.props.children) {\n      const foundSlides = processChildren(child.props.children);\n      if (foundSlides.length > 0) {\n        foundSlides.forEach(slide => slides.push(slide));\n      } else {\n        slots['container-end'].push(child);\n      }\n    } else {\n      slots['container-end'].push(child);\n    }\n  });\n  return {\n    slides,\n    slots\n  };\n}\n\nfunction renderVirtual(swiper, slides, virtualData) {\n  if (!virtualData) return null;\n  const getSlideIndex = index => {\n    let slideIndex = index;\n    if (index < 0) {\n      slideIndex = slides.length + index;\n    } else if (slideIndex >= slides.length) {\n      // eslint-disable-next-line\n      slideIndex = slideIndex - slides.length;\n    }\n    return slideIndex;\n  };\n  const style = swiper.isHorizontal() ? {\n    [swiper.rtlTranslate ? 'right' : 'left']: `${virtualData.offset}px`\n  } : {\n    top: `${virtualData.offset}px`\n  };\n  const {\n    from,\n    to\n  } = virtualData;\n  const loopFrom = swiper.params.loop ? -slides.length : 0;\n  const loopTo = swiper.params.loop ? slides.length * 2 : slides.length;\n  const slidesToRender = [];\n  for (let i = loopFrom; i < loopTo; i += 1) {\n    if (i >= from && i <= to) {\n      slidesToRender.push(slides[getSlideIndex(i)]);\n    }\n  }\n  return slidesToRender.map((child, index) => {\n    return /*#__PURE__*/React.cloneElement(child, {\n      swiper,\n      style,\n      key: child.props.virtualIndex || child.key || `slide-${index}`\n    });\n  });\n}\n\nfunction useIsomorphicLayoutEffect(callback, deps) {\n  // eslint-disable-next-line\n  if (typeof window === 'undefined') return useEffect(callback, deps);\n  return useLayoutEffect(callback, deps);\n}\n\nconst SwiperSlideContext = /*#__PURE__*/createContext(null);\nconst useSwiperSlide = () => {\n  return useContext(SwiperSlideContext);\n};\nconst SwiperContext = /*#__PURE__*/createContext(null);\nconst useSwiper = () => {\n  return useContext(SwiperContext);\n};\n\nconst Swiper = /*#__PURE__*/forwardRef(function (_temp, externalElRef) {\n  let {\n    className,\n    tag: Tag = 'div',\n    wrapperTag: WrapperTag = 'div',\n    children,\n    onSwiper,\n    ...rest\n  } = _temp === void 0 ? {} : _temp;\n  let eventsAssigned = false;\n  const [containerClasses, setContainerClasses] = useState('swiper');\n  const [virtualData, setVirtualData] = useState(null);\n  const [breakpointChanged, setBreakpointChanged] = useState(false);\n  const initializedRef = useRef(false);\n  const swiperElRef = useRef(null);\n  const swiperRef = useRef(null);\n  const oldPassedParamsRef = useRef(null);\n  const oldSlides = useRef(null);\n  const nextElRef = useRef(null);\n  const prevElRef = useRef(null);\n  const paginationElRef = useRef(null);\n  const scrollbarElRef = useRef(null);\n  const {\n    params: swiperParams,\n    passedParams,\n    rest: restProps,\n    events\n  } = getParams(rest);\n  const {\n    slides,\n    slots\n  } = getChildren(children);\n  const onBeforeBreakpoint = () => {\n    setBreakpointChanged(!breakpointChanged);\n  };\n  Object.assign(swiperParams.on, {\n    _containerClasses(swiper, classes) {\n      setContainerClasses(classes);\n    }\n  });\n  const initSwiper = () => {\n    // init swiper\n    Object.assign(swiperParams.on, events);\n    eventsAssigned = true;\n    const passParams = {\n      ...swiperParams\n    };\n    delete passParams.wrapperClass;\n    swiperRef.current = new Swiper$1(passParams);\n    if (swiperRef.current.virtual && swiperRef.current.params.virtual.enabled) {\n      swiperRef.current.virtual.slides = slides;\n      const extendWith = {\n        cache: false,\n        slides,\n        renderExternal: setVirtualData,\n        renderExternalUpdate: false\n      };\n      extend(swiperRef.current.params.virtual, extendWith);\n      extend(swiperRef.current.originalParams.virtual, extendWith);\n    }\n  };\n  if (!swiperElRef.current) {\n    initSwiper();\n  }\n\n  // Listen for breakpoints change\n  if (swiperRef.current) {\n    swiperRef.current.on('_beforeBreakpoint', onBeforeBreakpoint);\n  }\n  const attachEvents = () => {\n    if (eventsAssigned || !events || !swiperRef.current) return;\n    Object.keys(events).forEach(eventName => {\n      swiperRef.current.on(eventName, events[eventName]);\n    });\n  };\n  const detachEvents = () => {\n    if (!events || !swiperRef.current) return;\n    Object.keys(events).forEach(eventName => {\n      swiperRef.current.off(eventName, events[eventName]);\n    });\n  };\n  useEffect(() => {\n    return () => {\n      if (swiperRef.current) swiperRef.current.off('_beforeBreakpoint', onBeforeBreakpoint);\n    };\n  });\n\n  // set initialized flag\n  useEffect(() => {\n    if (!initializedRef.current && swiperRef.current) {\n      swiperRef.current.emitSlidesClasses();\n      initializedRef.current = true;\n    }\n  });\n\n  // mount swiper\n  useIsomorphicLayoutEffect(() => {\n    if (externalElRef) {\n      externalElRef.current = swiperElRef.current;\n    }\n    if (!swiperElRef.current) return;\n    if (swiperRef.current.destroyed) {\n      initSwiper();\n    }\n    mountSwiper({\n      el: swiperElRef.current,\n      nextEl: nextElRef.current,\n      prevEl: prevElRef.current,\n      paginationEl: paginationElRef.current,\n      scrollbarEl: scrollbarElRef.current,\n      swiper: swiperRef.current\n    }, swiperParams);\n    if (onSwiper && !swiperRef.current.destroyed) onSwiper(swiperRef.current);\n    // eslint-disable-next-line\n    return () => {\n      if (swiperRef.current && !swiperRef.current.destroyed) {\n        swiperRef.current.destroy(true, false);\n      }\n    };\n  }, []);\n\n  // watch for params change\n  useIsomorphicLayoutEffect(() => {\n    attachEvents();\n    const changedParams = getChangedParams(passedParams, oldPassedParamsRef.current, slides, oldSlides.current, c => c.key);\n    oldPassedParamsRef.current = passedParams;\n    oldSlides.current = slides;\n    if (changedParams.length && swiperRef.current && !swiperRef.current.destroyed) {\n      updateSwiper({\n        swiper: swiperRef.current,\n        slides,\n        passedParams,\n        changedParams,\n        nextEl: nextElRef.current,\n        prevEl: prevElRef.current,\n        scrollbarEl: scrollbarElRef.current,\n        paginationEl: paginationElRef.current\n      });\n    }\n    return () => {\n      detachEvents();\n    };\n  });\n\n  // update on virtual update\n  useIsomorphicLayoutEffect(() => {\n    updateOnVirtualData(swiperRef.current);\n  }, [virtualData]);\n\n  // bypass swiper instance to slides\n  function renderSlides() {\n    if (swiperParams.virtual) {\n      return renderVirtual(swiperRef.current, slides, virtualData);\n    }\n    return slides.map((child, index) => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        swiper: swiperRef.current,\n        swiperSlideIndex: index\n      });\n    });\n  }\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: swiperElRef,\n    className: uniqueClasses(`${containerClasses}${className ? ` ${className}` : ''}`)\n  }, restProps), /*#__PURE__*/React.createElement(SwiperContext.Provider, {\n    value: swiperRef.current\n  }, slots['container-start'], /*#__PURE__*/React.createElement(WrapperTag, {\n    className: wrapperClass(swiperParams.wrapperClass)\n  }, slots['wrapper-start'], renderSlides(), slots['wrapper-end']), needsNavigation(swiperParams) && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    ref: prevElRef,\n    className: \"swiper-button-prev\"\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: nextElRef,\n    className: \"swiper-button-next\"\n  })), needsScrollbar(swiperParams) && /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollbarElRef,\n    className: \"swiper-scrollbar\"\n  }), needsPagination(swiperParams) && /*#__PURE__*/React.createElement(\"div\", {\n    ref: paginationElRef,\n    className: \"swiper-pagination\"\n  }), slots['container-end']));\n});\nSwiper.displayName = 'Swiper';\n\nconst SwiperSlide = /*#__PURE__*/forwardRef(function (_temp, externalRef) {\n  let {\n    tag: Tag = 'div',\n    children,\n    className = '',\n    swiper,\n    zoom,\n    lazy,\n    virtualIndex,\n    swiperSlideIndex,\n    ...rest\n  } = _temp === void 0 ? {} : _temp;\n  const slideElRef = useRef(null);\n  const [slideClasses, setSlideClasses] = useState('swiper-slide');\n  const [lazyLoaded, setLazyLoaded] = useState(false);\n  function updateClasses(_s, el, classNames) {\n    if (el === slideElRef.current) {\n      setSlideClasses(classNames);\n    }\n  }\n  useIsomorphicLayoutEffect(() => {\n    if (typeof swiperSlideIndex !== 'undefined') {\n      slideElRef.current.swiperSlideIndex = swiperSlideIndex;\n    }\n    if (externalRef) {\n      externalRef.current = slideElRef.current;\n    }\n    if (!slideElRef.current || !swiper) {\n      return;\n    }\n    if (swiper.destroyed) {\n      if (slideClasses !== 'swiper-slide') {\n        setSlideClasses('swiper-slide');\n      }\n      return;\n    }\n    swiper.on('_slideClass', updateClasses);\n    // eslint-disable-next-line\n    return () => {\n      if (!swiper) return;\n      swiper.off('_slideClass', updateClasses);\n    };\n  });\n  useIsomorphicLayoutEffect(() => {\n    if (swiper && slideElRef.current && !swiper.destroyed) {\n      setSlideClasses(swiper.getSlideClasses(slideElRef.current));\n    }\n  }, [swiper]);\n  const slideData = {\n    isActive: slideClasses.indexOf('swiper-slide-active') >= 0,\n    isVisible: slideClasses.indexOf('swiper-slide-visible') >= 0,\n    isPrev: slideClasses.indexOf('swiper-slide-prev') >= 0,\n    isNext: slideClasses.indexOf('swiper-slide-next') >= 0\n  };\n  const renderChildren = () => {\n    return typeof children === 'function' ? children(slideData) : children;\n  };\n  const onLoad = () => {\n    setLazyLoaded(true);\n  };\n  return /*#__PURE__*/React.createElement(Tag, _extends({\n    ref: slideElRef,\n    className: uniqueClasses(`${slideClasses}${className ? ` ${className}` : ''}`),\n    \"data-swiper-slide-index\": virtualIndex,\n    onLoad: onLoad\n  }, rest), zoom && /*#__PURE__*/React.createElement(SwiperSlideContext.Provider, {\n    value: slideData\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-zoom-container\",\n    \"data-swiper-zoom\": typeof zoom === 'number' ? zoom : undefined\n  }, renderChildren(), lazy && !lazyLoaded && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-lazy-preloader\"\n  }))), !zoom && /*#__PURE__*/React.createElement(SwiperSlideContext.Provider, {\n    value: slideData\n  }, renderChildren(), lazy && !lazyLoaded && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"swiper-lazy-preloader\"\n  })));\n});\nSwiperSlide.displayName = 'SwiperSlide';\n\nexport { Swiper, SwiperSlide, useSwiper, useSwiperSlide };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;AAED;AACA;AACA;AACA;;;;;AAEA,SAAS;IACP,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI;IAW7C,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AAEA,SAAS,mBAAmB,KAAK;IAC/B,OAAO,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,WAAW,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;AACjF;AACA,SAAS,gBAAgB,CAAC;IACxB,MAAM,SAAS,EAAE;IACjB,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAA;QAChC,IAAI,mBAAmB,QAAQ;YAC7B,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,QAAQ,EAAE;YAC9C,gBAAgB,MAAM,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA,QAAS,OAAO,IAAI,CAAC;QACrE;IACF;IACA,OAAO;AACT;AACA,SAAS,YAAY,CAAC;IACpB,MAAM,SAAS,EAAE;IACjB,MAAM,QAAQ;QACZ,mBAAmB,EAAE;QACrB,iBAAiB,EAAE;QACnB,iBAAiB,EAAE;QACnB,eAAe,EAAE;IACnB;IACA,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAA;QAChC,IAAI,mBAAmB,QAAQ;YAC7B,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;YACrE,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QAC/B,OAAO,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,QAAQ,EAAE;YAC9C,MAAM,cAAc,gBAAgB,MAAM,KAAK,CAAC,QAAQ;YACxD,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,YAAY,OAAO,CAAC,CAAA,QAAS,OAAO,IAAI,CAAC;YAC3C,OAAO;gBACL,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC9B;QACF,OAAO;YACL,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAC9B;IACF;IACA,OAAO;QACL;QACA;IACF;AACF;AAEA,SAAS,cAAc,MAAM,EAAE,MAAM,EAAE,WAAW;IAChD,IAAI,CAAC,aAAa,OAAO;IACzB,MAAM,gBAAgB,CAAA;QACpB,IAAI,aAAa;QACjB,IAAI,QAAQ,GAAG;YACb,aAAa,OAAO,MAAM,GAAG;QAC/B,OAAO,IAAI,cAAc,OAAO,MAAM,EAAE;YACtC,2BAA2B;YAC3B,aAAa,aAAa,OAAO,MAAM;QACzC;QACA,OAAO;IACT;IACA,MAAM,QAAQ,OAAO,YAAY,KAAK;QACpC,CAAC,OAAO,YAAY,GAAG,UAAU,OAAO,EAAE,GAAG,YAAY,MAAM,CAAC,EAAE,CAAC;IACrE,IAAI;QACF,KAAK,GAAG,YAAY,MAAM,CAAC,EAAE,CAAC;IAChC;IACA,MAAM,EACJ,IAAI,EACJ,EAAE,EACH,GAAG;IACJ,MAAM,WAAW,OAAO,MAAM,CAAC,IAAI,GAAG,CAAC,OAAO,MAAM,GAAG;IACvD,MAAM,SAAS,OAAO,MAAM,CAAC,IAAI,GAAG,OAAO,MAAM,GAAG,IAAI,OAAO,MAAM;IACrE,MAAM,iBAAiB,EAAE;IACzB,IAAK,IAAI,IAAI,UAAU,IAAI,QAAQ,KAAK,EAAG;QACzC,IAAI,KAAK,QAAQ,KAAK,IAAI;YACxB,eAAe,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG;QAC9C;IACF;IACA,OAAO,eAAe,GAAG,CAAC,CAAC,OAAO;QAChC,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,YAAY,CAAC,OAAO;YAC5C;YACA;YACA,KAAK,MAAM,KAAK,CAAC,YAAY,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,OAAO;QAChE;IACF;AACF;AAEA,SAAS,0BAA0B,QAAQ,EAAE,IAAI;IAC/C,2BAA2B;IAC3B,IAAI,OAAO,WAAW,aAAa,OAAO,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,UAAU;IAC9D,OAAO,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE,UAAU;AACnC;AAEA,MAAM,qBAAqB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AACtD,MAAM,iBAAiB;IACrB,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AACA,MAAM,gBAAgB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AACjD,MAAM,YAAY;IAChB,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;AACpB;AAEA,MAAM,SAAS,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,SAAU,KAAK,EAAE,aAAa;IACnE,IAAI,EACF,SAAS,EACT,KAAK,MAAM,KAAK,EAChB,YAAY,aAAa,KAAK,EAC9B,QAAQ,EACR,QAAQ,EACR,GAAG,MACJ,GAAG,UAAU,KAAK,IAAI,CAAC,IAAI;IAC5B,IAAI,iBAAiB;IACrB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,EACJ,QAAQ,YAAY,EACpB,YAAY,EACZ,MAAM,SAAS,EACf,MAAM,EACP,GAAG,CAAA,GAAA,oKAAA,CAAA,IAAS,AAAD,EAAE;IACd,MAAM,EACJ,MAAM,EACN,KAAK,EACN,GAAG,YAAY;IAChB,MAAM,qBAAqB;QACzB,qBAAqB,CAAC;IACxB;IACA,OAAO,MAAM,CAAC,aAAa,EAAE,EAAE;QAC7B,mBAAkB,MAAM,EAAE,OAAO;YAC/B,oBAAoB;QACtB;IACF;IACA,MAAM,aAAa;QACjB,cAAc;QACd,OAAO,MAAM,CAAC,aAAa,EAAE,EAAE;QAC/B,iBAAiB;QACjB,MAAM,aAAa;YACjB,GAAG,YAAY;QACjB;QACA,OAAO,WAAW,YAAY;QAC9B,UAAU,OAAO,GAAG,IAAI,mJAAA,CAAA,IAAQ,CAAC;QACjC,IAAI,UAAU,OAAO,CAAC,OAAO,IAAI,UAAU,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;YACzE,UAAU,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG;YACnC,MAAM,aAAa;gBACjB,OAAO;gBACP;gBACA,gBAAgB;gBAChB,sBAAsB;YACxB;YACA,CAAA,GAAA,qJAAA,CAAA,IAAM,AAAD,EAAE,UAAU,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE;YACzC,CAAA,GAAA,qJAAA,CAAA,IAAM,AAAD,EAAE,UAAU,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE;QACnD;IACF;IACA,IAAI,CAAC,YAAY,OAAO,EAAE;QACxB;IACF;IAEA,gCAAgC;IAChC,IAAI,UAAU,OAAO,EAAE;QACrB,UAAU,OAAO,CAAC,EAAE,CAAC,qBAAqB;IAC5C;IACA,MAAM,eAAe;QACnB,IAAI,kBAAkB,CAAC,UAAU,CAAC,UAAU,OAAO,EAAE;QACrD,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,UAAU,OAAO,CAAC,EAAE,CAAC,WAAW,MAAM,CAAC,UAAU;QACnD;IACF;IACA,MAAM,eAAe;QACnB,IAAI,CAAC,UAAU,CAAC,UAAU,OAAO,EAAE;QACnC,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,UAAU,OAAO,CAAC,GAAG,CAAC,WAAW,MAAM,CAAC,UAAU;QACpD;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,UAAU,OAAO,EAAE,UAAU,OAAO,CAAC,GAAG,CAAC,qBAAqB;QACpE;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,OAAO,EAAE;YAChD,UAAU,OAAO,CAAC,iBAAiB;YACnC,eAAe,OAAO,GAAG;QAC3B;IACF;IAEA,eAAe;IACf,0BAA0B;QACxB,IAAI,eAAe;YACjB,cAAc,OAAO,GAAG,YAAY,OAAO;QAC7C;QACA,IAAI,CAAC,YAAY,OAAO,EAAE;QAC1B,IAAI,UAAU,OAAO,CAAC,SAAS,EAAE;YAC/B;QACF;QACA,CAAA,GAAA,oKAAA,CAAA,IAAW,AAAD,EAAE;YACV,IAAI,YAAY,OAAO;YACvB,QAAQ,UAAU,OAAO;YACzB,QAAQ,UAAU,OAAO;YACzB,cAAc,gBAAgB,OAAO;YACrC,aAAa,eAAe,OAAO;YACnC,QAAQ,UAAU,OAAO;QAC3B,GAAG;QACH,IAAI,YAAY,CAAC,UAAU,OAAO,CAAC,SAAS,EAAE,SAAS,UAAU,OAAO;QACxE,2BAA2B;QAC3B,OAAO;YACL,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,SAAS,EAAE;gBACrD,UAAU,OAAO,CAAC,OAAO,CAAC,MAAM;YAClC;QACF;IACF,GAAG,EAAE;IAEL,0BAA0B;IAC1B,0BAA0B;QACxB;QACA,MAAM,gBAAgB,CAAA,GAAA,oKAAA,CAAA,IAAgB,AAAD,EAAE,cAAc,mBAAmB,OAAO,EAAE,QAAQ,UAAU,OAAO,EAAE,CAAA,IAAK,EAAE,GAAG;QACtH,mBAAmB,OAAO,GAAG;QAC7B,UAAU,OAAO,GAAG;QACpB,IAAI,cAAc,MAAM,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,SAAS,EAAE;YAC7E,CAAA,GAAA,qJAAA,CAAA,IAAY,AAAD,EAAE;gBACX,QAAQ,UAAU,OAAO;gBACzB;gBACA;gBACA;gBACA,QAAQ,UAAU,OAAO;gBACzB,QAAQ,UAAU,OAAO;gBACzB,aAAa,eAAe,OAAO;gBACnC,cAAc,gBAAgB,OAAO;YACvC;QACF;QACA,OAAO;YACL;QACF;IACF;IAEA,2BAA2B;IAC3B,0BAA0B;QACxB,CAAA,GAAA,oKAAA,CAAA,IAAmB,AAAD,EAAE,UAAU,OAAO;IACvC,GAAG;QAAC;KAAY;IAEhB,mCAAmC;IACnC,SAAS;QACP,IAAI,aAAa,OAAO,EAAE;YACxB,OAAO,cAAc,UAAU,OAAO,EAAE,QAAQ;QAClD;QACA,OAAO,OAAO,GAAG,CAAC,CAAC,OAAO;YACxB,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,YAAY,CAAC,OAAO;gBAC5C,QAAQ,UAAU,OAAO;gBACzB,kBAAkB;YACpB;QACF;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK,SAAS;QACpD,KAAK;QACL,WAAW,CAAA,GAAA,qJAAA,CAAA,IAAa,AAAD,EAAE,GAAG,mBAAmB,YAAY,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IACnF,GAAG,YAAY,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,cAAc,QAAQ,EAAE;QACtE,OAAO,UAAU,OAAO;IAC1B,GAAG,KAAK,CAAC,kBAAkB,EAAE,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY;QACxE,WAAW,CAAA,GAAA,qJAAA,CAAA,IAAY,AAAD,EAAE,aAAa,YAAY;IACnD,GAAG,KAAK,CAAC,gBAAgB,EAAE,gBAAgB,KAAK,CAAC,cAAc,GAAG,CAAA,GAAA,qJAAA,CAAA,IAAe,AAAD,EAAE,iBAAiB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAChM,KAAK;QACL,WAAW;IACb,IAAI,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC1C,KAAK;QACL,WAAW;IACb,KAAK,CAAA,GAAA,qJAAA,CAAA,IAAc,AAAD,EAAE,iBAAiB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC3E,KAAK;QACL,WAAW;IACb,IAAI,CAAA,GAAA,qJAAA,CAAA,IAAe,AAAD,EAAE,iBAAiB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC3E,KAAK;QACL,WAAW;IACb,IAAI,KAAK,CAAC,gBAAgB;AAC5B;AACA,OAAO,WAAW,GAAG;AAErB,MAAM,cAAc,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,SAAU,KAAK,EAAE,WAAW;IACtE,IAAI,EACF,KAAK,MAAM,KAAK,EAChB,QAAQ,EACR,YAAY,EAAE,EACd,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,YAAY,EACZ,gBAAgB,EAChB,GAAG,MACJ,GAAG,UAAU,KAAK,IAAI,CAAC,IAAI;IAC5B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,SAAS,cAAc,EAAE,EAAE,EAAE,EAAE,UAAU;QACvC,IAAI,OAAO,WAAW,OAAO,EAAE;YAC7B,gBAAgB;QAClB;IACF;IACA,0BAA0B;QACxB,IAAI,OAAO,qBAAqB,aAAa;YAC3C,WAAW,OAAO,CAAC,gBAAgB,GAAG;QACxC;QACA,IAAI,aAAa;YACf,YAAY,OAAO,GAAG,WAAW,OAAO;QAC1C;QACA,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,QAAQ;YAClC;QACF;QACA,IAAI,OAAO,SAAS,EAAE;YACpB,IAAI,iBAAiB,gBAAgB;gBACnC,gBAAgB;YAClB;YACA;QACF;QACA,OAAO,EAAE,CAAC,eAAe;QACzB,2BAA2B;QAC3B,OAAO;YACL,IAAI,CAAC,QAAQ;YACb,OAAO,GAAG,CAAC,eAAe;QAC5B;IACF;IACA,0BAA0B;QACxB,IAAI,UAAU,WAAW,OAAO,IAAI,CAAC,OAAO,SAAS,EAAE;YACrD,gBAAgB,OAAO,eAAe,CAAC,WAAW,OAAO;QAC3D;IACF,GAAG;QAAC;KAAO;IACX,MAAM,YAAY;QAChB,UAAU,aAAa,OAAO,CAAC,0BAA0B;QACzD,WAAW,aAAa,OAAO,CAAC,2BAA2B;QAC3D,QAAQ,aAAa,OAAO,CAAC,wBAAwB;QACrD,QAAQ,aAAa,OAAO,CAAC,wBAAwB;IACvD;IACA,MAAM,iBAAiB;QACrB,OAAO,OAAO,aAAa,aAAa,SAAS,aAAa;IAChE;IACA,MAAM,SAAS;QACb,cAAc;IAChB;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK,SAAS;QACpD,KAAK;QACL,WAAW,CAAA,GAAA,qJAAA,CAAA,IAAa,AAAD,EAAE,GAAG,eAAe,YAAY,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;QAC7E,2BAA2B;QAC3B,QAAQ;IACV,GAAG,OAAO,QAAQ,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,mBAAmB,QAAQ,EAAE;QAC9E,OAAO;IACT,GAAG,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACzC,WAAW;QACX,oBAAoB,OAAO,SAAS,WAAW,OAAO;IACxD,GAAG,kBAAkB,QAAQ,CAAC,cAAc,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAClF,WAAW;IACb,MAAM,CAAC,QAAQ,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,mBAAmB,QAAQ,EAAE;QAC3E,OAAO;IACT,GAAG,kBAAkB,QAAQ,CAAC,cAAc,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAClF,WAAW;IACb;AACF;AACA,YAAY,WAAW,GAAG", "ignoreList": [0], "debugId": null}}]}