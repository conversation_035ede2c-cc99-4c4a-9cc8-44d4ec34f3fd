{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/src/app/login/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { useUser } from \"../../context/UserContext\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Link from \"next/link\";\r\n\r\nexport default function Login() {\r\n  const { login, isLoading } = useUser();\r\n  const router = useRouter();\r\n\r\n  const [email, setEmail] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [error, setError] = useState(\"\");\r\n\r\n  const handleLogin = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setError(\"\");\r\n\r\n    if (!email || !password) {\r\n      setError(\"Please enter email and password\");\r\n      return;\r\n    }\r\n\r\n    const result = await login(email, password);\r\n\r\n    if (result.success) {\r\n      router.push(\"/\");\r\n    } else {\r\n      setError(result.message || \"Login failed. Please try again.\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-md mx-auto mt-20 p-6 bg-white rounded shadow\">\r\n      <h1 className=\"text-2xl mb-6 font-semibold text-center\">Login</h1>\r\n\r\n      {error && <p className=\"text-red-500 mb-4\">{error}</p>}\r\n\r\n      <form onSubmit={handleLogin} className=\"space-y-4\">\r\n        <input\r\n          type=\"email\"\r\n          placeholder=\"Email\"\r\n          value={email}\r\n          onChange={(e) => setEmail(e.target.value)}\r\n          className=\"w-full border px-3 py-2 rounded\"\r\n        />\r\n        <input\r\n          type=\"password\"\r\n          placeholder=\"Password\"\r\n          value={password}\r\n          onChange={(e) => setPassword(e.target.value)}\r\n          className=\"w-full border px-3 py-2 rounded\"\r\n        />\r\n        <button\r\n          type=\"submit\"\r\n          disabled={isLoading}\r\n          className={`w-full py-2 rounded font-semibold text-white transition-colors ${\r\n            isLoading\r\n              ? 'bg-gray-400 cursor-not-allowed'\r\n              : 'bg-orange-500 hover:bg-orange-600 animate-bounce'\r\n          }`}\r\n        >\r\n          {isLoading ? 'Logging in...' : 'Login'}\r\n        </button>\r\n      </form>\r\n\r\n      <p className=\"mt-6 text-center text-sm text-gray-600\">\r\n        Don't have an account?{\" \"}\r\n        <Link href=\"/signup\" className=\"text-orange-500 hover:underline\">\r\n          Create one\r\n        </Link>\r\n      </p>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,SAAS;QAET,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,SAAS;YACT;QACF;QAEA,MAAM,SAAS,MAAM,MAAM,OAAO;QAElC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,SAAS,OAAO,OAAO,IAAI;QAC7B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0C;;;;;;YAEvD,uBAAS,8OAAC;gBAAE,WAAU;0BAAqB;;;;;;0BAE5C,8OAAC;gBAAK,UAAU;gBAAa,WAAU;;kCACrC,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,WAAU;;;;;;kCAEZ,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC3C,WAAU;;;;;;kCAEZ,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAW,CAAC,+DAA+D,EACzE,YACI,mCACA,oDACJ;kCAED,YAAY,kBAAkB;;;;;;;;;;;;0BAInC,8OAAC;gBAAE,WAAU;;oBAAyC;oBAC7B;kCACvB,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAU,WAAU;kCAAkC;;;;;;;;;;;;;;;;;;AAMzE", "debugId": null}}]}