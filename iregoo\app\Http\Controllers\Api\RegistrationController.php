<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Registration;
use App\Models\Conference;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;

class RegistrationController extends Controller
{
    public function store(Request $request)
    {
        // Check if registration is enabled
        if (!Setting::get('registration_enabled', true)) {
            return response()->json([
                'success' => false,
                'message' => 'Registration is currently closed'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:registrations,email,NULL,id,conference_id,' . $request->conference_id,
            'phone' => 'nullable|string|max:20',
            'country' => 'required|string|max:100',
            'city' => 'nullable|string|max:100',
            'organization' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'participant_type' => 'required|in:local,international',
            'registration_type' => 'required|in:presenter,attendee',
            'dietary_requirements' => 'nullable|string|max:500',
            'special_needs' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $conference = Conference::findOrFail($request->conference_id);

        // Check registration limit
        $maxRegistrations = Setting::get('max_registrations', 500);
        $currentRegistrations = Registration::where('conference_id', $conference->id)
            ->where('payment_status', '!=', 'cancelled')
            ->count();

        if ($currentRegistrations >= $maxRegistrations) {
            return response()->json([
                'success' => false,
                'message' => 'Registration limit reached'
            ], 403);
        }

        // Calculate registration fee
        $registrationFee = $request->participant_type === 'local' 
            ? $conference->registration_fee_local 
            : $conference->registration_fee_international;
        
        $currency = $request->participant_type === 'local' 
            ? $conference->currency_local 
            : $conference->currency_international;

        $registration = Registration::create([
            'conference_id' => $request->conference_id,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'country' => $request->country,
            'city' => $request->city,
            'organization' => $request->organization,
            'position' => $request->position,
            'participant_type' => $request->participant_type,
            'registration_type' => $request->registration_type,
            'dietary_requirements' => $request->dietary_requirements,
            'special_needs' => $request->special_needs,
            'registration_fee' => $registrationFee,
            'currency' => $currency,
            'payment_status' => 'pending',
        ]);

        // Generate confirmation code
        $registration->generateConfirmationCode();

        // Send confirmation email (you can implement this later)
        // Mail::to($registration->email)->send(new RegistrationConfirmation($registration));

        return response()->json([
            'success' => true,
            'message' => 'Registration submitted successfully',
            'data' => [
                'id' => $registration->id,
                'confirmation_code' => $registration->confirmation_code,
                'full_name' => $registration->full_name,
                'email' => $registration->email,
                'participant_type' => $registration->participant_type,
                'registration_type' => $registration->registration_type,
                'registration_fee' => $registration->registration_fee,
                'currency' => $registration->currency,
                'payment_status' => $registration->payment_status,
                'status' => $registration->status,
            ]
        ], 201);
    }

    public function show($confirmationCode)
    {
        $registration = Registration::where('confirmation_code', $confirmationCode)
            ->with('conference')
            ->firstOrFail();

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $registration->id,
                'confirmation_code' => $registration->confirmation_code,
                'full_name' => $registration->full_name,
                'email' => $registration->email,
                'phone' => $registration->phone,
                'country' => $registration->country,
                'city' => $registration->city,
                'organization' => $registration->organization,
                'position' => $registration->position,
                'participant_type' => $registration->participant_type,
                'registration_type' => $registration->registration_type,
                'registration_fee' => $registration->registration_fee,
                'currency' => $registration->currency,
                'payment_status' => $registration->payment_status,
                'status' => $registration->status,
                'is_confirmed' => $registration->is_confirmed,
                'confirmed_at' => $registration->confirmed_at,
                'checked_in' => $registration->checked_in,
                'checked_in_at' => $registration->checked_in_at,
                'conference' => [
                    'title' => $registration->conference->title,
                    'start_date' => $registration->conference->start_date,
                    'end_date' => $registration->conference->end_date,
                    'location' => $registration->conference->location,
                ],
                'created_at' => $registration->created_at,
            ]
        ]);
    }

    public function checkStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'confirmation_code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $registration = Registration::where('email', $request->email)
            ->where('confirmation_code', $request->confirmation_code)
            ->with('conference')
            ->first();

        if (!$registration) {
            return response()->json([
                'success' => false,
                'message' => 'Registration not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'confirmation_code' => $registration->confirmation_code,
                'full_name' => $registration->full_name,
                'email' => $registration->email,
                'status' => $registration->status,
                'payment_status' => $registration->payment_status,
                'is_confirmed' => $registration->is_confirmed,
                'checked_in' => $registration->checked_in,
                'conference_title' => $registration->conference->title,
            ]
        ]);
    }
}
