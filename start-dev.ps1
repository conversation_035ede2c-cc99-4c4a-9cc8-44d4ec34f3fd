# PowerShell script to start both Node.js backend and Next.js frontend

Write-Host "🚀 Starting IREGO Conference Development Environment..." -ForegroundColor Green

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Check if required ports are available
Write-Host "🔍 Checking ports..." -ForegroundColor Yellow

if (Test-Port 8001) {
    Write-Host "❌ Port 8001 is already in use. Please stop the service using this port." -ForegroundColor Red
    exit 1
}

if (Test-Port 3000) {
    Write-Host "❌ Port 3000 is already in use. Please stop the service using this port." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Ports 8001 and 3000 are available." -ForegroundColor Green

# Start Node.js backend
Write-Host "🔧 Starting Node.js Backend..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'backend'; Write-Host '🔥 Node.js Backend Starting...' -ForegroundColor Green; node server.js"

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start Next.js frontend
Write-Host "⚡ Starting Next.js Frontend..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'lipya1'; Write-Host '🌟 Next.js Frontend Starting...' -ForegroundColor Green; npm run dev"

# Wait a moment for Next.js to start
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "🎉 Development environment started successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Services:" -ForegroundColor Yellow
Write-Host "   🔗 Node.js API: http://localhost:8001" -ForegroundColor White
Write-Host "   🔗 Next.js App: http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "📝 API Health Check: http://localhost:8001/api/v1/health" -ForegroundColor Cyan
Write-Host ""
Write-Host "⚠️  To stop the services, close both PowerShell windows or press Ctrl+C in each." -ForegroundColor Yellow
Write-Host ""

# Keep the main script running
Write-Host "Press any key to exit this script (services will continue running)..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
