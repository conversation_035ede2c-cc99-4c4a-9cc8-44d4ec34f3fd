# PowerShell script to start both <PERSON><PERSON> backend and Next.js frontend

Write-Host "🚀 Starting IREGO Conference Development Environment..." -ForegroundColor Green

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Check if required ports are available
Write-Host "🔍 Checking ports..." -ForegroundColor Yellow

if (Test-Port 8000) {
    Write-Host "❌ Port 8000 is already in use. Please stop the service using this port." -ForegroundColor Red
    exit 1
}

if (Test-Port 3000) {
    Write-Host "❌ Port 3000 is already in use. Please stop the service using this port." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Ports 8000 and 3000 are available." -ForegroundColor Green

# Start Laravel backend
Write-Host "🔧 Starting Laravel Backend..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'iregoo'; Write-Host '🔥 Laravel Backend Starting...' -ForegroundColor Green; php artisan serve --host=0.0.0.0 --port=8000"

# Wait a moment for <PERSON><PERSON> to start
Start-Sleep -Seconds 3

# Start Next.js frontend
Write-Host "⚡ Starting Next.js Frontend..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'lipya1'; Write-Host '🌟 Next.js Frontend Starting...' -ForegroundColor Green; npm run dev"

# Wait a moment for Next.js to start
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "🎉 Development environment started successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Services:" -ForegroundColor Yellow
Write-Host "   🔗 Laravel API: http://localhost:8000" -ForegroundColor White
Write-Host "   🔗 Next.js App: http://localhost:3000" -ForegroundColor White
Write-Host ""
Write-Host "📝 API Documentation: http://localhost:8000/api/documentation" -ForegroundColor Cyan
Write-Host ""
Write-Host "⚠️  To stop the services, close both PowerShell windows or press Ctrl+C in each." -ForegroundColor Yellow
Write-Host ""

# Keep the main script running
Write-Host "Press any key to exit this script (services will continue running)..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
