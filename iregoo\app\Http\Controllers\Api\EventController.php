<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Event;
use Illuminate\Http\Request;
use Carbon\Carbon;

class EventController extends Controller
{
    public function index(Request $request)
    {
        $query = Event::active()->with(['conference', 'speakers']);

        // Filter by conference
        if ($request->has('conference_id')) {
            $query->where('conference_id', $request->conference_id);
        }

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by date
        if ($request->has('date')) {
            $query->whereDate('start_time', $request->date);
        }

        // Filter upcoming events
        if ($request->has('upcoming') && $request->upcoming) {
            $query->upcoming();
        }

        $events = $query->ordered()->get();

        return response()->json([
            'success' => true,
            'data' => $events->map(function($event) {
                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'description' => $event->description,
                    'type' => $event->type,
                    'start_time' => $event->start_time,
                    'end_time' => $event->end_time,
                    'location' => $event->location,
                    'room' => $event->room,
                    'duration' => $event->duration,
                    'time_range' => $event->time_range,
                    'max_participants' => $event->max_participants,
                    'current_participants' => $event->current_participants,
                    'available_spots' => $event->available_spots,
                    'is_parallel' => $event->is_parallel,
                    'speakers' => $event->speakers->map(function($speaker) {
                        return [
                            'id' => $speaker->id,
                            'name' => $speaker->full_name,
                            'position' => $speaker->position,
                            'organization' => $speaker->organization,
                            'photo_url' => $speaker->photo_url,
                        ];
                    }),
                    'conference' => [
                        'id' => $event->conference->id,
                        'title' => $event->conference->title,
                    ],
                ];
            })
        ]);
    }

    public function show($id)
    {
        $event = Event::active()
            ->with(['conference', 'speakers'])
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $event->id,
                'title' => $event->title,
                'description' => $event->description,
                'type' => $event->type,
                'start_time' => $event->start_time,
                'end_time' => $event->end_time,
                'location' => $event->location,
                'room' => $event->room,
                'duration' => $event->duration,
                'time_range' => $event->time_range,
                'max_participants' => $event->max_participants,
                'current_participants' => $event->current_participants,
                'available_spots' => $event->available_spots,
                'is_parallel' => $event->is_parallel,
                'speakers' => $event->speakers->map(function($speaker) {
                    return [
                        'id' => $speaker->id,
                        'name' => $speaker->full_name,
                        'position' => $speaker->position,
                        'organization' => $speaker->organization,
                        'bio' => $speaker->bio,
                        'photo_url' => $speaker->photo_url,
                        'role' => $speaker->pivot->role,
                    ];
                }),
                'conference' => [
                    'id' => $event->conference->id,
                    'title' => $event->conference->title,
                    'start_date' => $event->conference->start_date,
                    'end_date' => $event->conference->end_date,
                ],
            ]
        ]);
    }

    public function schedule(Request $request)
    {
        $query = Event::active()->with(['conference', 'speakers']);

        // Filter by conference
        if ($request->has('conference_id')) {
            $query->where('conference_id', $request->conference_id);
        }

        $events = $query->ordered()->get();

        // Group events by date
        $schedule = $events->groupBy(function($event) {
            return $event->start_time->format('Y-m-d');
        })->map(function($dayEvents, $date) {
            return [
                'date' => $date,
                'date_formatted' => Carbon::parse($date)->format('l, F j, Y'),
                'events' => $dayEvents->map(function($event) {
                    return [
                        'id' => $event->id,
                        'title' => $event->title,
                        'description' => $event->description,
                        'type' => $event->type,
                        'start_time' => $event->start_time,
                        'end_time' => $event->end_time,
                        'time_range' => $event->time_range,
                        'location' => $event->location,
                        'room' => $event->room,
                        'duration' => $event->duration,
                        'is_parallel' => $event->is_parallel,
                        'speakers' => $event->speakers->map(function($speaker) {
                            return [
                                'id' => $speaker->id,
                                'name' => $speaker->full_name,
                                'position' => $speaker->position,
                                'photo_url' => $speaker->photo_url,
                            ];
                        }),
                    ];
                })->values(),
            ];
        })->values();

        return response()->json([
            'success' => true,
            'data' => $schedule
        ]);
    }
}
