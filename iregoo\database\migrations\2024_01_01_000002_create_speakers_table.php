<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('speakers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conference_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('title')->nullable(); // Dr., Prof., etc.
            $table->string('position')->nullable();
            $table->string('organization')->nullable();
            $table->text('bio')->nullable();
            $table->string('photo')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('linkedin_url')->nullable();
            $table->string('twitter_url')->nullable();
            $table->string('website_url')->nullable();
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->enum('type', ['keynote', 'invited', 'committee_chair', 'committee_member'])->default('invited');
            $table->enum('committee_type', ['advisory', 'scientific', 'organizing'])->nullable();
            $table->boolean('is_featured')->default(false);
            $table->integer('display_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['conference_id', 'is_active']);
            $table->index(['type', 'committee_type']);
            $table->index('display_order');
        });
    }

    public function down()
    {
        Schema::dropIfExists('speakers');
    }
};
