<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Conference;
use App\Models\Registration;
use App\Models\Speaker;
use App\Models\Sponsor;
use App\Models\News;
use App\Models\Event;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // Get current conference
        $currentConference = Conference::active()->current()->first();
        
        if (!$currentConference) {
            $currentConference = Conference::active()->orderBy('start_date', 'desc')->first();
        }

        // Basic statistics
        $stats = [
            'total_conferences' => Conference::count(),
            'active_conferences' => Conference::active()->count(),
            'total_registrations' => Registration::count(),
            'confirmed_registrations' => Registration::confirmed()->count(),
            'paid_registrations' => Registration::paid()->count(),
            'total_speakers' => Speaker::count(),
            'active_speakers' => Speaker::active()->count(),
            'total_sponsors' => Sponsor::count(),
            'active_sponsors' => Sponsor::active()->count(),
            'total_news' => News::count(),
            'published_news' => News::published()->count(),
            'total_events' => Event::count(),
            'active_events' => Event::active()->count(),
        ];

        // Registration statistics for current conference
        $registrationStats = [];
        if ($currentConference) {
            $registrationStats = [
                'total' => Registration::where('conference_id', $currentConference->id)->count(),
                'confirmed' => Registration::where('conference_id', $currentConference->id)->confirmed()->count(),
                'paid' => Registration::where('conference_id', $currentConference->id)->paid()->count(),
                'checked_in' => Registration::where('conference_id', $currentConference->id)->checkedIn()->count(),
                'local' => Registration::where('conference_id', $currentConference->id)->local()->count(),
                'international' => Registration::where('conference_id', $currentConference->id)->international()->count(),
            ];
        }

        // Recent registrations
        $recentRegistrations = Registration::with('conference')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Registration trends (last 30 days)
        $registrationTrends = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $count = Registration::whereDate('created_at', $date)->count();
            $registrationTrends[] = [
                'date' => $date->format('Y-m-d'),
                'count' => $count,
            ];
        }

        // Payment status distribution
        $paymentStats = Registration::selectRaw('payment_status, COUNT(*) as count')
            ->groupBy('payment_status')
            ->pluck('count', 'payment_status')
            ->toArray();

        // Participant type distribution
        $participantStats = Registration::selectRaw('participant_type, COUNT(*) as count')
            ->groupBy('participant_type')
            ->pluck('count', 'participant_type')
            ->toArray();

        // Recent news
        $recentNews = News::with('conference')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Upcoming events
        $upcomingEvents = Event::with('conference')
            ->upcoming()
            ->orderBy('start_time')
            ->limit(5)
            ->get();

        return view('admin.dashboard.index', compact(
            'currentConference',
            'stats',
            'registrationStats',
            'recentRegistrations',
            'registrationTrends',
            'paymentStats',
            'participantStats',
            'recentNews',
            'upcomingEvents'
        ));
    }

    public function stats()
    {
        // API endpoint for dashboard stats
        $currentConference = Conference::active()->current()->first();
        
        $stats = [
            'conferences' => [
                'total' => Conference::count(),
                'active' => Conference::active()->count(),
            ],
            'registrations' => [
                'total' => Registration::count(),
                'confirmed' => Registration::confirmed()->count(),
                'paid' => Registration::paid()->count(),
                'checked_in' => Registration::checkedIn()->count(),
            ],
            'speakers' => [
                'total' => Speaker::count(),
                'active' => Speaker::active()->count(),
                'keynotes' => Speaker::where('type', 'keynote')->count(),
            ],
            'sponsors' => [
                'total' => Sponsor::count(),
                'active' => Sponsor::active()->count(),
            ],
            'news' => [
                'total' => News::count(),
                'published' => News::published()->count(),
            ],
            'events' => [
                'total' => Event::count(),
                'active' => Event::active()->count(),
                'upcoming' => Event::upcoming()->count(),
            ],
        ];

        if ($currentConference) {
            $stats['current_conference'] = [
                'id' => $currentConference->id,
                'title' => $currentConference->title,
                'start_date' => $currentConference->start_date,
                'registrations' => Registration::where('conference_id', $currentConference->id)->count(),
                'speakers' => Speaker::where('conference_id', $currentConference->id)->count(),
                'sponsors' => Sponsor::where('conference_id', $currentConference->id)->count(),
                'events' => Event::where('conference_id', $currentConference->id)->count(),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
