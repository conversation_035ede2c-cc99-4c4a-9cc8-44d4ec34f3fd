<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('registrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conference_id')->constrained()->onDelete('cascade');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email');
            $table->string('phone')->nullable();
            $table->string('country');
            $table->string('city')->nullable();
            $table->string('organization')->nullable();
            $table->string('position')->nullable();
            $table->enum('participant_type', ['local', 'international'])->default('local');
            $table->enum('registration_type', ['presenter', 'attendee'])->default('attendee');
            $table->text('dietary_requirements')->nullable();
            $table->text('special_needs')->nullable();
            $table->enum('payment_status', ['pending', 'paid', 'cancelled'])->default('pending');
            $table->string('payment_method')->nullable();
            $table->string('payment_reference')->nullable();
            $table->decimal('registration_fee', 10, 2)->nullable();
            $table->string('currency', 3)->nullable();
            $table->string('confirmation_code')->unique()->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_confirmed')->default(false);
            $table->timestamp('confirmed_at')->nullable();
            $table->boolean('checked_in')->default(false);
            $table->timestamp('checked_in_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['conference_id', 'email']);
            $table->index(['payment_status', 'is_confirmed']);
            $table->index('participant_type');
            $table->index('confirmation_code');
        });
    }

    public function down()
    {
        Schema::dropIfExists('registrations');
    }
};
