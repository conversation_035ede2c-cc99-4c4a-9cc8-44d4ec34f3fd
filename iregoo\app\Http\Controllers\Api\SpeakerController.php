<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Speaker;
use Illuminate\Http\Request;

class SpeakerController extends Controller
{
    public function index(Request $request)
    {
        $query = Speaker::active()->with('conference');

        // Filter by conference
        if ($request->has('conference_id')) {
            $query->where('conference_id', $request->conference_id);
        }

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by committee type
        if ($request->has('committee_type')) {
            $query->where('committee_type', $request->committee_type);
        }

        // Filter featured speakers
        if ($request->has('featured') && $request->featured) {
            $query->where('is_featured', true);
        }

        $speakers = $query->ordered()->get();

        return response()->json([
            'success' => true,
            'data' => $speakers->map(function($speaker) {
                return [
                    'id' => $speaker->id,
                    'name' => $speaker->full_name,
                    'position' => $speaker->position,
                    'organization' => $speaker->organization,
                    'bio' => $speaker->bio,
                    'photo_url' => $speaker->photo_url,
                    'email' => $speaker->email,
                    'linkedin_url' => $speaker->linkedin_url,
                    'twitter_url' => $speaker->twitter_url,
                    'website_url' => $speaker->website_url,
                    'country' => $speaker->country,
                    'city' => $speaker->city,
                    'type' => $speaker->type,
                    'committee_type' => $speaker->committee_type,
                    'is_featured' => $speaker->is_featured,
                    'conference' => [
                        'id' => $speaker->conference->id,
                        'title' => $speaker->conference->title,
                    ],
                ];
            })
        ]);
    }

    public function show($id)
    {
        $speaker = Speaker::active()
            ->with(['conference', 'events' => function($query) {
                $query->active()->ordered();
            }])
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $speaker->id,
                'name' => $speaker->full_name,
                'position' => $speaker->position,
                'organization' => $speaker->organization,
                'bio' => $speaker->bio,
                'photo_url' => $speaker->photo_url,
                'email' => $speaker->email,
                'linkedin_url' => $speaker->linkedin_url,
                'twitter_url' => $speaker->twitter_url,
                'website_url' => $speaker->website_url,
                'country' => $speaker->country,
                'city' => $speaker->city,
                'type' => $speaker->type,
                'committee_type' => $speaker->committee_type,
                'is_featured' => $speaker->is_featured,
                'conference' => [
                    'id' => $speaker->conference->id,
                    'title' => $speaker->conference->title,
                    'start_date' => $speaker->conference->start_date,
                    'end_date' => $speaker->conference->end_date,
                ],
                'events' => $speaker->events->map(function($event) {
                    return [
                        'id' => $event->id,
                        'title' => $event->title,
                        'type' => $event->type,
                        'start_time' => $event->start_time,
                        'end_time' => $event->end_time,
                        'location' => $event->location,
                        'room' => $event->room,
                    ];
                }),
            ]
        ]);
    }

    public function committees(Request $request)
    {
        $query = Speaker::active()
            ->whereIn('type', ['committee_chair', 'committee_member'])
            ->with('conference');

        // Filter by conference
        if ($request->has('conference_id')) {
            $query->where('conference_id', $request->conference_id);
        }

        // Filter by committee type
        if ($request->has('committee_type')) {
            $query->where('committee_type', $request->committee_type);
        }

        $speakers = $query->ordered()->get();

        // Group by committee type
        $committees = $speakers->groupBy('committee_type')->map(function($members, $type) {
            return [
                'type' => $type,
                'name' => ucfirst($type) . ' Committee',
                'members' => $members->map(function($speaker) {
                    return [
                        'id' => $speaker->id,
                        'name' => $speaker->full_name,
                        'position' => $speaker->position,
                        'organization' => $speaker->organization,
                        'photo_url' => $speaker->photo_url,
                        'type' => $speaker->type,
                        'is_chair' => $speaker->type === 'committee_chair',
                    ];
                })->values(),
            ];
        })->values();

        return response()->json([
            'success' => true,
            'data' => $committees
        ]);
    }

    public function keynotes(Request $request)
    {
        $query = Speaker::active()
            ->where('type', 'keynote')
            ->with('conference');

        // Filter by conference
        if ($request->has('conference_id')) {
            $query->where('conference_id', $request->conference_id);
        }

        $speakers = $query->ordered()->get();

        return response()->json([
            'success' => true,
            'data' => $speakers->map(function($speaker) {
                return [
                    'id' => $speaker->id,
                    'name' => $speaker->full_name,
                    'position' => $speaker->position,
                    'organization' => $speaker->organization,
                    'bio' => $speaker->bio,
                    'photo_url' => $speaker->photo_url,
                    'country' => $speaker->country,
                    'city' => $speaker->city,
                ];
            })
        ]);
    }
}
