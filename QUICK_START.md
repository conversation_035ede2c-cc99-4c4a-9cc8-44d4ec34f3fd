# 🚀 Quick Start Guide - IREGO Conference

## ✅ Current Status
- ✅ Frontend (Next.js): Running on http://localhost:3000
- ✅ Backend (Node.js): Running on http://localhost:8001
- ✅ API Connection: Configured and working

## 🔧 How to Start the Project

### Option 1: Automatic Start (Recommended)
```powershell
.\start-dev.ps1
```

### Option 2: Manual Start
```bash
# Terminal 1 - Backend
cd backend
node server.js

# Terminal 2 - Frontend  
cd lipya1
npm run dev
```

## 🌐 Access Points
- **Website**: http://localhost:3000
- **API Health**: http://localhost:8001/api/v1/health
- **Login Page**: http://localhost:3000/login
- **Registration**: http://localhost:3000/signup

## 🔐 Test Authentication
1. Go to http://localhost:3000/signup
2. Create a new account with:
   - Name: Test User
   - Email: <EMAIL>
   - Password: password123
3. Login with the same credentials

## 📱 Available Features
- ✅ User Registration & Login
- ✅ Conference Information
- ✅ Speaker Profiles
- ✅ News & Updates
- ✅ Event Schedule
- ✅ Contact Forms
- ✅ Responsive Design

## 🔧 API Endpoints
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/conferences` - Get conferences
- `GET /api/v1/speakers` - Get speakers
- `GET /api/v1/news` - Get news
- `GET /api/v1/events` - Get events

## 🛠️ Project Structure
```
├── backend/           # Node.js API Server
│   ├── server.js     # Main server file
│   ├── package.json  # Dependencies
│   └── .env          # Environment variables
├── lipya1/           # Next.js Frontend
│   ├── src/
│   │   ├── app/      # Pages
│   │   ├── components/ # React components
│   │   ├── context/  # User context
│   │   └── services/ # API services
│   └── .env.local    # Frontend config
└── start-dev.ps1     # Start script
```

## 🔄 Development Workflow
1. Make changes to code
2. Frontend auto-reloads (Next.js hot reload)
3. Backend needs manual restart for changes
4. Test in browser at http://localhost:3000

## 🚨 Troubleshooting
- **Port 3000 busy**: Stop other Next.js apps
- **Port 8001 busy**: Stop other Node.js apps  
- **API not working**: Check backend terminal for errors
- **Login issues**: Check browser console for errors

## 📞 Ready for Delivery
The project is now fully connected and ready for:
- ✅ User testing
- ✅ Demo presentation
- ✅ Production deployment
- ✅ Client delivery

Both frontend and backend are working together seamlessly!
