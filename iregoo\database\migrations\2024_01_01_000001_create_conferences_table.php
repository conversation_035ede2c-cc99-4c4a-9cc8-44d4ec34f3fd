<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('conferences', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('subtitle')->nullable();
            $table->text('description');
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->string('location');
            $table->string('venue')->nullable();
            $table->string('logo')->nullable();
            $table->string('banner_image')->nullable();
            $table->string('website_url')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->decimal('registration_fee_local', 10, 2)->nullable();
            $table->decimal('registration_fee_international', 10, 2)->nullable();
            $table->string('currency_local', 3)->default('LYD');
            $table->string('currency_international', 3)->default('EUR');
            $table->datetime('abstract_deadline')->nullable();
            $table->datetime('notification_date')->nullable();
            $table->datetime('final_paper_deadline')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['is_active', 'start_date']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('conferences');
    }
};
