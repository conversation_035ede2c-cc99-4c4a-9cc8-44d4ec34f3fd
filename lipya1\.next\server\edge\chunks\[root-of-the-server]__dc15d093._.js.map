{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// Define protected routes that require authentication\nconst protectedRoutes = [\n  '/dashboard',\n  '/profile',\n  '/admin',\n];\n\n// Define auth routes that should redirect to home if user is already authenticated\nconst authRoutes = [\n  '/login',\n  '/signup',\n];\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n  \n  // Check if user has auth token\n  const token = request.cookies.get('auth_token')?.value;\n  const isAuthenticated = !!token;\n\n  // Check if the current path is protected\n  const isProtectedRoute = protectedRoutes.some(route => \n    pathname.startsWith(route)\n  );\n\n  // Check if the current path is an auth route\n  const isAuthRoute = authRoutes.some(route => \n    pathname.startsWith(route)\n  );\n\n  // Redirect to login if trying to access protected route without authentication\n  if (isProtectedRoute && !isAuthenticated) {\n    const loginUrl = new URL('/login', request.url);\n    loginUrl.searchParams.set('redirect', pathname);\n    return NextResponse.redirect(loginUrl);\n  }\n\n  // Redirect to home if trying to access auth routes while authenticated\n  if (isAuthRoute && isAuthenticated) {\n    return NextResponse.redirect(new URL('/', request.url));\n  }\n\n  // Add security headers\n  const response = NextResponse.next();\n  \n  // Security headers\n  response.headers.set('X-Frame-Options', 'DENY');\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  response.headers.set('X-XSS-Protection', '1; mode=block');\n  \n  // CORS headers for API routes\n  if (pathname.startsWith('/api/')) {\n    response.headers.set('Access-Control-Allow-Origin', process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000');\n    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');\n    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');\n    response.headers.set('Access-Control-Allow-Credentials', 'true');\n  }\n\n  return response;\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,sDAAsD;AACtD,MAAM,kBAAkB;IACtB;IACA;IACA;CACD;AAED,mFAAmF;AACnF,MAAM,aAAa;IACjB;IACA;CACD;AAEM,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,+BAA+B;IAC/B,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,MAAM,kBAAkB,CAAC,CAAC;IAE1B,yCAAyC;IACzC,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,SAAS,UAAU,CAAC;IAGtB,6CAA6C;IAC7C,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,QAClC,SAAS,UAAU,CAAC;IAGtB,+EAA+E;IAC/E,IAAI,oBAAoB,CAAC,iBAAiB;QACxC,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC9C,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,uEAAuE;IACvE,IAAI,eAAe,iBAAiB;QAClC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;IACvD;IAEA,uBAAuB;IACvB,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,mBAAmB;IACnB,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;IAEzC,8BAA8B;IAC9B,IAAI,SAAS,UAAU,CAAC,UAAU;QAChC,SAAS,OAAO,CAAC,GAAG,CAAC,+BAA+B,6DAAwC;QAC5F,SAAS,OAAO,CAAC,GAAG,CAAC,gCAAgC;QACrD,SAAS,OAAO,CAAC,GAAG,CAAC,gCAAgC;QACrD,SAAS,OAAO,CAAC,GAAG,CAAC,oCAAoC;IAC3D;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}