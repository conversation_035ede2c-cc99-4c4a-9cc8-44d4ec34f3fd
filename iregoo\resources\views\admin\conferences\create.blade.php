@extends('layouts.admin')

@section('title', 'Create Conference')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-plus"></i> Create New Conference
    </h1>
    <a href="{{ route('admin.conferences.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Conferences
    </a>
</div>

<form action="{{ route('admin.conferences.store') }}" method="POST" enctype="multipart/form-data">
    @csrf
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Conference Title *</label>
                        <input type="text" 
                               class="form-control @error('title') is-invalid @enderror" 
                               id="title" 
                               name="title" 
                               value="{{ old('title') }}" 
                               required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="subtitle" class="form-label">Subtitle</label>
                        <input type="text" 
                               class="form-control @error('subtitle') is-invalid @enderror" 
                               id="subtitle" 
                               name="subtitle" 
                               value="{{ old('subtitle') }}">
                        @error('subtitle')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description *</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="5" 
                                  required>{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date *</label>
                                <input type="datetime-local" 
                                       class="form-control @error('start_date') is-invalid @enderror" 
                                       id="start_date" 
                                       name="start_date" 
                                       value="{{ old('start_date') }}" 
                                       required>
                                @error('start_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">End Date *</label>
                                <input type="datetime-local" 
                                       class="form-control @error('end_date') is-invalid @enderror" 
                                       id="end_date" 
                                       name="end_date" 
                                       value="{{ old('end_date') }}" 
                                       required>
                                @error('end_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">Location *</label>
                                <input type="text" 
                                       class="form-control @error('location') is-invalid @enderror" 
                                       id="location" 
                                       name="location" 
                                       value="{{ old('location') }}" 
                                       required>
                                @error('location')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="venue" class="form-label">Venue</label>
                                <input type="text" 
                                       class="form-control @error('venue') is-invalid @enderror" 
                                       id="venue" 
                                       name="venue" 
                                       value="{{ old('venue') }}">
                                @error('venue')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Contact Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" 
                                       class="form-control @error('email') is-invalid @enderror" 
                                       id="email" 
                                       name="email" 
                                       value="{{ old('email') }}">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="text" 
                                       class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" 
                                       name="phone" 
                                       value="{{ old('phone') }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="website_url" class="form-label">Website URL</label>
                        <input type="url" 
                               class="form-control @error('website_url') is-invalid @enderror" 
                               id="website_url" 
                               name="website_url" 
                               value="{{ old('website_url') }}">
                        @error('website_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Registration Fees -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Registration Fees</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registration_fee_local" class="form-label">Local Fee</label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control @error('registration_fee_local') is-invalid @enderror" 
                                           id="registration_fee_local" 
                                           name="registration_fee_local" 
                                           value="{{ old('registration_fee_local') }}" 
                                           step="0.01" 
                                           min="0">
                                    <input type="text" 
                                           class="form-control @error('currency_local') is-invalid @enderror" 
                                           name="currency_local" 
                                           value="{{ old('currency_local', 'LYD') }}" 
                                           placeholder="Currency" 
                                           style="max-width: 80px;">
                                </div>
                                @error('registration_fee_local')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="registration_fee_international" class="form-label">International Fee</label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control @error('registration_fee_international') is-invalid @enderror" 
                                           id="registration_fee_international" 
                                           name="registration_fee_international" 
                                           value="{{ old('registration_fee_international') }}" 
                                           step="0.01" 
                                           min="0">
                                    <input type="text" 
                                           class="form-control @error('currency_international') is-invalid @enderror" 
                                           name="currency_international" 
                                           value="{{ old('currency_international', 'EUR') }}" 
                                           placeholder="Currency" 
                                           style="max-width: 80px;">
                                </div>
                                @error('registration_fee_international')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Images -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Images</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="logo" class="form-label">Logo</label>
                        <input type="file" 
                               class="form-control @error('logo') is-invalid @enderror" 
                               id="logo" 
                               name="logo" 
                               accept="image/*">
                        @error('logo')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Max size: 2MB. Formats: JPG, PNG, GIF</small>
                    </div>

                    <div class="mb-3">
                        <label for="banner_image" class="form-label">Banner Image</label>
                        <input type="file" 
                               class="form-control @error('banner_image') is-invalid @enderror" 
                               id="banner_image" 
                               name="banner_image" 
                               accept="image/*">
                        @error('banner_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="form-text text-muted">Max size: 5MB. Formats: JPG, PNG, GIF</small>
                    </div>
                </div>
            </div>

            <!-- Important Dates -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Important Dates</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="abstract_deadline" class="form-label">Abstract Deadline</label>
                        <input type="datetime-local" 
                               class="form-control @error('abstract_deadline') is-invalid @enderror" 
                               id="abstract_deadline" 
                               name="abstract_deadline" 
                               value="{{ old('abstract_deadline') }}">
                        @error('abstract_deadline')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="notification_date" class="form-label">Notification Date</label>
                        <input type="datetime-local" 
                               class="form-control @error('notification_date') is-invalid @enderror" 
                               id="notification_date" 
                               name="notification_date" 
                               value="{{ old('notification_date') }}">
                        @error('notification_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="final_paper_deadline" class="form-label">Final Paper Deadline</label>
                        <input type="datetime-local" 
                               class="form-control @error('final_paper_deadline') is-invalid @enderror" 
                               id="final_paper_deadline" 
                               name="final_paper_deadline" 
                               value="{{ old('final_paper_deadline') }}">
                        @error('final_paper_deadline')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Status -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Status</h5>
                </div>
                <div class="card-body">
                    <div class="form-check">
                        <input class="form-check-input" 
                               type="checkbox" 
                               id="is_active" 
                               name="is_active" 
                               value="1" 
                               {{ old('is_active', true) ? 'checked' : '' }}>
                        <label class="form-check-label" for="is_active">
                            Active Conference
                        </label>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card mt-4">
                <div class="card-body">
                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-save"></i> Create Conference
                    </button>
                    <a href="{{ route('admin.conferences.index') }}" class="btn btn-secondary w-100">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection
