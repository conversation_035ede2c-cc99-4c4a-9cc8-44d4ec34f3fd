@extends('layouts.admin')

@section('title', 'Registrations')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-user-plus"></i> Registrations
    </h1>
    <div>
        <div class="btn-group me-2">
            <a href="{{ route('admin.registrations.export.excel') }}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Export Excel
            </a>
            <a href="{{ route('admin.registrations.export.csv') }}" class="btn btn-outline-success">
                <i class="fas fa-file-csv"></i> Export CSV
            </a>
        </div>
        <a href="{{ route('admin.registrations.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Registration
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ $stats['total'] }}</h4>
                <small class="text-muted">Total</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">{{ $stats['confirmed'] }}</h4>
                <small class="text-muted">Confirmed</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning">{{ $stats['paid'] }}</h4>
                <small class="text-muted">Paid</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">{{ $stats['checked_in'] }}</h4>
                <small class="text-muted">Checked In</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-secondary">{{ $stats['local'] }}</h4>
                <small class="text-muted">Local</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-dark">{{ $stats['international'] }}</h4>
                <small class="text-muted">International</small>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.registrations.index') }}">
            <div class="row">
                <div class="col-md-3">
                    <select name="conference_id" class="form-select">
                        <option value="">All Conferences</option>
                        @foreach($conferences as $conference)
                            <option value="{{ $conference->id }}" 
                                    {{ request('conference_id') == $conference->id ? 'selected' : '' }}>
                                {{ $conference->title }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="payment_status" class="form-select">
                        <option value="">All Payment Status</option>
                        <option value="pending" {{ request('payment_status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="paid" {{ request('payment_status') == 'paid' ? 'selected' : '' }}>Paid</option>
                        <option value="cancelled" {{ request('payment_status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="participant_type" class="form-select">
                        <option value="">All Types</option>
                        <option value="local" {{ request('participant_type') == 'local' ? 'selected' : '' }}>Local</option>
                        <option value="international" {{ request('participant_type') == 'international' ? 'selected' : '' }}>International</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="is_confirmed" class="form-select">
                        <option value="">All Confirmations</option>
                        <option value="1" {{ request('is_confirmed') == '1' ? 'selected' : '' }}>Confirmed</option>
                        <option value="0" {{ request('is_confirmed') == '0' ? 'selected' : '' }}>Not Confirmed</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="text" 
                           name="search" 
                           class="form-control" 
                           placeholder="Search..." 
                           value="{{ request('search') }}">
                </div>
                <div class="col-md-1">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        @if($registrations->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Participant</th>
                            <th>Contact</th>
                            <th>Conference</th>
                            <th>Type</th>
                            <th>Payment</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($registrations as $registration)
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ $registration->full_name }}</strong>
                                    @if($registration->organization)
                                        <br><small class="text-muted">{{ $registration->organization }}</small>
                                    @endif
                                    @if($registration->position)
                                        <br><small class="text-muted">{{ $registration->position }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <i class="fas fa-envelope"></i> {{ $registration->email }}
                                    @if($registration->phone)
                                        <br><i class="fas fa-phone"></i> {{ $registration->phone }}
                                    @endif
                                    <br><i class="fas fa-map-marker-alt"></i> {{ $registration->country }}
                                </div>
                            </td>
                            <td>
                                <small>{{ $registration->conference->title }}</small>
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    {{ ucfirst($registration->participant_type) }}
                                </span>
                                <br>
                                <span class="badge bg-secondary mt-1">
                                    {{ ucfirst($registration->registration_type) }}
                                </span>
                            </td>
                            <td>
                                @if($registration->payment_status === 'paid')
                                    <span class="badge bg-success">Paid</span>
                                @elseif($registration->payment_status === 'pending')
                                    <span class="badge bg-warning">Pending</span>
                                @else
                                    <span class="badge bg-danger">Cancelled</span>
                                @endif
                                
                                @if($registration->registration_fee)
                                    <br><small class="text-muted">
                                        {{ $registration->registration_fee }} {{ $registration->currency }}
                                    </small>
                                @endif
                            </td>
                            <td>
                                @if($registration->is_confirmed)
                                    <span class="badge bg-success">Confirmed</span>
                                @else
                                    <span class="badge bg-secondary">Pending</span>
                                @endif
                                
                                @if($registration->checked_in)
                                    <br><span class="badge bg-primary mt-1">Checked In</span>
                                @endif
                                
                                @if($registration->confirmation_code)
                                    <br><small class="text-muted">{{ $registration->confirmation_code }}</small>
                                @endif
                            </td>
                            <td>
                                <small>{{ $registration->created_at->format('M j, Y') }}</small>
                                <br><small class="text-muted">{{ $registration->created_at->format('H:i') }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.registrations.show', $registration) }}" 
                                       class="btn btn-sm btn-outline-info" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.registrations.edit', $registration) }}" 
                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    @if(!$registration->is_confirmed)
                                        <form action="{{ route('admin.registrations.confirm', $registration) }}" 
                                              method="POST" class="d-inline">
                                            @csrf
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-success" 
                                                    title="Confirm"
                                                    onclick="return confirm('Confirm this registration?')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    @endif
                                    
                                    @if($registration->is_confirmed && !$registration->checked_in)
                                        <form action="{{ route('admin.registrations.checkin', $registration) }}" 
                                              method="POST" class="d-inline">
                                            @csrf
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-warning" 
                                                    title="Check In"
                                                    onclick="return confirm('Check in this participant?')">
                                                <i class="fas fa-sign-in-alt"></i>
                                            </button>
                                        </form>
                                    @endif
                                    
                                    <form action="{{ route('admin.registrations.destroy', $registration) }}" 
                                          method="POST" 
                                          class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this registration?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $registrations->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-user-plus fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Registrations Found</h4>
                <p class="text-muted">No registrations match your current filters.</p>
                <a href="{{ route('admin.registrations.index') }}" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i> Clear Filters
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
