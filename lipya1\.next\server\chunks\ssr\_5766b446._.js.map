{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/src/app/about/page.tsx"], "sourcesContent": ["// components/About.tsx\r\nimport React from 'react';\r\n\r\nexport default function About() {\r\n  return (\r\n    <div>\r\n      {/* صورة الهيرو */}\r\n      <div className=\"relative h-[300px] md:h-[400px] w-full\">\r\n        <img\r\n          src=\"/photo1.png\"\r\n          alt=\"About IREGO\"\r\n          className=\"object-cover w-full h-full\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n          <h1 className=\"text-3xl md:text-5xl font-bold text-white\">\r\n            About <span className=\"text-orange-500\">IREGO</span>\r\n          </h1>\r\n        </div>\r\n      </div>\r\n\r\n      {/* المحتوى تحت الصورة */}\r\n      <div className=\"max-w-7xl mx-auto px-4 py-12 flex flex-col md:flex-row justify-between gap-8 text-gray-800\">\r\n        {/* الجهة الشمال */}\r\n        <div className=\"flex flex-col items-center relative w-max mx-auto\">\r\n          <span className=\"text-4xl font-bold text-black relative\" style={{ left: '5%' }}>\r\n            About\r\n          </span>\r\n          <span className=\"text-4xl font-bold text-orange-500\">\r\n            The Conference\r\n          </span>\r\n        </div>\r\n\r\n        {/* الجهة اليمين */}\r\n        <p className=\"max-w-xl text-md leading-relaxed\">\r\n          The Renewable Energy, Gas & Oil, and Climate Change Conference will be held in Tripoli, Libya, in November 2025.\r\n          Bringing together global experts, researchers, and policymakers, the event aims to foster collaboration and drive innovative solutions for sustainable energy and climate resilience.\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"max-w-7xl mx-auto px-4 py-12 flex flex-col md:flex-row items-start gap-8\">\r\n        {/* الصورة على اليسار */}\r\n        <div className=\"w-full md:w-1/2\">\r\n          <img\r\n            src=\"/photo9.jpg\"\r\n            alt=\"Conference Visual\"\r\n            className=\"w-full h-auto rounded-lg\"\r\n          />\r\n        </div>\r\n\r\n        {/* النصوص على اليمين */}\r\n        <div className=\"w-full md:w-1/2 space-y-6\">\r\n          <div>\r\n            <h3 className=\"text-orange-500 text-lg font-semibold uppercase\">01- Academic Engagement and Research Contributions</h3>\r\n            <p className=\"mt-2 text-gray-700 leading-relaxed\">\r\n              The conference will feature research paper presentations on renewable energy, oil and gas, and climate change. These contributions will support local and international scientific progress and promote knowledge exchange among experts.\r\n            </p>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-orange-500 text-lg font-semibold uppercase\">02- Industry Exhibition and Technological Innovation</h3>\r\n            <p className=\"mt-2 text-gray-700 leading-relaxed\">\r\n              A large exhibition will showcase the latest energy and environmental technologies, offering companies and innovators a chance to present their work and connect with global stakeholders.\r\n            </p>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-orange-500 text-lg font-semibold uppercase\">03- Dialogue, Collaboration, and Sustainable Development</h3>\r\n            <p className=\"mt-2 text-gray-700 leading-relaxed\">\r\n              The conference will feature discussions, workshops, and networking to promote sustainable energy, environmental responsibility, and alignment with national goals and the UN SDGs.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* مهمتنا */}\r\n      <div className=\"flex justify-center w-full px-4 py-12\">\r\n        <div className=\"w-full md:w-1/2 text-center\">\r\n          <h2 className=\"text-3xl font-bold uppercase mb-4\">\r\n            OUR <span className=\"text-orange-500\">MISSION</span>\r\n          </h2>\r\n          <p className=\"text-gray-700 leading-relaxed\">\r\n            To foster the exchange of knowledge and experiences among diverse stakeholders in the energy sector, promoting innovation and driving the development of practical solutions to tackle global environmental and energy challenges.\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* رؤيتنا */}\r\n      <div className=\"flex justify-center w-full px-4 py-12\">\r\n        <div className=\"w-full md:w-1/2 text-center\">\r\n          <h2 className=\"text-3xl font-bold uppercase mb-4\">\r\n            OUR <span className=\"text-orange-500\">Vision</span>\r\n          </h2>\r\n          <p className=\"text-gray-700 leading-relaxed\">\r\n           To establish the conference as a premier and innovative platform in the energy field, facilitating global dialogue and spearheading the transition to sustainable energy systems. Through these efforts, we aim to contribute to achieving sustainable development goals and advancing climate change adaptation\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n  {/* النموذج */}\r\n  <form className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n    {/* الاسم */}\r\n    <div className=\"flex items-center border border-gray-300 rounded-md px-4 py-3\">\r\n      <i className=\"fas fa-user text-orange-500 mr-3\" />\r\n      <input\r\n        type=\"text\"\r\n        placeholder=\"Full Name\"\r\n        className=\"w-full outline-none text-sm\"\r\n      />\r\n    </div>\r\n\r\n    {/* الإيميل */}\r\n    <div className=\"flex items-center border border-gray-300 rounded-md px-4 py-3\">\r\n      <i className=\"fas fa-envelope text-orange-500 mr-3\" />\r\n      <input\r\n        type=\"email\"\r\n        placeholder=\"Email\"\r\n        className=\"w-full outline-none text-sm\"\r\n      />\r\n    </div>\r\n\r\n    {/* الموضوع */}\r\n    <div className=\"md:col-span-2 flex items-center border border-gray-300 rounded-md px-4 py-3\">\r\n      <i className=\"fas fa-tag text-orange-500 mr-3\" />\r\n      <input\r\n        type=\"text\"\r\n        placeholder=\"Subject\"\r\n        className=\"w-full outline-none text-sm\"\r\n      />\r\n    </div>\r\n\r\n    {/* الرسالة */}\r\n    <div className=\"md:col-span-2 flex border border-gray-300 rounded-md px-4 py-3\">\r\n      <i className=\"fas fa-comment-dots text-orange-500 mr-3 mt-1\" />\r\n      <textarea\r\n        placeholder=\"Your Message\"\r\n        className=\"w-full outline-none text-sm resize-none min-h-[120px]\"\r\n      />\r\n    </div>\r\n\r\n    {/* زر الإرسال */}\r\n    <div className=\"md:col-span-2 text-center\">\r\n      <button\r\n        type=\"submit\"\r\n        className=\"bg-orange-500 r:bg-orange-600 text-white font-semibold px-8 py-2 rounded-md transition\"\r\n      >\r\n        Send Message\r\n      </button>\r\n    </div>\r\n  </form>\r\n\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;;AAGR,SAAS;IACtB,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAA4C;8CAClD,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAM9C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;gCAAyC,OAAO;oCAAE,MAAM;gCAAK;0CAAG;;;;;;0CAGhF,8OAAC;gCAAK,WAAU;0CAAqC;;;;;;;;;;;;kCAMvD,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;0BAMlD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAI;4BACJ,KAAI;4BACJ,WAAU;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAKpD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAKpD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAoC;8CAC5C,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;sCAExC,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;0BAOjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAoC;8CAC5C,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;sCAExC,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;0BAOrD,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCACC,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAUP", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;AAEgG,EAAC;IA2BrHiB,UAAU;;;;;;;;;AAlBZ,OAAO,MAAMd,eAAe,6CAAA;IAC1BC,MAAAA,GAASC;IACTC,EAAAA,OAAAA;IAAAA,CAAWC;IAAAA;QACb,EAAC,UAAA;YAAA;YAAA;gBAED,YAAA;oBAAA,CAAc;oBAAA,+BAA0C;qBAAE,wBAAwB;wBAAuB,UAAA,CAAA;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;oBAEzG;iBAAA,0DAA4D;YAC5D;YAAA,IAAO,MAAMC,cAAc,IAAIX,mBAAmB;kBAChDY,QAAAA,CAAAA,CAAY;YAAA;SAAA;;SACVC,MAAMZ,UAAUa,QAAQ;cACxBC,IAAAA;YAAAA,CAAM,GAAA;YAAA;SAAA;cACNC,OAAAA;YAAAA,EAAU,EAAA;YAAA;SAAA;cACV,OAAA;YAAA,IAAA,6BAA2C;YAAA;SAAA;cAC3CC,UAAAA;YAAAA,CAAY,GAAA;YAAA;SAAA;;OACZC,UAAU;QACVC,MAAAA;IAAAA,GAAU,EAAE;CAAA;;;AAKhB,GAAE,GAAA,uBAAA,sBAAA,CAAA", "ignoreList": [0], "debugId": null}}]}