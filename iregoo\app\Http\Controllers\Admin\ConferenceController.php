<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Conference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ConferenceController extends Controller
{
    public function index()
    {
        $conferences = Conference::withCount(['speakers', 'registrations', 'sponsors', 'events'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.conferences.index', compact('conferences'));
    }

    public function create()
    {
        return view('admin.conferences.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'location' => 'required|string|max:255',
            'venue' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'website_url' => 'nullable|url',
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:20',
            'registration_fee_local' => 'nullable|numeric|min:0',
            'registration_fee_international' => 'nullable|numeric|min:0',
            'currency_local' => 'nullable|string|max:3',
            'currency_international' => 'nullable|string|max:3',
            'abstract_deadline' => 'nullable|date',
            'notification_date' => 'nullable|date',
            'final_paper_deadline' => 'nullable|date',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('conferences', 'public');
            $data['logo'] = basename($logoPath);
        }

        // Handle banner image upload
        if ($request->hasFile('banner_image')) {
            $bannerPath = $request->file('banner_image')->store('conferences', 'public');
            $data['banner_image'] = basename($bannerPath);
        }

        $data['is_active'] = $request->has('is_active');

        Conference::create($data);

        return redirect()->route('admin.conferences.index')
            ->with('success', 'Conference created successfully.');
    }

    public function show(Conference $conference)
    {
        $conference->load(['speakers', 'registrations', 'sponsors', 'events', 'news']);
        
        return view('admin.conferences.show', compact('conference'));
    }

    public function edit(Conference $conference)
    {
        return view('admin.conferences.edit', compact('conference'));
    }

    public function update(Request $request, Conference $conference)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'location' => 'required|string|max:255',
            'venue' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'website_url' => 'nullable|url',
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:20',
            'registration_fee_local' => 'nullable|numeric|min:0',
            'registration_fee_international' => 'nullable|numeric|min:0',
            'currency_local' => 'nullable|string|max:3',
            'currency_international' => 'nullable|string|max:3',
            'abstract_deadline' => 'nullable|date',
            'notification_date' => 'nullable|date',
            'final_paper_deadline' => 'nullable|date',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($conference->logo) {
                Storage::disk('public')->delete('conferences/' . $conference->logo);
            }
            $logoPath = $request->file('logo')->store('conferences', 'public');
            $data['logo'] = basename($logoPath);
        }

        // Handle banner image upload
        if ($request->hasFile('banner_image')) {
            // Delete old banner
            if ($conference->banner_image) {
                Storage::disk('public')->delete('conferences/' . $conference->banner_image);
            }
            $bannerPath = $request->file('banner_image')->store('conferences', 'public');
            $data['banner_image'] = basename($bannerPath);
        }

        $data['is_active'] = $request->has('is_active');

        $conference->update($data);

        return redirect()->route('admin.conferences.index')
            ->with('success', 'Conference updated successfully.');
    }

    public function destroy(Conference $conference)
    {
        // Delete associated files
        if ($conference->logo) {
            Storage::disk('public')->delete('conferences/' . $conference->logo);
        }
        if ($conference->banner_image) {
            Storage::disk('public')->delete('conferences/' . $conference->banner_image);
        }

        $conference->delete();

        return redirect()->route('admin.conferences.index')
            ->with('success', 'Conference deleted successfully.');
    }
}
