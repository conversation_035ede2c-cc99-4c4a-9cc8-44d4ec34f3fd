{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.8 | MIT License | https://tailwindcss.com */\n@layer properties;\n.absolute {\n  position: absolute;\n}\n.fixed {\n  position: fixed;\n}\n.relative {\n  position: relative;\n}\n.inset-0 {\n  inset: calc(var(--spacing) * 0);\n}\n.top-2\\.5 {\n  top: calc(var(--spacing) * 2.5);\n}\n.top-6 {\n  top: calc(var(--spacing) * 6);\n}\n.top-20 {\n  top: calc(var(--spacing) * 20);\n}\n.top-\\[35px\\] {\n  top: 35px;\n}\n.right-0 {\n  right: calc(var(--spacing) * 0);\n}\n.right-3 {\n  right: calc(var(--spacing) * 3);\n}\n.right-4 {\n  right: calc(var(--spacing) * 4);\n}\n.right-6 {\n  right: calc(var(--spacing) * 6);\n}\n.left-0 {\n  left: calc(var(--spacing) * 0);\n}\n.z-0 {\n  z-index: 0;\n}\n.z-10 {\n  z-index: 10;\n}\n.z-20 {\n  z-index: 20;\n}\n.z-40 {\n  z-index: 40;\n}\n.z-50 {\n  z-index: 50;\n}\n.mx-auto {\n  margin-inline: auto;\n}\n.my-6 {\n  margin-block: calc(var(--spacing) * 6);\n}\n.mt-1 {\n  margin-top: calc(var(--spacing) * 1);\n}\n.mt-2 {\n  margin-top: calc(var(--spacing) * 2);\n}\n.mt-4 {\n  margin-top: calc(var(--spacing) * 4);\n}\n.mt-5 {\n  margin-top: calc(var(--spacing) * 5);\n}\n.mt-6 {\n  margin-top: calc(var(--spacing) * 6);\n}\n.mt-8 {\n  margin-top: calc(var(--spacing) * 8);\n}\n.mt-10 {\n  margin-top: calc(var(--spacing) * 10);\n}\n.mt-12 {\n  margin-top: calc(var(--spacing) * 12);\n}\n.mt-16 {\n  margin-top: calc(var(--spacing) * 16);\n}\n.mt-20 {\n  margin-top: calc(var(--spacing) * 20);\n}\n.mt-24 {\n  margin-top: calc(var(--spacing) * 24);\n}\n.mr-3 {\n  margin-right: calc(var(--spacing) * 3);\n}\n.mb-2 {\n  margin-bottom: calc(var(--spacing) * 2);\n}\n.mb-3 {\n  margin-bottom: calc(var(--spacing) * 3);\n}\n.mb-4 {\n  margin-bottom: calc(var(--spacing) * 4);\n}\n.mb-6 {\n  margin-bottom: calc(var(--spacing) * 6);\n}\n.mb-8 {\n  margin-bottom: calc(var(--spacing) * 8);\n}\n.mb-10 {\n  margin-bottom: calc(var(--spacing) * 10);\n}\n.mb-12 {\n  margin-bottom: calc(var(--spacing) * 12);\n}\n.mb-16 {\n  margin-bottom: calc(var(--spacing) * 16);\n}\n.block {\n  display: block;\n}\n.flex {\n  display: flex;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.inline-block {\n  display: inline-block;\n}\n.h-2 {\n  height: calc(var(--spacing) * 2);\n}\n.h-4 {\n  height: calc(var(--spacing) * 4);\n}\n.h-6 {\n  height: calc(var(--spacing) * 6);\n}\n.h-8 {\n  height: calc(var(--spacing) * 8);\n}\n.h-9 {\n  height: calc(var(--spacing) * 9);\n}\n.h-10 {\n  height: calc(var(--spacing) * 10);\n}\n.h-14 {\n  height: calc(var(--spacing) * 14);\n}\n.h-16 {\n  height: calc(var(--spacing) * 16);\n}\n.h-20 {\n  height: calc(var(--spacing) * 20);\n}\n.h-24 {\n  height: calc(var(--spacing) * 24);\n}\n.h-46 {\n  height: calc(var(--spacing) * 46);\n}\n.h-64 {\n  height: calc(var(--spacing) * 64);\n}\n.h-\\[2px\\] {\n  height: 2px;\n}\n.h-\\[300px\\] {\n  height: 300px;\n}\n.h-\\[500px\\] {\n  height: 500px;\n}\n.h-auto {\n  height: auto;\n}\n.h-full {\n  height: 100%;\n}\n.min-h-\\[120px\\] {\n  min-height: 120px;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-2 {\n  width: calc(var(--spacing) * 2);\n}\n.w-3 {\n  width: calc(var(--spacing) * 3);\n}\n.w-4 {\n  width: calc(var(--spacing) * 4);\n}\n.w-6 {\n  width: calc(var(--spacing) * 6);\n}\n.w-8 {\n  width: calc(var(--spacing) * 8);\n}\n.w-9 {\n  width: calc(var(--spacing) * 9);\n}\n.w-10 {\n  width: calc(var(--spacing) * 10);\n}\n.w-14 {\n  width: calc(var(--spacing) * 14);\n}\n.w-20 {\n  width: calc(var(--spacing) * 20);\n}\n.w-24 {\n  width: calc(var(--spacing) * 24);\n}\n.w-48 {\n  width: calc(var(--spacing) * 48);\n}\n.w-66 {\n  width: calc(var(--spacing) * 66);\n}\n.w-\\[250px\\] {\n  width: 250px;\n}\n.w-auto {\n  width: auto;\n}\n.w-fit {\n  width: fit-content;\n}\n.w-full {\n  width: 100%;\n}\n.w-max {\n  width: max-content;\n}\n.max-w-2xl {\n  max-width: var(--container-2xl);\n}\n.max-w-3xl {\n  max-width: var(--container-3xl);\n}\n.max-w-4xl {\n  max-width: var(--container-4xl);\n}\n.max-w-5xl {\n  max-width: var(--container-5xl);\n}\n.max-w-6xl {\n  max-width: var(--container-6xl);\n}\n.max-w-7xl {\n  max-width: var(--container-7xl);\n}\n.max-w-\\[120px\\] {\n  max-width: 120px;\n}\n.max-w-\\[1000px\\] {\n  max-width: 1000px;\n}\n.max-w-md {\n  max-width: var(--container-md);\n}\n.max-w-xl {\n  max-width: var(--container-xl);\n}\n.min-w-\\[60px\\] {\n  min-width: 60px;\n}\n.min-w-\\[100px\\] {\n  min-width: 100px;\n}\n.min-w-\\[140px\\] {\n  min-width: 140px;\n}\n.min-w-\\[150px\\] {\n  min-width: 150px;\n}\n.flex-grow {\n  flex-grow: 1;\n}\n.border-collapse {\n  border-collapse: collapse;\n}\n.transform {\n  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n}\n.animate-bounce {\n  animation: var(--animate-bounce);\n}\n.animate-pulse {\n  animation: var(--animate-pulse);\n}\n.animate-spin {\n  animation: var(--animate-spin);\n}\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.resize-none {\n  resize: none;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-row {\n  flex-direction: row;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.items-center {\n  align-items: center;\n}\n.items-start {\n  align-items: flex-start;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-end {\n  justify-content: flex-end;\n}\n.gap-2 {\n  gap: calc(var(--spacing) * 2);\n}\n.gap-3 {\n  gap: calc(var(--spacing) * 3);\n}\n.gap-4 {\n  gap: calc(var(--spacing) * 4);\n}\n.gap-6 {\n  gap: calc(var(--spacing) * 6);\n}\n.gap-8 {\n  gap: calc(var(--spacing) * 8);\n}\n.gap-10 {\n  gap: calc(var(--spacing) * 10);\n}\n.gap-12 {\n  gap: calc(var(--spacing) * 12);\n}\n.space-y-1 {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.space-y-2 {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.space-y-4 {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.space-y-6 {\n  :where(& > :not(:last-child)) {\n    --tw-space-y-reverse: 0;\n    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n  }\n}\n.space-x-2 {\n  :where(& > :not(:last-child)) {\n    --tw-space-x-reverse: 0;\n    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n  }\n}\n.space-x-3 {\n  :where(& > :not(:last-child)) {\n    --tw-space-x-reverse: 0;\n    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n  }\n}\n.space-x-4 {\n  :where(& > :not(:last-child)) {\n    --tw-space-x-reverse: 0;\n    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n  }\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.rounded {\n  border-radius: 0.25rem;\n}\n.rounded-2xl {\n  border-radius: var(--radius-2xl);\n}\n.rounded-full {\n  border-radius: calc(infinity * 1px);\n}\n.rounded-lg {\n  border-radius: var(--radius-lg);\n}\n.rounded-md {\n  border-radius: var(--radius-md);\n}\n.rounded-xl {\n  border-radius: var(--radius-xl);\n}\n.border {\n  border-style: var(--tw-border-style);\n  border-width: 1px;\n}\n.border-2 {\n  border-style: var(--tw-border-style);\n  border-width: 2px;\n}\n.border-t {\n  border-top-style: var(--tw-border-style);\n  border-top-width: 1px;\n}\n.border-t-4 {\n  border-top-style: var(--tw-border-style);\n  border-top-width: 4px;\n}\n.border-b {\n  border-bottom-style: var(--tw-border-style);\n  border-bottom-width: 1px;\n}\n.border-gray-200 {\n  border-color: var(--color-gray-200);\n}\n.border-gray-300 {\n  border-color: var(--color-gray-300);\n}\n.border-gray-500 {\n  border-color: var(--color-gray-500);\n}\n.border-gray-700 {\n  border-color: var(--color-gray-700);\n}\n.border-orange-200 {\n  border-color: var(--color-orange-200);\n}\n.border-orange-300 {\n  border-color: var(--color-orange-300);\n}\n.border-orange-500 {\n  border-color: var(--color-orange-500);\n}\n.border-white {\n  border-color: var(--color-white);\n}\n.border-white\\/10 {\n  border-color: color-mix(in srgb, #fff 10%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    border-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n  }\n}\n.border-t-transparent {\n  border-top-color: transparent;\n}\n.bg-black\\/40 {\n  background-color: color-mix(in srgb, #000 40%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--color-black) 40%, transparent);\n  }\n}\n.bg-black\\/50 {\n  background-color: color-mix(in srgb, #000 50%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n  }\n}\n.bg-gray-50 {\n  background-color: var(--color-gray-50);\n}\n.bg-gray-100 {\n  background-color: var(--color-gray-100);\n}\n.bg-gray-400 {\n  background-color: var(--color-gray-400);\n}\n.bg-gray-800\\/30 {\n  background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 30%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--color-gray-800) 30%, transparent);\n  }\n}\n.bg-gray-900 {\n  background-color: var(--color-gray-900);\n}\n.bg-green-500 {\n  background-color: var(--color-green-500);\n}\n.bg-orange-50 {\n  background-color: var(--color-orange-50);\n}\n.bg-orange-100 {\n  background-color: var(--color-orange-100);\n}\n.bg-orange-400 {\n  background-color: var(--color-orange-400);\n}\n.bg-orange-500 {\n  background-color: var(--color-orange-500);\n}\n.bg-orange-600 {\n  background-color: var(--color-orange-600);\n}\n.bg-red-500 {\n  background-color: var(--color-red-500);\n}\n.bg-transparent {\n  background-color: transparent;\n}\n.bg-white {\n  background-color: var(--color-white);\n}\n.bg-white\\/90 {\n  background-color: color-mix(in srgb, #fff 90%, transparent);\n  @supports (color: color-mix(in lab, red, red)) {\n    background-color: color-mix(in oklab, var(--color-white) 90%, transparent);\n  }\n}\n.bg-yellow-500 {\n  background-color: var(--color-yellow-500);\n}\n.object-contain {\n  object-fit: contain;\n}\n.object-cover {\n  object-fit: cover;\n}\n.p-2 {\n  padding: calc(var(--spacing) * 2);\n}\n.p-3 {\n  padding: calc(var(--spacing) * 3);\n}\n.p-4 {\n  padding: calc(var(--spacing) * 4);\n}\n.p-6 {\n  padding: calc(var(--spacing) * 6);\n}\n.p-8 {\n  padding: calc(var(--spacing) * 8);\n}\n.px-2 {\n  padding-inline: calc(var(--spacing) * 2);\n}\n.px-3 {\n  padding-inline: calc(var(--spacing) * 3);\n}\n.px-4 {\n  padding-inline: calc(var(--spacing) * 4);\n}\n.px-6 {\n  padding-inline: calc(var(--spacing) * 6);\n}\n.px-8 {\n  padding-inline: calc(var(--spacing) * 8);\n}\n.py-1 {\n  padding-block: calc(var(--spacing) * 1);\n}\n.py-2 {\n  padding-block: calc(var(--spacing) * 2);\n}\n.py-3 {\n  padding-block: calc(var(--spacing) * 3);\n}\n.py-4 {\n  padding-block: calc(var(--spacing) * 4);\n}\n.py-10 {\n  padding-block: calc(var(--spacing) * 10);\n}\n.py-12 {\n  padding-block: calc(var(--spacing) * 12);\n}\n.py-16 {\n  padding-block: calc(var(--spacing) * 16);\n}\n.pt-2 {\n  padding-top: calc(var(--spacing) * 2);\n}\n.pb-4 {\n  padding-bottom: calc(var(--spacing) * 4);\n}\n.pb-12 {\n  padding-bottom: calc(var(--spacing) * 12);\n}\n.pb-16 {\n  padding-bottom: calc(var(--spacing) * 16);\n}\n.text-center {\n  text-align: center;\n}\n.text-left {\n  text-align: left;\n}\n.text-2xl {\n  font-size: var(--text-2xl);\n  line-height: var(--tw-leading, var(--text-2xl--line-height));\n}\n.text-3xl {\n  font-size: var(--text-3xl);\n  line-height: var(--tw-leading, var(--text-3xl--line-height));\n}\n.text-4xl {\n  font-size: var(--text-4xl);\n  line-height: var(--tw-leading, var(--text-4xl--line-height));\n}\n.text-base {\n  font-size: var(--text-base);\n  line-height: var(--tw-leading, var(--text-base--line-height));\n}\n.text-lg {\n  font-size: var(--text-lg);\n  line-height: var(--tw-leading, var(--text-lg--line-height));\n}\n.text-sm {\n  font-size: var(--text-sm);\n  line-height: var(--tw-leading, var(--text-sm--line-height));\n}\n.text-xl {\n  font-size: var(--text-xl);\n  line-height: var(--tw-leading, var(--text-xl--line-height));\n}\n.text-xs {\n  font-size: var(--text-xs);\n  line-height: var(--tw-leading, var(--text-xs--line-height));\n}\n.leading-relaxed {\n  --tw-leading: var(--leading-relaxed);\n  line-height: var(--leading-relaxed);\n}\n.leading-snug {\n  --tw-leading: var(--leading-snug);\n  line-height: var(--leading-snug);\n}\n.font-bold {\n  --tw-font-weight: var(--font-weight-bold);\n  font-weight: var(--font-weight-bold);\n}\n.font-medium {\n  --tw-font-weight: var(--font-weight-medium);\n  font-weight: var(--font-weight-medium);\n}\n.font-semibold {\n  --tw-font-weight: var(--font-weight-semibold);\n  font-weight: var(--font-weight-semibold);\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.text-black {\n  color: var(--color-black);\n}\n.text-blue-900 {\n  color: var(--color-blue-900);\n}\n.text-gray-400 {\n  color: var(--color-gray-400);\n}\n.text-gray-500 {\n  color: var(--color-gray-500);\n}\n.text-gray-600 {\n  color: var(--color-gray-600);\n}\n.text-gray-700 {\n  color: var(--color-gray-700);\n}\n.text-gray-800 {\n  color: var(--color-gray-800);\n}\n.text-gray-900 {\n  color: var(--color-gray-900);\n}\n.text-green-600 {\n  color: var(--color-green-600);\n}\n.text-orange-500 {\n  color: var(--color-orange-500);\n}\n.text-orange-600 {\n  color: var(--color-orange-600);\n}\n.text-orange-800 {\n  color: var(--color-orange-800);\n}\n.text-red-500 {\n  color: var(--color-red-500);\n}\n.text-white {\n  color: var(--color-white);\n}\n.uppercase {\n  text-transform: uppercase;\n}\n.underline {\n  text-decoration-line: underline;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.shadow {\n  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.shadow-md {\n  --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n.backdrop-blur-md {\n  --tw-backdrop-blur: blur(var(--blur-md));\n  -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n}\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(var(--blur-sm));\n  -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n}\n.transition {\n  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.transition-transform {\n  transition-property: transform, translate, scale, rotate;\n  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n  transition-duration: var(--tw-duration, var(--default-transition-duration));\n}\n.duration-300 {\n  --tw-duration: 300ms;\n  transition-duration: 300ms;\n}\n.duration-700 {\n  --tw-duration: 700ms;\n  transition-duration: 700ms;\n}\n.ease-in-out {\n  --tw-ease: var(--ease-in-out);\n  transition-timing-function: var(--ease-in-out);\n}\n.outline-none {\n  --tw-outline-style: none;\n  outline-style: none;\n}\n.select-text {\n  -webkit-user-select: text;\n  user-select: text;\n}\n.hover\\:scale-\\[1\\.02\\] {\n  &:hover {\n    @media (hover: hover) {\n      scale: 1.02;\n    }\n  }\n}\n.hover\\:border-orange-500 {\n  &:hover {\n    @media (hover: hover) {\n      border-color: var(--color-orange-500);\n    }\n  }\n}\n.hover\\:bg-gray-100 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--color-gray-100);\n    }\n  }\n}\n.hover\\:bg-orange-600 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--color-orange-600);\n    }\n  }\n}\n.hover\\:bg-orange-700 {\n  &:hover {\n    @media (hover: hover) {\n      background-color: var(--color-orange-700);\n    }\n  }\n}\n.hover\\:text-orange-400 {\n  &:hover {\n    @media (hover: hover) {\n      color: var(--color-orange-400);\n    }\n  }\n}\n.hover\\:text-orange-500 {\n  &:hover {\n    @media (hover: hover) {\n      color: var(--color-orange-500);\n    }\n  }\n}\n.hover\\:text-orange-700 {\n  &:hover {\n    @media (hover: hover) {\n      color: var(--color-orange-700);\n    }\n  }\n}\n.hover\\:text-white {\n  &:hover {\n    @media (hover: hover) {\n      color: var(--color-white);\n    }\n  }\n}\n.hover\\:underline {\n  &:hover {\n    @media (hover: hover) {\n      text-decoration-line: underline;\n    }\n  }\n}\n.hover\\:shadow-md {\n  &:hover {\n    @media (hover: hover) {\n      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n}\n.focus\\:outline-none {\n  &:focus {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n}\n.sm\\:h-\\[550px\\] {\n  @media (width >= 40rem) {\n    height: 550px;\n  }\n}\n.sm\\:grid-cols-2 {\n  @media (width >= 40rem) {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n}\n.sm\\:flex-row {\n  @media (width >= 40rem) {\n    flex-direction: row;\n  }\n}\n.sm\\:px-6 {\n  @media (width >= 40rem) {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n}\n.sm\\:text-sm {\n  @media (width >= 40rem) {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n}\n.md\\:col-span-2 {\n  @media (width >= 48rem) {\n    grid-column: span 2 / span 2;\n  }\n}\n.md\\:flex {\n  @media (width >= 48rem) {\n    display: flex;\n  }\n}\n.md\\:hidden {\n  @media (width >= 48rem) {\n    display: none;\n  }\n}\n.md\\:h-80 {\n  @media (width >= 48rem) {\n    height: calc(var(--spacing) * 80);\n  }\n}\n.md\\:h-\\[400px\\] {\n  @media (width >= 48rem) {\n    height: 400px;\n  }\n}\n.md\\:h-\\[600px\\] {\n  @media (width >= 48rem) {\n    height: 600px;\n  }\n}\n.md\\:w-1\\/2 {\n  @media (width >= 48rem) {\n    width: calc(1/2 * 100%);\n  }\n}\n.md\\:w-\\[300px\\] {\n  @media (width >= 48rem) {\n    width: 300px;\n  }\n}\n.md\\:grid-cols-2 {\n  @media (width >= 48rem) {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n}\n.md\\:grid-cols-3 {\n  @media (width >= 48rem) {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n}\n.md\\:grid-cols-4 {\n  @media (width >= 48rem) {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n}\n.md\\:flex-row {\n  @media (width >= 48rem) {\n    flex-direction: row;\n  }\n}\n.md\\:px-20 {\n  @media (width >= 48rem) {\n    padding-inline: calc(var(--spacing) * 20);\n  }\n}\n.md\\:text-left {\n  @media (width >= 48rem) {\n    text-align: left;\n  }\n}\n.md\\:text-2xl {\n  @media (width >= 48rem) {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n}\n.md\\:text-3xl {\n  @media (width >= 48rem) {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n}\n.md\\:text-4xl {\n  @media (width >= 48rem) {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n}\n.md\\:text-5xl {\n  @media (width >= 48rem) {\n    font-size: var(--text-5xl);\n    line-height: var(--tw-leading, var(--text-5xl--line-height));\n  }\n}\n.md\\:text-base {\n  @media (width >= 48rem) {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n}\n.md\\:text-lg {\n  @media (width >= 48rem) {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n}\n.md\\:text-xl {\n  @media (width >= 48rem) {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n}\n.lg\\:grid-cols-3 {\n  @media (width >= 64rem) {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n}\n.lg\\:grid-cols-4 {\n  @media (width >= 64rem) {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n}\n.lg\\:px-8 {\n  @media (width >= 64rem) {\n    padding-inline: calc(var(--spacing) * 8);\n  }\n}\n.lg\\:px-12 {\n  @media (width >= 64rem) {\n    padding-inline: calc(var(--spacing) * 12);\n  }\n}\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-orange-50: oklch(98% 0.016 73.684);\n    --color-orange-100: oklch(95.4% 0.038 75.164);\n    --color-orange-200: oklch(90.1% 0.076 70.697);\n    --color-orange-300: oklch(83.7% 0.128 66.29);\n    --color-orange-400: oklch(75% 0.183 55.934);\n    --color-orange-500: oklch(70.5% 0.213 47.604);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-orange-700: oklch(55.3% 0.195 38.402);\n    --color-orange-800: oklch(47% 0.157 37.304);\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-blue-900: oklch(37.9% 0.146 265.522);\n    --color-gray-50: oklch(98.5% 0.002 247.839);\n    --color-gray-100: oklch(96.7% 0.003 264.542);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-md: 28rem;\n    --container-xl: 36rem;\n    --container-2xl: 42rem;\n    --container-3xl: 48rem;\n    --container-4xl: 56rem;\n    --container-5xl: 64rem;\n    --container-6xl: 72rem;\n    --container-7xl: 80rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-5xl: 3rem;\n    --text-5xl--line-height: 1;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --leading-snug: 1.375;\n    --leading-relaxed: 1.625;\n    --radius-md: 0.375rem;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --radius-2xl: 1rem;\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-spin: spin 1s linear infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --animate-bounce: bounce 1s infinite;\n    --blur-sm: 8px;\n    --blur-md: 12px;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-geist-sans);\n    --default-mono-font-family: var(--font-geist-mono);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities;\n/*!\n * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)\n * Copyright 2024 Fonticons, Inc.\n */\n.fa {\n  font-family: var(--fa-style-family,\"Font Awesome 6 Free\");\n  font-weight: var(--fa-style,900);\n}\n.fa,.fa-brands,.fa-regular,.fa-solid,.fab,.far,.fas {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n  display: var(--fa-display,inline-block);\n  font-style: normal;\n  font-variant: normal;\n  line-height: 1;\n  text-rendering: auto;\n}\n.fa-brands:before,.fa-regular:before,.fa-solid:before,.fa:before,.fab:before,.far:before,.fas:before {\n  content: var(--fa);\n}\n.fa-classic,.fa-regular,.fa-solid,.far,.fas {\n  font-family: \"Font Awesome 6 Free\";\n}\n.fa-brands,.fab {\n  font-family: \"Font Awesome 6 Brands\";\n}\n.fa-1x {\n  font-size: 1em;\n}\n.fa-2x {\n  font-size: 2em;\n}\n.fa-3x {\n  font-size: 3em;\n}\n.fa-4x {\n  font-size: 4em;\n}\n.fa-5x {\n  font-size: 5em;\n}\n.fa-6x {\n  font-size: 6em;\n}\n.fa-7x {\n  font-size: 7em;\n}\n.fa-8x {\n  font-size: 8em;\n}\n.fa-9x {\n  font-size: 9em;\n}\n.fa-10x {\n  font-size: 10em;\n}\n.fa-2xs {\n  font-size: .625em;\n  line-height: .1em;\n  vertical-align: .225em;\n}\n.fa-xs {\n  font-size: .75em;\n  line-height: .08333em;\n  vertical-align: .125em;\n}\n.fa-sm {\n  font-size: .875em;\n  line-height: .07143em;\n  vertical-align: .05357em;\n}\n.fa-lg {\n  font-size: 1.25em;\n  line-height: .05em;\n  vertical-align: -.075em;\n}\n.fa-xl {\n  font-size: 1.5em;\n  line-height: .04167em;\n  vertical-align: -.125em;\n}\n.fa-2xl {\n  font-size: 2em;\n  line-height: .03125em;\n  vertical-align: -.1875em;\n}\n.fa-fw {\n  text-align: center;\n  width: 1.25em;\n}\n.fa-ul {\n  list-style-type: none;\n  margin-left: var(--fa-li-margin,2.5em);\n  padding-left: 0;\n}\n.fa-ul>li {\n  position: relative;\n}\n.fa-li {\n  left: calc(var(--fa-li-width, 2em)*-1);\n  position: absolute;\n  text-align: center;\n  width: var(--fa-li-width,2em);\n  line-height: inherit;\n}\n.fa-border {\n  border-radius: var(--fa-border-radius,.1em);\n  border: var(--fa-border-width,.08em) var(--fa-border-style,solid) var(--fa-border-color,#eee);\n  padding: var(--fa-border-padding,.2em .25em .15em);\n}\n.fa-pull-left {\n  float: left;\n  margin-right: var(--fa-pull-margin,.3em);\n}\n.fa-pull-right {\n  float: right;\n  margin-left: var(--fa-pull-margin,.3em);\n}\n.fa-beat {\n  animation-name: fa-beat;\n  animation-delay: var(--fa-animation-delay,0s);\n  animation-direction: var(--fa-animation-direction,normal);\n  animation-duration: var(--fa-animation-duration,1s);\n  animation-iteration-count: var(--fa-animation-iteration-count,infinite);\n  animation-timing-function: var(--fa-animation-timing,ease-in-out);\n}\n.fa-bounce {\n  animation-name: fa-bounce;\n  animation-delay: var(--fa-animation-delay,0s);\n  animation-direction: var(--fa-animation-direction,normal);\n  animation-duration: var(--fa-animation-duration,1s);\n  animation-iteration-count: var(--fa-animation-iteration-count,infinite);\n  animation-timing-function: var(--fa-animation-timing,cubic-bezier(.28,.84,.42,1));\n}\n.fa-fade {\n  animation-name: fa-fade;\n  animation-iteration-count: var(--fa-animation-iteration-count,infinite);\n  animation-timing-function: var(--fa-animation-timing,cubic-bezier(.4,0,.6,1));\n}\n.fa-beat-fade,.fa-fade {\n  animation-delay: var(--fa-animation-delay,0s);\n  animation-direction: var(--fa-animation-direction,normal);\n  animation-duration: var(--fa-animation-duration,1s);\n}\n.fa-beat-fade {\n  animation-name: fa-beat-fade;\n  animation-iteration-count: var(--fa-animation-iteration-count,infinite);\n  animation-timing-function: var(--fa-animation-timing,cubic-bezier(.4,0,.6,1));\n}\n.fa-flip {\n  animation-name: fa-flip;\n  animation-delay: var(--fa-animation-delay,0s);\n  animation-direction: var(--fa-animation-direction,normal);\n  animation-duration: var(--fa-animation-duration,1s);\n  animation-iteration-count: var(--fa-animation-iteration-count,infinite);\n  animation-timing-function: var(--fa-animation-timing,ease-in-out);\n}\n.fa-shake {\n  animation-name: fa-shake;\n  animation-duration: var(--fa-animation-duration,1s);\n  animation-iteration-count: var(--fa-animation-iteration-count,infinite);\n  animation-timing-function: var(--fa-animation-timing,linear);\n}\n.fa-shake,.fa-spin {\n  animation-delay: var(--fa-animation-delay,0s);\n  animation-direction: var(--fa-animation-direction,normal);\n}\n.fa-spin {\n  animation-name: fa-spin;\n  animation-duration: var(--fa-animation-duration,2s);\n  animation-iteration-count: var(--fa-animation-iteration-count,infinite);\n  animation-timing-function: var(--fa-animation-timing,linear);\n}\n.fa-spin-reverse {\n  --fa-animation-direction: reverse;\n}\n.fa-pulse,.fa-spin-pulse {\n  animation-name: fa-spin;\n  animation-direction: var(--fa-animation-direction,normal);\n  animation-duration: var(--fa-animation-duration,1s);\n  animation-iteration-count: var(--fa-animation-iteration-count,infinite);\n  animation-timing-function: var(--fa-animation-timing,steps(8));\n}\n@media (prefers-reduced-motion:reduce) {\n  .fa-beat,.fa-beat-fade,.fa-bounce,.fa-fade,.fa-flip,.fa-pulse,.fa-shake,.fa-spin,.fa-spin-pulse {\n    animation-delay: -1ms;\n    animation-duration: 1ms;\n    animation-iteration-count: 1;\n    transition-delay: 0s;\n    transition-duration: 0s;\n  }\n}\n@keyframes fa-beat {\n  0%,90% {\n    transform: scale(1);\n  }\n  45% {\n    transform: scale(var(--fa-beat-scale,1.25));\n  }\n}\n@keyframes fa-bounce {\n  0% {\n    transform: scale(1) translateY(0);\n  }\n  10% {\n    transform: scale(var(--fa-bounce-start-scale-x,1.1),var(--fa-bounce-start-scale-y,.9)) translateY(0);\n  }\n  30% {\n    transform: scale(var(--fa-bounce-jump-scale-x,.9),var(--fa-bounce-jump-scale-y,1.1)) translateY(var(--fa-bounce-height,-.5em));\n  }\n  50% {\n    transform: scale(var(--fa-bounce-land-scale-x,1.05),var(--fa-bounce-land-scale-y,.95)) translateY(0);\n  }\n  57% {\n    transform: scale(1) translateY(var(--fa-bounce-rebound,-.125em));\n  }\n  64% {\n    transform: scale(1) translateY(0);\n  }\n  to {\n    transform: scale(1) translateY(0);\n  }\n}\n@keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity,.4);\n  }\n}\n@keyframes fa-beat-fade {\n  0%,to {\n    opacity: var(--fa-beat-fade-opacity,.4);\n    transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(var(--fa-beat-fade-scale,1.125));\n  }\n}\n@keyframes fa-flip {\n  50% {\n    transform: rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-flip-z,0),var(--fa-flip-angle,-180deg));\n  }\n}\n@keyframes fa-shake {\n  0% {\n    transform: rotate(-15deg);\n  }\n  4% {\n    transform: rotate(15deg);\n  }\n  8%,24% {\n    transform: rotate(-18deg);\n  }\n  12%,28% {\n    transform: rotate(18deg);\n  }\n  16% {\n    transform: rotate(-22deg);\n  }\n  20% {\n    transform: rotate(22deg);\n  }\n  32% {\n    transform: rotate(-12deg);\n  }\n  36% {\n    transform: rotate(12deg);\n  }\n  40%,to {\n    transform: rotate(0deg);\n  }\n}\n@keyframes fa-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(1turn);\n  }\n}\n.fa-rotate-90 {\n  transform: rotate(90deg);\n}\n.fa-rotate-180 {\n  transform: rotate(180deg);\n}\n.fa-rotate-270 {\n  transform: rotate(270deg);\n}\n.fa-flip-horizontal {\n  transform: scaleX(-1);\n}\n.fa-flip-vertical {\n  transform: scaleY(-1);\n}\n.fa-flip-both,.fa-flip-horizontal.fa-flip-vertical {\n  transform: scale(-1);\n}\n.fa-rotate-by {\n  transform: rotate(var(--fa-rotate-angle,0));\n}\n.fa-stack {\n  display: inline-block;\n  height: 2em;\n  line-height: 2em;\n  position: relative;\n  vertical-align: middle;\n  width: 2.5em;\n}\n.fa-stack-1x,.fa-stack-2x {\n  left: 0;\n  position: absolute;\n  text-align: center;\n  width: 100%;\n  z-index: var(--fa-stack-z-index,auto);\n}\n.fa-stack-1x {\n  line-height: inherit;\n}\n.fa-stack-2x {\n  font-size: 2em;\n}\n.fa-inverse {\n  color: var(--fa-inverse,#fff);\n}\n.fa-0 {\n  --fa: \"\\30\";\n}\n.fa-1 {\n  --fa: \"\\31\";\n}\n.fa-2 {\n  --fa: \"\\32\";\n}\n.fa-3 {\n  --fa: \"\\33\";\n}\n.fa-4 {\n  --fa: \"\\34\";\n}\n.fa-5 {\n  --fa: \"\\35\";\n}\n.fa-6 {\n  --fa: \"\\36\";\n}\n.fa-7 {\n  --fa: \"\\37\";\n}\n.fa-8 {\n  --fa: \"\\38\";\n}\n.fa-9 {\n  --fa: \"\\39\";\n}\n.fa-fill-drip {\n  --fa: \"\\f576\";\n}\n.fa-arrows-to-circle {\n  --fa: \"\\e4bd\";\n}\n.fa-chevron-circle-right,.fa-circle-chevron-right {\n  --fa: \"\\f138\";\n}\n.fa-at {\n  --fa: \"\\40\";\n}\n.fa-trash-alt,.fa-trash-can {\n  --fa: \"\\f2ed\";\n}\n.fa-text-height {\n  --fa: \"\\f034\";\n}\n.fa-user-times,.fa-user-xmark {\n  --fa: \"\\f235\";\n}\n.fa-stethoscope {\n  --fa: \"\\f0f1\";\n}\n.fa-comment-alt,.fa-message {\n  --fa: \"\\f27a\";\n}\n.fa-info {\n  --fa: \"\\f129\";\n}\n.fa-compress-alt,.fa-down-left-and-up-right-to-center {\n  --fa: \"\\f422\";\n}\n.fa-explosion {\n  --fa: \"\\e4e9\";\n}\n.fa-file-alt,.fa-file-lines,.fa-file-text {\n  --fa: \"\\f15c\";\n}\n.fa-wave-square {\n  --fa: \"\\f83e\";\n}\n.fa-ring {\n  --fa: \"\\f70b\";\n}\n.fa-building-un {\n  --fa: \"\\e4d9\";\n}\n.fa-dice-three {\n  --fa: \"\\f527\";\n}\n.fa-calendar-alt,.fa-calendar-days {\n  --fa: \"\\f073\";\n}\n.fa-anchor-circle-check {\n  --fa: \"\\e4aa\";\n}\n.fa-building-circle-arrow-right {\n  --fa: \"\\e4d1\";\n}\n.fa-volleyball,.fa-volleyball-ball {\n  --fa: \"\\f45f\";\n}\n.fa-arrows-up-to-line {\n  --fa: \"\\e4c2\";\n}\n.fa-sort-desc,.fa-sort-down {\n  --fa: \"\\f0dd\";\n}\n.fa-circle-minus,.fa-minus-circle {\n  --fa: \"\\f056\";\n}\n.fa-door-open {\n  --fa: \"\\f52b\";\n}\n.fa-right-from-bracket,.fa-sign-out-alt {\n  --fa: \"\\f2f5\";\n}\n.fa-atom {\n  --fa: \"\\f5d2\";\n}\n.fa-soap {\n  --fa: \"\\e06e\";\n}\n.fa-heart-music-camera-bolt,.fa-icons {\n  --fa: \"\\f86d\";\n}\n.fa-microphone-alt-slash,.fa-microphone-lines-slash {\n  --fa: \"\\f539\";\n}\n.fa-bridge-circle-check {\n  --fa: \"\\e4c9\";\n}\n.fa-pump-medical {\n  --fa: \"\\e06a\";\n}\n.fa-fingerprint {\n  --fa: \"\\f577\";\n}\n.fa-hand-point-right {\n  --fa: \"\\f0a4\";\n}\n.fa-magnifying-glass-location,.fa-search-location {\n  --fa: \"\\f689\";\n}\n.fa-forward-step,.fa-step-forward {\n  --fa: \"\\f051\";\n}\n.fa-face-smile-beam,.fa-smile-beam {\n  --fa: \"\\f5b8\";\n}\n.fa-flag-checkered {\n  --fa: \"\\f11e\";\n}\n.fa-football,.fa-football-ball {\n  --fa: \"\\f44e\";\n}\n.fa-school-circle-exclamation {\n  --fa: \"\\e56c\";\n}\n.fa-crop {\n  --fa: \"\\f125\";\n}\n.fa-angle-double-down,.fa-angles-down {\n  --fa: \"\\f103\";\n}\n.fa-users-rectangle {\n  --fa: \"\\e594\";\n}\n.fa-people-roof {\n  --fa: \"\\e537\";\n}\n.fa-people-line {\n  --fa: \"\\e534\";\n}\n.fa-beer,.fa-beer-mug-empty {\n  --fa: \"\\f0fc\";\n}\n.fa-diagram-predecessor {\n  --fa: \"\\e477\";\n}\n.fa-arrow-up-long,.fa-long-arrow-up {\n  --fa: \"\\f176\";\n}\n.fa-burn,.fa-fire-flame-simple {\n  --fa: \"\\f46a\";\n}\n.fa-male,.fa-person {\n  --fa: \"\\f183\";\n}\n.fa-laptop {\n  --fa: \"\\f109\";\n}\n.fa-file-csv {\n  --fa: \"\\f6dd\";\n}\n.fa-menorah {\n  --fa: \"\\f676\";\n}\n.fa-truck-plane {\n  --fa: \"\\e58f\";\n}\n.fa-record-vinyl {\n  --fa: \"\\f8d9\";\n}\n.fa-face-grin-stars,.fa-grin-stars {\n  --fa: \"\\f587\";\n}\n.fa-bong {\n  --fa: \"\\f55c\";\n}\n.fa-pastafarianism,.fa-spaghetti-monster-flying {\n  --fa: \"\\f67b\";\n}\n.fa-arrow-down-up-across-line {\n  --fa: \"\\e4af\";\n}\n.fa-spoon,.fa-utensil-spoon {\n  --fa: \"\\f2e5\";\n}\n.fa-jar-wheat {\n  --fa: \"\\e517\";\n}\n.fa-envelopes-bulk,.fa-mail-bulk {\n  --fa: \"\\f674\";\n}\n.fa-file-circle-exclamation {\n  --fa: \"\\e4eb\";\n}\n.fa-circle-h,.fa-hospital-symbol {\n  --fa: \"\\f47e\";\n}\n.fa-pager {\n  --fa: \"\\f815\";\n}\n.fa-address-book,.fa-contact-book {\n  --fa: \"\\f2b9\";\n}\n.fa-strikethrough {\n  --fa: \"\\f0cc\";\n}\n.fa-k {\n  --fa: \"\\4b\";\n}\n.fa-landmark-flag {\n  --fa: \"\\e51c\";\n}\n.fa-pencil,.fa-pencil-alt {\n  --fa: \"\\f303\";\n}\n.fa-backward {\n  --fa: \"\\f04a\";\n}\n.fa-caret-right {\n  --fa: \"\\f0da\";\n}\n.fa-comments {\n  --fa: \"\\f086\";\n}\n.fa-file-clipboard,.fa-paste {\n  --fa: \"\\f0ea\";\n}\n.fa-code-pull-request {\n  --fa: \"\\e13c\";\n}\n.fa-clipboard-list {\n  --fa: \"\\f46d\";\n}\n.fa-truck-loading,.fa-truck-ramp-box {\n  --fa: \"\\f4de\";\n}\n.fa-user-check {\n  --fa: \"\\f4fc\";\n}\n.fa-vial-virus {\n  --fa: \"\\e597\";\n}\n.fa-sheet-plastic {\n  --fa: \"\\e571\";\n}\n.fa-blog {\n  --fa: \"\\f781\";\n}\n.fa-user-ninja {\n  --fa: \"\\f504\";\n}\n.fa-person-arrow-up-from-line {\n  --fa: \"\\e539\";\n}\n.fa-scroll-torah,.fa-torah {\n  --fa: \"\\f6a0\";\n}\n.fa-broom-ball,.fa-quidditch,.fa-quidditch-broom-ball {\n  --fa: \"\\f458\";\n}\n.fa-toggle-off {\n  --fa: \"\\f204\";\n}\n.fa-archive,.fa-box-archive {\n  --fa: \"\\f187\";\n}\n.fa-person-drowning {\n  --fa: \"\\e545\";\n}\n.fa-arrow-down-9-1,.fa-sort-numeric-desc,.fa-sort-numeric-down-alt {\n  --fa: \"\\f886\";\n}\n.fa-face-grin-tongue-squint,.fa-grin-tongue-squint {\n  --fa: \"\\f58a\";\n}\n.fa-spray-can {\n  --fa: \"\\f5bd\";\n}\n.fa-truck-monster {\n  --fa: \"\\f63b\";\n}\n.fa-w {\n  --fa: \"\\57\";\n}\n.fa-earth-africa,.fa-globe-africa {\n  --fa: \"\\f57c\";\n}\n.fa-rainbow {\n  --fa: \"\\f75b\";\n}\n.fa-circle-notch {\n  --fa: \"\\f1ce\";\n}\n.fa-tablet-alt,.fa-tablet-screen-button {\n  --fa: \"\\f3fa\";\n}\n.fa-paw {\n  --fa: \"\\f1b0\";\n}\n.fa-cloud {\n  --fa: \"\\f0c2\";\n}\n.fa-trowel-bricks {\n  --fa: \"\\e58a\";\n}\n.fa-face-flushed,.fa-flushed {\n  --fa: \"\\f579\";\n}\n.fa-hospital-user {\n  --fa: \"\\f80d\";\n}\n.fa-tent-arrow-left-right {\n  --fa: \"\\e57f\";\n}\n.fa-gavel,.fa-legal {\n  --fa: \"\\f0e3\";\n}\n.fa-binoculars {\n  --fa: \"\\f1e5\";\n}\n.fa-microphone-slash {\n  --fa: \"\\f131\";\n}\n.fa-box-tissue {\n  --fa: \"\\e05b\";\n}\n.fa-motorcycle {\n  --fa: \"\\f21c\";\n}\n.fa-bell-concierge,.fa-concierge-bell {\n  --fa: \"\\f562\";\n}\n.fa-pen-ruler,.fa-pencil-ruler {\n  --fa: \"\\f5ae\";\n}\n.fa-people-arrows,.fa-people-arrows-left-right {\n  --fa: \"\\e068\";\n}\n.fa-mars-and-venus-burst {\n  --fa: \"\\e523\";\n}\n.fa-caret-square-right,.fa-square-caret-right {\n  --fa: \"\\f152\";\n}\n.fa-cut,.fa-scissors {\n  --fa: \"\\f0c4\";\n}\n.fa-sun-plant-wilt {\n  --fa: \"\\e57a\";\n}\n.fa-toilets-portable {\n  --fa: \"\\e584\";\n}\n.fa-hockey-puck {\n  --fa: \"\\f453\";\n}\n.fa-table {\n  --fa: \"\\f0ce\";\n}\n.fa-magnifying-glass-arrow-right {\n  --fa: \"\\e521\";\n}\n.fa-digital-tachograph,.fa-tachograph-digital {\n  --fa: \"\\f566\";\n}\n.fa-users-slash {\n  --fa: \"\\e073\";\n}\n.fa-clover {\n  --fa: \"\\e139\";\n}\n.fa-mail-reply,.fa-reply {\n  --fa: \"\\f3e5\";\n}\n.fa-star-and-crescent {\n  --fa: \"\\f699\";\n}\n.fa-house-fire {\n  --fa: \"\\e50c\";\n}\n.fa-minus-square,.fa-square-minus {\n  --fa: \"\\f146\";\n}\n.fa-helicopter {\n  --fa: \"\\f533\";\n}\n.fa-compass {\n  --fa: \"\\f14e\";\n}\n.fa-caret-square-down,.fa-square-caret-down {\n  --fa: \"\\f150\";\n}\n.fa-file-circle-question {\n  --fa: \"\\e4ef\";\n}\n.fa-laptop-code {\n  --fa: \"\\f5fc\";\n}\n.fa-swatchbook {\n  --fa: \"\\f5c3\";\n}\n.fa-prescription-bottle {\n  --fa: \"\\f485\";\n}\n.fa-bars,.fa-navicon {\n  --fa: \"\\f0c9\";\n}\n.fa-people-group {\n  --fa: \"\\e533\";\n}\n.fa-hourglass-3,.fa-hourglass-end {\n  --fa: \"\\f253\";\n}\n.fa-heart-broken,.fa-heart-crack {\n  --fa: \"\\f7a9\";\n}\n.fa-external-link-square-alt,.fa-square-up-right {\n  --fa: \"\\f360\";\n}\n.fa-face-kiss-beam,.fa-kiss-beam {\n  --fa: \"\\f597\";\n}\n.fa-film {\n  --fa: \"\\f008\";\n}\n.fa-ruler-horizontal {\n  --fa: \"\\f547\";\n}\n.fa-people-robbery {\n  --fa: \"\\e536\";\n}\n.fa-lightbulb {\n  --fa: \"\\f0eb\";\n}\n.fa-caret-left {\n  --fa: \"\\f0d9\";\n}\n.fa-circle-exclamation,.fa-exclamation-circle {\n  --fa: \"\\f06a\";\n}\n.fa-school-circle-xmark {\n  --fa: \"\\e56d\";\n}\n.fa-arrow-right-from-bracket,.fa-sign-out {\n  --fa: \"\\f08b\";\n}\n.fa-chevron-circle-down,.fa-circle-chevron-down {\n  --fa: \"\\f13a\";\n}\n.fa-unlock-alt,.fa-unlock-keyhole {\n  --fa: \"\\f13e\";\n}\n.fa-cloud-showers-heavy {\n  --fa: \"\\f740\";\n}\n.fa-headphones-alt,.fa-headphones-simple {\n  --fa: \"\\f58f\";\n}\n.fa-sitemap {\n  --fa: \"\\f0e8\";\n}\n.fa-circle-dollar-to-slot,.fa-donate {\n  --fa: \"\\f4b9\";\n}\n.fa-memory {\n  --fa: \"\\f538\";\n}\n.fa-road-spikes {\n  --fa: \"\\e568\";\n}\n.fa-fire-burner {\n  --fa: \"\\e4f1\";\n}\n.fa-flag {\n  --fa: \"\\f024\";\n}\n.fa-hanukiah {\n  --fa: \"\\f6e6\";\n}\n.fa-feather {\n  --fa: \"\\f52d\";\n}\n.fa-volume-down,.fa-volume-low {\n  --fa: \"\\f027\";\n}\n.fa-comment-slash {\n  --fa: \"\\f4b3\";\n}\n.fa-cloud-sun-rain {\n  --fa: \"\\f743\";\n}\n.fa-compress {\n  --fa: \"\\f066\";\n}\n.fa-wheat-alt,.fa-wheat-awn {\n  --fa: \"\\e2cd\";\n}\n.fa-ankh {\n  --fa: \"\\f644\";\n}\n.fa-hands-holding-child {\n  --fa: \"\\e4fa\";\n}\n.fa-asterisk {\n  --fa: \"\\2a\";\n}\n.fa-check-square,.fa-square-check {\n  --fa: \"\\f14a\";\n}\n.fa-peseta-sign {\n  --fa: \"\\e221\";\n}\n.fa-header,.fa-heading {\n  --fa: \"\\f1dc\";\n}\n.fa-ghost {\n  --fa: \"\\f6e2\";\n}\n.fa-list,.fa-list-squares {\n  --fa: \"\\f03a\";\n}\n.fa-phone-square-alt,.fa-square-phone-flip {\n  --fa: \"\\f87b\";\n}\n.fa-cart-plus {\n  --fa: \"\\f217\";\n}\n.fa-gamepad {\n  --fa: \"\\f11b\";\n}\n.fa-circle-dot,.fa-dot-circle {\n  --fa: \"\\f192\";\n}\n.fa-dizzy,.fa-face-dizzy {\n  --fa: \"\\f567\";\n}\n.fa-egg {\n  --fa: \"\\f7fb\";\n}\n.fa-house-medical-circle-xmark {\n  --fa: \"\\e513\";\n}\n.fa-campground {\n  --fa: \"\\f6bb\";\n}\n.fa-folder-plus {\n  --fa: \"\\f65e\";\n}\n.fa-futbol,.fa-futbol-ball,.fa-soccer-ball {\n  --fa: \"\\f1e3\";\n}\n.fa-paint-brush,.fa-paintbrush {\n  --fa: \"\\f1fc\";\n}\n.fa-lock {\n  --fa: \"\\f023\";\n}\n.fa-gas-pump {\n  --fa: \"\\f52f\";\n}\n.fa-hot-tub,.fa-hot-tub-person {\n  --fa: \"\\f593\";\n}\n.fa-map-location,.fa-map-marked {\n  --fa: \"\\f59f\";\n}\n.fa-house-flood-water {\n  --fa: \"\\e50e\";\n}\n.fa-tree {\n  --fa: \"\\f1bb\";\n}\n.fa-bridge-lock {\n  --fa: \"\\e4cc\";\n}\n.fa-sack-dollar {\n  --fa: \"\\f81d\";\n}\n.fa-edit,.fa-pen-to-square {\n  --fa: \"\\f044\";\n}\n.fa-car-side {\n  --fa: \"\\f5e4\";\n}\n.fa-share-alt,.fa-share-nodes {\n  --fa: \"\\f1e0\";\n}\n.fa-heart-circle-minus {\n  --fa: \"\\e4ff\";\n}\n.fa-hourglass-2,.fa-hourglass-half {\n  --fa: \"\\f252\";\n}\n.fa-microscope {\n  --fa: \"\\f610\";\n}\n.fa-sink {\n  --fa: \"\\e06d\";\n}\n.fa-bag-shopping,.fa-shopping-bag {\n  --fa: \"\\f290\";\n}\n.fa-arrow-down-z-a,.fa-sort-alpha-desc,.fa-sort-alpha-down-alt {\n  --fa: \"\\f881\";\n}\n.fa-mitten {\n  --fa: \"\\f7b5\";\n}\n.fa-person-rays {\n  --fa: \"\\e54d\";\n}\n.fa-users {\n  --fa: \"\\f0c0\";\n}\n.fa-eye-slash {\n  --fa: \"\\f070\";\n}\n.fa-flask-vial {\n  --fa: \"\\e4f3\";\n}\n.fa-hand,.fa-hand-paper {\n  --fa: \"\\f256\";\n}\n.fa-om {\n  --fa: \"\\f679\";\n}\n.fa-worm {\n  --fa: \"\\e599\";\n}\n.fa-house-circle-xmark {\n  --fa: \"\\e50b\";\n}\n.fa-plug {\n  --fa: \"\\f1e6\";\n}\n.fa-chevron-up {\n  --fa: \"\\f077\";\n}\n.fa-hand-spock {\n  --fa: \"\\f259\";\n}\n.fa-stopwatch {\n  --fa: \"\\f2f2\";\n}\n.fa-face-kiss,.fa-kiss {\n  --fa: \"\\f596\";\n}\n.fa-bridge-circle-xmark {\n  --fa: \"\\e4cb\";\n}\n.fa-face-grin-tongue,.fa-grin-tongue {\n  --fa: \"\\f589\";\n}\n.fa-chess-bishop {\n  --fa: \"\\f43a\";\n}\n.fa-face-grin-wink,.fa-grin-wink {\n  --fa: \"\\f58c\";\n}\n.fa-deaf,.fa-deafness,.fa-ear-deaf,.fa-hard-of-hearing {\n  --fa: \"\\f2a4\";\n}\n.fa-road-circle-check {\n  --fa: \"\\e564\";\n}\n.fa-dice-five {\n  --fa: \"\\f523\";\n}\n.fa-rss-square,.fa-square-rss {\n  --fa: \"\\f143\";\n}\n.fa-land-mine-on {\n  --fa: \"\\e51b\";\n}\n.fa-i-cursor {\n  --fa: \"\\f246\";\n}\n.fa-stamp {\n  --fa: \"\\f5bf\";\n}\n.fa-stairs {\n  --fa: \"\\e289\";\n}\n.fa-i {\n  --fa: \"\\49\";\n}\n.fa-hryvnia,.fa-hryvnia-sign {\n  --fa: \"\\f6f2\";\n}\n.fa-pills {\n  --fa: \"\\f484\";\n}\n.fa-face-grin-wide,.fa-grin-alt {\n  --fa: \"\\f581\";\n}\n.fa-tooth {\n  --fa: \"\\f5c9\";\n}\n.fa-v {\n  --fa: \"\\56\";\n}\n.fa-bangladeshi-taka-sign {\n  --fa: \"\\e2e6\";\n}\n.fa-bicycle {\n  --fa: \"\\f206\";\n}\n.fa-rod-asclepius,.fa-rod-snake,.fa-staff-aesculapius,.fa-staff-snake {\n  --fa: \"\\e579\";\n}\n.fa-head-side-cough-slash {\n  --fa: \"\\e062\";\n}\n.fa-ambulance,.fa-truck-medical {\n  --fa: \"\\f0f9\";\n}\n.fa-wheat-awn-circle-exclamation {\n  --fa: \"\\e598\";\n}\n.fa-snowman {\n  --fa: \"\\f7d0\";\n}\n.fa-mortar-pestle {\n  --fa: \"\\f5a7\";\n}\n.fa-road-barrier {\n  --fa: \"\\e562\";\n}\n.fa-school {\n  --fa: \"\\f549\";\n}\n.fa-igloo {\n  --fa: \"\\f7ae\";\n}\n.fa-joint {\n  --fa: \"\\f595\";\n}\n.fa-angle-right {\n  --fa: \"\\f105\";\n}\n.fa-horse {\n  --fa: \"\\f6f0\";\n}\n.fa-q {\n  --fa: \"\\51\";\n}\n.fa-g {\n  --fa: \"\\47\";\n}\n.fa-notes-medical {\n  --fa: \"\\f481\";\n}\n.fa-temperature-2,.fa-temperature-half,.fa-thermometer-2,.fa-thermometer-half {\n  --fa: \"\\f2c9\";\n}\n.fa-dong-sign {\n  --fa: \"\\e169\";\n}\n.fa-capsules {\n  --fa: \"\\f46b\";\n}\n.fa-poo-bolt,.fa-poo-storm {\n  --fa: \"\\f75a\";\n}\n.fa-face-frown-open,.fa-frown-open {\n  --fa: \"\\f57a\";\n}\n.fa-hand-point-up {\n  --fa: \"\\f0a6\";\n}\n.fa-money-bill {\n  --fa: \"\\f0d6\";\n}\n.fa-bookmark {\n  --fa: \"\\f02e\";\n}\n.fa-align-justify {\n  --fa: \"\\f039\";\n}\n.fa-umbrella-beach {\n  --fa: \"\\f5ca\";\n}\n.fa-helmet-un {\n  --fa: \"\\e503\";\n}\n.fa-bullseye {\n  --fa: \"\\f140\";\n}\n.fa-bacon {\n  --fa: \"\\f7e5\";\n}\n.fa-hand-point-down {\n  --fa: \"\\f0a7\";\n}\n.fa-arrow-up-from-bracket {\n  --fa: \"\\e09a\";\n}\n.fa-folder,.fa-folder-blank {\n  --fa: \"\\f07b\";\n}\n.fa-file-medical-alt,.fa-file-waveform {\n  --fa: \"\\f478\";\n}\n.fa-radiation {\n  --fa: \"\\f7b9\";\n}\n.fa-chart-simple {\n  --fa: \"\\e473\";\n}\n.fa-mars-stroke {\n  --fa: \"\\f229\";\n}\n.fa-vial {\n  --fa: \"\\f492\";\n}\n.fa-dashboard,.fa-gauge,.fa-gauge-med,.fa-tachometer-alt-average {\n  --fa: \"\\f624\";\n}\n.fa-magic-wand-sparkles,.fa-wand-magic-sparkles {\n  --fa: \"\\e2ca\";\n}\n.fa-e {\n  --fa: \"\\45\";\n}\n.fa-pen-alt,.fa-pen-clip {\n  --fa: \"\\f305\";\n}\n.fa-bridge-circle-exclamation {\n  --fa: \"\\e4ca\";\n}\n.fa-user {\n  --fa: \"\\f007\";\n}\n.fa-school-circle-check {\n  --fa: \"\\e56b\";\n}\n.fa-dumpster {\n  --fa: \"\\f793\";\n}\n.fa-shuttle-van,.fa-van-shuttle {\n  --fa: \"\\f5b6\";\n}\n.fa-building-user {\n  --fa: \"\\e4da\";\n}\n.fa-caret-square-left,.fa-square-caret-left {\n  --fa: \"\\f191\";\n}\n.fa-highlighter {\n  --fa: \"\\f591\";\n}\n.fa-key {\n  --fa: \"\\f084\";\n}\n.fa-bullhorn {\n  --fa: \"\\f0a1\";\n}\n.fa-globe {\n  --fa: \"\\f0ac\";\n}\n.fa-synagogue {\n  --fa: \"\\f69b\";\n}\n.fa-person-half-dress {\n  --fa: \"\\e548\";\n}\n.fa-road-bridge {\n  --fa: \"\\e563\";\n}\n.fa-location-arrow {\n  --fa: \"\\f124\";\n}\n.fa-c {\n  --fa: \"\\43\";\n}\n.fa-tablet-button {\n  --fa: \"\\f10a\";\n}\n.fa-building-lock {\n  --fa: \"\\e4d6\";\n}\n.fa-pizza-slice {\n  --fa: \"\\f818\";\n}\n.fa-money-bill-wave {\n  --fa: \"\\f53a\";\n}\n.fa-area-chart,.fa-chart-area {\n  --fa: \"\\f1fe\";\n}\n.fa-house-flag {\n  --fa: \"\\e50d\";\n}\n.fa-person-circle-minus {\n  --fa: \"\\e540\";\n}\n.fa-ban,.fa-cancel {\n  --fa: \"\\f05e\";\n}\n.fa-camera-rotate {\n  --fa: \"\\e0d8\";\n}\n.fa-air-freshener,.fa-spray-can-sparkles {\n  --fa: \"\\f5d0\";\n}\n.fa-star {\n  --fa: \"\\f005\";\n}\n.fa-repeat {\n  --fa: \"\\f363\";\n}\n.fa-cross {\n  --fa: \"\\f654\";\n}\n.fa-box {\n  --fa: \"\\f466\";\n}\n.fa-venus-mars {\n  --fa: \"\\f228\";\n}\n.fa-arrow-pointer,.fa-mouse-pointer {\n  --fa: \"\\f245\";\n}\n.fa-expand-arrows-alt,.fa-maximize {\n  --fa: \"\\f31e\";\n}\n.fa-charging-station {\n  --fa: \"\\f5e7\";\n}\n.fa-shapes,.fa-triangle-circle-square {\n  --fa: \"\\f61f\";\n}\n.fa-random,.fa-shuffle {\n  --fa: \"\\f074\";\n}\n.fa-person-running,.fa-running {\n  --fa: \"\\f70c\";\n}\n.fa-mobile-retro {\n  --fa: \"\\e527\";\n}\n.fa-grip-lines-vertical {\n  --fa: \"\\f7a5\";\n}\n.fa-spider {\n  --fa: \"\\f717\";\n}\n.fa-hands-bound {\n  --fa: \"\\e4f9\";\n}\n.fa-file-invoice-dollar {\n  --fa: \"\\f571\";\n}\n.fa-plane-circle-exclamation {\n  --fa: \"\\e556\";\n}\n.fa-x-ray {\n  --fa: \"\\f497\";\n}\n.fa-spell-check {\n  --fa: \"\\f891\";\n}\n.fa-slash {\n  --fa: \"\\f715\";\n}\n.fa-computer-mouse,.fa-mouse {\n  --fa: \"\\f8cc\";\n}\n.fa-arrow-right-to-bracket,.fa-sign-in {\n  --fa: \"\\f090\";\n}\n.fa-shop-slash,.fa-store-alt-slash {\n  --fa: \"\\e070\";\n}\n.fa-server {\n  --fa: \"\\f233\";\n}\n.fa-virus-covid-slash {\n  --fa: \"\\e4a9\";\n}\n.fa-shop-lock {\n  --fa: \"\\e4a5\";\n}\n.fa-hourglass-1,.fa-hourglass-start {\n  --fa: \"\\f251\";\n}\n.fa-blender-phone {\n  --fa: \"\\f6b6\";\n}\n.fa-building-wheat {\n  --fa: \"\\e4db\";\n}\n.fa-person-breastfeeding {\n  --fa: \"\\e53a\";\n}\n.fa-right-to-bracket,.fa-sign-in-alt {\n  --fa: \"\\f2f6\";\n}\n.fa-venus {\n  --fa: \"\\f221\";\n}\n.fa-passport {\n  --fa: \"\\f5ab\";\n}\n.fa-thumb-tack-slash,.fa-thumbtack-slash {\n  --fa: \"\\e68f\";\n}\n.fa-heart-pulse,.fa-heartbeat {\n  --fa: \"\\f21e\";\n}\n.fa-people-carry,.fa-people-carry-box {\n  --fa: \"\\f4ce\";\n}\n.fa-temperature-high {\n  --fa: \"\\f769\";\n}\n.fa-microchip {\n  --fa: \"\\f2db\";\n}\n.fa-crown {\n  --fa: \"\\f521\";\n}\n.fa-weight-hanging {\n  --fa: \"\\f5cd\";\n}\n.fa-xmarks-lines {\n  --fa: \"\\e59a\";\n}\n.fa-file-prescription {\n  --fa: \"\\f572\";\n}\n.fa-weight,.fa-weight-scale {\n  --fa: \"\\f496\";\n}\n.fa-user-friends,.fa-user-group {\n  --fa: \"\\f500\";\n}\n.fa-arrow-up-a-z,.fa-sort-alpha-up {\n  --fa: \"\\f15e\";\n}\n.fa-chess-knight {\n  --fa: \"\\f441\";\n}\n.fa-face-laugh-squint,.fa-laugh-squint {\n  --fa: \"\\f59b\";\n}\n.fa-wheelchair {\n  --fa: \"\\f193\";\n}\n.fa-arrow-circle-up,.fa-circle-arrow-up {\n  --fa: \"\\f0aa\";\n}\n.fa-toggle-on {\n  --fa: \"\\f205\";\n}\n.fa-person-walking,.fa-walking {\n  --fa: \"\\f554\";\n}\n.fa-l {\n  --fa: \"\\4c\";\n}\n.fa-fire {\n  --fa: \"\\f06d\";\n}\n.fa-bed-pulse,.fa-procedures {\n  --fa: \"\\f487\";\n}\n.fa-shuttle-space,.fa-space-shuttle {\n  --fa: \"\\f197\";\n}\n.fa-face-laugh,.fa-laugh {\n  --fa: \"\\f599\";\n}\n.fa-folder-open {\n  --fa: \"\\f07c\";\n}\n.fa-heart-circle-plus {\n  --fa: \"\\e500\";\n}\n.fa-code-fork {\n  --fa: \"\\e13b\";\n}\n.fa-city {\n  --fa: \"\\f64f\";\n}\n.fa-microphone-alt,.fa-microphone-lines {\n  --fa: \"\\f3c9\";\n}\n.fa-pepper-hot {\n  --fa: \"\\f816\";\n}\n.fa-unlock {\n  --fa: \"\\f09c\";\n}\n.fa-colon-sign {\n  --fa: \"\\e140\";\n}\n.fa-headset {\n  --fa: \"\\f590\";\n}\n.fa-store-slash {\n  --fa: \"\\e071\";\n}\n.fa-road-circle-xmark {\n  --fa: \"\\e566\";\n}\n.fa-user-minus {\n  --fa: \"\\f503\";\n}\n.fa-mars-stroke-up,.fa-mars-stroke-v {\n  --fa: \"\\f22a\";\n}\n.fa-champagne-glasses,.fa-glass-cheers {\n  --fa: \"\\f79f\";\n}\n.fa-clipboard {\n  --fa: \"\\f328\";\n}\n.fa-house-circle-exclamation {\n  --fa: \"\\e50a\";\n}\n.fa-file-arrow-up,.fa-file-upload {\n  --fa: \"\\f574\";\n}\n.fa-wifi,.fa-wifi-3,.fa-wifi-strong {\n  --fa: \"\\f1eb\";\n}\n.fa-bath,.fa-bathtub {\n  --fa: \"\\f2cd\";\n}\n.fa-underline {\n  --fa: \"\\f0cd\";\n}\n.fa-user-edit,.fa-user-pen {\n  --fa: \"\\f4ff\";\n}\n.fa-signature {\n  --fa: \"\\f5b7\";\n}\n.fa-stroopwafel {\n  --fa: \"\\f551\";\n}\n.fa-bold {\n  --fa: \"\\f032\";\n}\n.fa-anchor-lock {\n  --fa: \"\\e4ad\";\n}\n.fa-building-ngo {\n  --fa: \"\\e4d7\";\n}\n.fa-manat-sign {\n  --fa: \"\\e1d5\";\n}\n.fa-not-equal {\n  --fa: \"\\f53e\";\n}\n.fa-border-style,.fa-border-top-left {\n  --fa: \"\\f853\";\n}\n.fa-map-location-dot,.fa-map-marked-alt {\n  --fa: \"\\f5a0\";\n}\n.fa-jedi {\n  --fa: \"\\f669\";\n}\n.fa-poll,.fa-square-poll-vertical {\n  --fa: \"\\f681\";\n}\n.fa-mug-hot {\n  --fa: \"\\f7b6\";\n}\n.fa-battery-car,.fa-car-battery {\n  --fa: \"\\f5df\";\n}\n.fa-gift {\n  --fa: \"\\f06b\";\n}\n.fa-dice-two {\n  --fa: \"\\f528\";\n}\n.fa-chess-queen {\n  --fa: \"\\f445\";\n}\n.fa-glasses {\n  --fa: \"\\f530\";\n}\n.fa-chess-board {\n  --fa: \"\\f43c\";\n}\n.fa-building-circle-check {\n  --fa: \"\\e4d2\";\n}\n.fa-person-chalkboard {\n  --fa: \"\\e53d\";\n}\n.fa-mars-stroke-h,.fa-mars-stroke-right {\n  --fa: \"\\f22b\";\n}\n.fa-hand-back-fist,.fa-hand-rock {\n  --fa: \"\\f255\";\n}\n.fa-caret-square-up,.fa-square-caret-up {\n  --fa: \"\\f151\";\n}\n.fa-cloud-showers-water {\n  --fa: \"\\e4e4\";\n}\n.fa-bar-chart,.fa-chart-bar {\n  --fa: \"\\f080\";\n}\n.fa-hands-bubbles,.fa-hands-wash {\n  --fa: \"\\e05e\";\n}\n.fa-less-than-equal {\n  --fa: \"\\f537\";\n}\n.fa-train {\n  --fa: \"\\f238\";\n}\n.fa-eye-low-vision,.fa-low-vision {\n  --fa: \"\\f2a8\";\n}\n.fa-crow {\n  --fa: \"\\f520\";\n}\n.fa-sailboat {\n  --fa: \"\\e445\";\n}\n.fa-window-restore {\n  --fa: \"\\f2d2\";\n}\n.fa-plus-square,.fa-square-plus {\n  --fa: \"\\f0fe\";\n}\n.fa-torii-gate {\n  --fa: \"\\f6a1\";\n}\n.fa-frog {\n  --fa: \"\\f52e\";\n}\n.fa-bucket {\n  --fa: \"\\e4cf\";\n}\n.fa-image {\n  --fa: \"\\f03e\";\n}\n.fa-microphone {\n  --fa: \"\\f130\";\n}\n.fa-cow {\n  --fa: \"\\f6c8\";\n}\n.fa-caret-up {\n  --fa: \"\\f0d8\";\n}\n.fa-screwdriver {\n  --fa: \"\\f54a\";\n}\n.fa-folder-closed {\n  --fa: \"\\e185\";\n}\n.fa-house-tsunami {\n  --fa: \"\\e515\";\n}\n.fa-square-nfi {\n  --fa: \"\\e576\";\n}\n.fa-arrow-up-from-ground-water {\n  --fa: \"\\e4b5\";\n}\n.fa-glass-martini-alt,.fa-martini-glass {\n  --fa: \"\\f57b\";\n}\n.fa-square-binary {\n  --fa: \"\\e69b\";\n}\n.fa-rotate-back,.fa-rotate-backward,.fa-rotate-left,.fa-undo-alt {\n  --fa: \"\\f2ea\";\n}\n.fa-columns,.fa-table-columns {\n  --fa: \"\\f0db\";\n}\n.fa-lemon {\n  --fa: \"\\f094\";\n}\n.fa-head-side-mask {\n  --fa: \"\\e063\";\n}\n.fa-handshake {\n  --fa: \"\\f2b5\";\n}\n.fa-gem {\n  --fa: \"\\f3a5\";\n}\n.fa-dolly,.fa-dolly-box {\n  --fa: \"\\f472\";\n}\n.fa-smoking {\n  --fa: \"\\f48d\";\n}\n.fa-compress-arrows-alt,.fa-minimize {\n  --fa: \"\\f78c\";\n}\n.fa-monument {\n  --fa: \"\\f5a6\";\n}\n.fa-snowplow {\n  --fa: \"\\f7d2\";\n}\n.fa-angle-double-right,.fa-angles-right {\n  --fa: \"\\f101\";\n}\n.fa-cannabis {\n  --fa: \"\\f55f\";\n}\n.fa-circle-play,.fa-play-circle {\n  --fa: \"\\f144\";\n}\n.fa-tablets {\n  --fa: \"\\f490\";\n}\n.fa-ethernet {\n  --fa: \"\\f796\";\n}\n.fa-eur,.fa-euro,.fa-euro-sign {\n  --fa: \"\\f153\";\n}\n.fa-chair {\n  --fa: \"\\f6c0\";\n}\n.fa-check-circle,.fa-circle-check {\n  --fa: \"\\f058\";\n}\n.fa-circle-stop,.fa-stop-circle {\n  --fa: \"\\f28d\";\n}\n.fa-compass-drafting,.fa-drafting-compass {\n  --fa: \"\\f568\";\n}\n.fa-plate-wheat {\n  --fa: \"\\e55a\";\n}\n.fa-icicles {\n  --fa: \"\\f7ad\";\n}\n.fa-person-shelter {\n  --fa: \"\\e54f\";\n}\n.fa-neuter {\n  --fa: \"\\f22c\";\n}\n.fa-id-badge {\n  --fa: \"\\f2c1\";\n}\n.fa-marker {\n  --fa: \"\\f5a1\";\n}\n.fa-face-laugh-beam,.fa-laugh-beam {\n  --fa: \"\\f59a\";\n}\n.fa-helicopter-symbol {\n  --fa: \"\\e502\";\n}\n.fa-universal-access {\n  --fa: \"\\f29a\";\n}\n.fa-chevron-circle-up,.fa-circle-chevron-up {\n  --fa: \"\\f139\";\n}\n.fa-lari-sign {\n  --fa: \"\\e1c8\";\n}\n.fa-volcano {\n  --fa: \"\\f770\";\n}\n.fa-person-walking-dashed-line-arrow-right {\n  --fa: \"\\e553\";\n}\n.fa-gbp,.fa-pound-sign,.fa-sterling-sign {\n  --fa: \"\\f154\";\n}\n.fa-viruses {\n  --fa: \"\\e076\";\n}\n.fa-square-person-confined {\n  --fa: \"\\e577\";\n}\n.fa-user-tie {\n  --fa: \"\\f508\";\n}\n.fa-arrow-down-long,.fa-long-arrow-down {\n  --fa: \"\\f175\";\n}\n.fa-tent-arrow-down-to-line {\n  --fa: \"\\e57e\";\n}\n.fa-certificate {\n  --fa: \"\\f0a3\";\n}\n.fa-mail-reply-all,.fa-reply-all {\n  --fa: \"\\f122\";\n}\n.fa-suitcase {\n  --fa: \"\\f0f2\";\n}\n.fa-person-skating,.fa-skating {\n  --fa: \"\\f7c5\";\n}\n.fa-filter-circle-dollar,.fa-funnel-dollar {\n  --fa: \"\\f662\";\n}\n.fa-camera-retro {\n  --fa: \"\\f083\";\n}\n.fa-arrow-circle-down,.fa-circle-arrow-down {\n  --fa: \"\\f0ab\";\n}\n.fa-arrow-right-to-file,.fa-file-import {\n  --fa: \"\\f56f\";\n}\n.fa-external-link-square,.fa-square-arrow-up-right {\n  --fa: \"\\f14c\";\n}\n.fa-box-open {\n  --fa: \"\\f49e\";\n}\n.fa-scroll {\n  --fa: \"\\f70e\";\n}\n.fa-spa {\n  --fa: \"\\f5bb\";\n}\n.fa-location-pin-lock {\n  --fa: \"\\e51f\";\n}\n.fa-pause {\n  --fa: \"\\f04c\";\n}\n.fa-hill-avalanche {\n  --fa: \"\\e507\";\n}\n.fa-temperature-0,.fa-temperature-empty,.fa-thermometer-0,.fa-thermometer-empty {\n  --fa: \"\\f2cb\";\n}\n.fa-bomb {\n  --fa: \"\\f1e2\";\n}\n.fa-registered {\n  --fa: \"\\f25d\";\n}\n.fa-address-card,.fa-contact-card,.fa-vcard {\n  --fa: \"\\f2bb\";\n}\n.fa-balance-scale-right,.fa-scale-unbalanced-flip {\n  --fa: \"\\f516\";\n}\n.fa-subscript {\n  --fa: \"\\f12c\";\n}\n.fa-diamond-turn-right,.fa-directions {\n  --fa: \"\\f5eb\";\n}\n.fa-burst {\n  --fa: \"\\e4dc\";\n}\n.fa-house-laptop,.fa-laptop-house {\n  --fa: \"\\e066\";\n}\n.fa-face-tired,.fa-tired {\n  --fa: \"\\f5c8\";\n}\n.fa-money-bills {\n  --fa: \"\\e1f3\";\n}\n.fa-smog {\n  --fa: \"\\f75f\";\n}\n.fa-crutch {\n  --fa: \"\\f7f7\";\n}\n.fa-cloud-arrow-up,.fa-cloud-upload,.fa-cloud-upload-alt {\n  --fa: \"\\f0ee\";\n}\n.fa-palette {\n  --fa: \"\\f53f\";\n}\n.fa-arrows-turn-right {\n  --fa: \"\\e4c0\";\n}\n.fa-vest {\n  --fa: \"\\e085\";\n}\n.fa-ferry {\n  --fa: \"\\e4ea\";\n}\n.fa-arrows-down-to-people {\n  --fa: \"\\e4b9\";\n}\n.fa-seedling,.fa-sprout {\n  --fa: \"\\f4d8\";\n}\n.fa-arrows-alt-h,.fa-left-right {\n  --fa: \"\\f337\";\n}\n.fa-boxes-packing {\n  --fa: \"\\e4c7\";\n}\n.fa-arrow-circle-left,.fa-circle-arrow-left {\n  --fa: \"\\f0a8\";\n}\n.fa-group-arrows-rotate {\n  --fa: \"\\e4f6\";\n}\n.fa-bowl-food {\n  --fa: \"\\e4c6\";\n}\n.fa-candy-cane {\n  --fa: \"\\f786\";\n}\n.fa-arrow-down-wide-short,.fa-sort-amount-asc,.fa-sort-amount-down {\n  --fa: \"\\f160\";\n}\n.fa-cloud-bolt,.fa-thunderstorm {\n  --fa: \"\\f76c\";\n}\n.fa-remove-format,.fa-text-slash {\n  --fa: \"\\f87d\";\n}\n.fa-face-smile-wink,.fa-smile-wink {\n  --fa: \"\\f4da\";\n}\n.fa-file-word {\n  --fa: \"\\f1c2\";\n}\n.fa-file-powerpoint {\n  --fa: \"\\f1c4\";\n}\n.fa-arrows-h,.fa-arrows-left-right {\n  --fa: \"\\f07e\";\n}\n.fa-house-lock {\n  --fa: \"\\e510\";\n}\n.fa-cloud-arrow-down,.fa-cloud-download,.fa-cloud-download-alt {\n  --fa: \"\\f0ed\";\n}\n.fa-children {\n  --fa: \"\\e4e1\";\n}\n.fa-blackboard,.fa-chalkboard {\n  --fa: \"\\f51b\";\n}\n.fa-user-alt-slash,.fa-user-large-slash {\n  --fa: \"\\f4fa\";\n}\n.fa-envelope-open {\n  --fa: \"\\f2b6\";\n}\n.fa-handshake-alt-slash,.fa-handshake-simple-slash {\n  --fa: \"\\e05f\";\n}\n.fa-mattress-pillow {\n  --fa: \"\\e525\";\n}\n.fa-guarani-sign {\n  --fa: \"\\e19a\";\n}\n.fa-arrows-rotate,.fa-refresh,.fa-sync {\n  --fa: \"\\f021\";\n}\n.fa-fire-extinguisher {\n  --fa: \"\\f134\";\n}\n.fa-cruzeiro-sign {\n  --fa: \"\\e152\";\n}\n.fa-greater-than-equal {\n  --fa: \"\\f532\";\n}\n.fa-shield-alt,.fa-shield-halved {\n  --fa: \"\\f3ed\";\n}\n.fa-atlas,.fa-book-atlas {\n  --fa: \"\\f558\";\n}\n.fa-virus {\n  --fa: \"\\e074\";\n}\n.fa-envelope-circle-check {\n  --fa: \"\\e4e8\";\n}\n.fa-layer-group {\n  --fa: \"\\f5fd\";\n}\n.fa-arrows-to-dot {\n  --fa: \"\\e4be\";\n}\n.fa-archway {\n  --fa: \"\\f557\";\n}\n.fa-heart-circle-check {\n  --fa: \"\\e4fd\";\n}\n.fa-house-chimney-crack,.fa-house-damage {\n  --fa: \"\\f6f1\";\n}\n.fa-file-archive,.fa-file-zipper {\n  --fa: \"\\f1c6\";\n}\n.fa-square {\n  --fa: \"\\f0c8\";\n}\n.fa-glass-martini,.fa-martini-glass-empty {\n  --fa: \"\\f000\";\n}\n.fa-couch {\n  --fa: \"\\f4b8\";\n}\n.fa-cedi-sign {\n  --fa: \"\\e0df\";\n}\n.fa-italic {\n  --fa: \"\\f033\";\n}\n.fa-table-cells-column-lock {\n  --fa: \"\\e678\";\n}\n.fa-church {\n  --fa: \"\\f51d\";\n}\n.fa-comments-dollar {\n  --fa: \"\\f653\";\n}\n.fa-democrat {\n  --fa: \"\\f747\";\n}\n.fa-z {\n  --fa: \"\\5a\";\n}\n.fa-person-skiing,.fa-skiing {\n  --fa: \"\\f7c9\";\n}\n.fa-road-lock {\n  --fa: \"\\e567\";\n}\n.fa-a {\n  --fa: \"\\41\";\n}\n.fa-temperature-arrow-down,.fa-temperature-down {\n  --fa: \"\\e03f\";\n}\n.fa-feather-alt,.fa-feather-pointed {\n  --fa: \"\\f56b\";\n}\n.fa-p {\n  --fa: \"\\50\";\n}\n.fa-snowflake {\n  --fa: \"\\f2dc\";\n}\n.fa-newspaper {\n  --fa: \"\\f1ea\";\n}\n.fa-ad,.fa-rectangle-ad {\n  --fa: \"\\f641\";\n}\n.fa-arrow-circle-right,.fa-circle-arrow-right {\n  --fa: \"\\f0a9\";\n}\n.fa-filter-circle-xmark {\n  --fa: \"\\e17b\";\n}\n.fa-locust {\n  --fa: \"\\e520\";\n}\n.fa-sort,.fa-unsorted {\n  --fa: \"\\f0dc\";\n}\n.fa-list-1-2,.fa-list-numeric,.fa-list-ol {\n  --fa: \"\\f0cb\";\n}\n.fa-person-dress-burst {\n  --fa: \"\\e544\";\n}\n.fa-money-check-alt,.fa-money-check-dollar {\n  --fa: \"\\f53d\";\n}\n.fa-vector-square {\n  --fa: \"\\f5cb\";\n}\n.fa-bread-slice {\n  --fa: \"\\f7ec\";\n}\n.fa-language {\n  --fa: \"\\f1ab\";\n}\n.fa-face-kiss-wink-heart,.fa-kiss-wink-heart {\n  --fa: \"\\f598\";\n}\n.fa-filter {\n  --fa: \"\\f0b0\";\n}\n.fa-question {\n  --fa: \"\\3f\";\n}\n.fa-file-signature {\n  --fa: \"\\f573\";\n}\n.fa-arrows-alt,.fa-up-down-left-right {\n  --fa: \"\\f0b2\";\n}\n.fa-house-chimney-user {\n  --fa: \"\\e065\";\n}\n.fa-hand-holding-heart {\n  --fa: \"\\f4be\";\n}\n.fa-puzzle-piece {\n  --fa: \"\\f12e\";\n}\n.fa-money-check {\n  --fa: \"\\f53c\";\n}\n.fa-star-half-alt,.fa-star-half-stroke {\n  --fa: \"\\f5c0\";\n}\n.fa-code {\n  --fa: \"\\f121\";\n}\n.fa-glass-whiskey,.fa-whiskey-glass {\n  --fa: \"\\f7a0\";\n}\n.fa-building-circle-exclamation {\n  --fa: \"\\e4d3\";\n}\n.fa-magnifying-glass-chart {\n  --fa: \"\\e522\";\n}\n.fa-arrow-up-right-from-square,.fa-external-link {\n  --fa: \"\\f08e\";\n}\n.fa-cubes-stacked {\n  --fa: \"\\e4e6\";\n}\n.fa-krw,.fa-won,.fa-won-sign {\n  --fa: \"\\f159\";\n}\n.fa-virus-covid {\n  --fa: \"\\e4a8\";\n}\n.fa-austral-sign {\n  --fa: \"\\e0a9\";\n}\n.fa-f {\n  --fa: \"\\46\";\n}\n.fa-leaf {\n  --fa: \"\\f06c\";\n}\n.fa-road {\n  --fa: \"\\f018\";\n}\n.fa-cab,.fa-taxi {\n  --fa: \"\\f1ba\";\n}\n.fa-person-circle-plus {\n  --fa: \"\\e541\";\n}\n.fa-chart-pie,.fa-pie-chart {\n  --fa: \"\\f200\";\n}\n.fa-bolt-lightning {\n  --fa: \"\\e0b7\";\n}\n.fa-sack-xmark {\n  --fa: \"\\e56a\";\n}\n.fa-file-excel {\n  --fa: \"\\f1c3\";\n}\n.fa-file-contract {\n  --fa: \"\\f56c\";\n}\n.fa-fish-fins {\n  --fa: \"\\e4f2\";\n}\n.fa-building-flag {\n  --fa: \"\\e4d5\";\n}\n.fa-face-grin-beam,.fa-grin-beam {\n  --fa: \"\\f582\";\n}\n.fa-object-ungroup {\n  --fa: \"\\f248\";\n}\n.fa-poop {\n  --fa: \"\\f619\";\n}\n.fa-location-pin,.fa-map-marker {\n  --fa: \"\\f041\";\n}\n.fa-kaaba {\n  --fa: \"\\f66b\";\n}\n.fa-toilet-paper {\n  --fa: \"\\f71e\";\n}\n.fa-hard-hat,.fa-hat-hard,.fa-helmet-safety {\n  --fa: \"\\f807\";\n}\n.fa-eject {\n  --fa: \"\\f052\";\n}\n.fa-arrow-alt-circle-right,.fa-circle-right {\n  --fa: \"\\f35a\";\n}\n.fa-plane-circle-check {\n  --fa: \"\\e555\";\n}\n.fa-face-rolling-eyes,.fa-meh-rolling-eyes {\n  --fa: \"\\f5a5\";\n}\n.fa-object-group {\n  --fa: \"\\f247\";\n}\n.fa-chart-line,.fa-line-chart {\n  --fa: \"\\f201\";\n}\n.fa-mask-ventilator {\n  --fa: \"\\e524\";\n}\n.fa-arrow-right {\n  --fa: \"\\f061\";\n}\n.fa-map-signs,.fa-signs-post {\n  --fa: \"\\f277\";\n}\n.fa-cash-register {\n  --fa: \"\\f788\";\n}\n.fa-person-circle-question {\n  --fa: \"\\e542\";\n}\n.fa-h {\n  --fa: \"\\48\";\n}\n.fa-tarp {\n  --fa: \"\\e57b\";\n}\n.fa-screwdriver-wrench,.fa-tools {\n  --fa: \"\\f7d9\";\n}\n.fa-arrows-to-eye {\n  --fa: \"\\e4bf\";\n}\n.fa-plug-circle-bolt {\n  --fa: \"\\e55b\";\n}\n.fa-heart {\n  --fa: \"\\f004\";\n}\n.fa-mars-and-venus {\n  --fa: \"\\f224\";\n}\n.fa-home-user,.fa-house-user {\n  --fa: \"\\e1b0\";\n}\n.fa-dumpster-fire {\n  --fa: \"\\f794\";\n}\n.fa-house-crack {\n  --fa: \"\\e3b1\";\n}\n.fa-cocktail,.fa-martini-glass-citrus {\n  --fa: \"\\f561\";\n}\n.fa-face-surprise,.fa-surprise {\n  --fa: \"\\f5c2\";\n}\n.fa-bottle-water {\n  --fa: \"\\e4c5\";\n}\n.fa-circle-pause,.fa-pause-circle {\n  --fa: \"\\f28b\";\n}\n.fa-toilet-paper-slash {\n  --fa: \"\\e072\";\n}\n.fa-apple-alt,.fa-apple-whole {\n  --fa: \"\\f5d1\";\n}\n.fa-kitchen-set {\n  --fa: \"\\e51a\";\n}\n.fa-r {\n  --fa: \"\\52\";\n}\n.fa-temperature-1,.fa-temperature-quarter,.fa-thermometer-1,.fa-thermometer-quarter {\n  --fa: \"\\f2ca\";\n}\n.fa-cube {\n  --fa: \"\\f1b2\";\n}\n.fa-bitcoin-sign {\n  --fa: \"\\e0b4\";\n}\n.fa-shield-dog {\n  --fa: \"\\e573\";\n}\n.fa-solar-panel {\n  --fa: \"\\f5ba\";\n}\n.fa-lock-open {\n  --fa: \"\\f3c1\";\n}\n.fa-elevator {\n  --fa: \"\\e16d\";\n}\n.fa-money-bill-transfer {\n  --fa: \"\\e528\";\n}\n.fa-money-bill-trend-up {\n  --fa: \"\\e529\";\n}\n.fa-house-flood-water-circle-arrow-right {\n  --fa: \"\\e50f\";\n}\n.fa-poll-h,.fa-square-poll-horizontal {\n  --fa: \"\\f682\";\n}\n.fa-circle {\n  --fa: \"\\f111\";\n}\n.fa-backward-fast,.fa-fast-backward {\n  --fa: \"\\f049\";\n}\n.fa-recycle {\n  --fa: \"\\f1b8\";\n}\n.fa-user-astronaut {\n  --fa: \"\\f4fb\";\n}\n.fa-plane-slash {\n  --fa: \"\\e069\";\n}\n.fa-trademark {\n  --fa: \"\\f25c\";\n}\n.fa-basketball,.fa-basketball-ball {\n  --fa: \"\\f434\";\n}\n.fa-satellite-dish {\n  --fa: \"\\f7c0\";\n}\n.fa-arrow-alt-circle-up,.fa-circle-up {\n  --fa: \"\\f35b\";\n}\n.fa-mobile-alt,.fa-mobile-screen-button {\n  --fa: \"\\f3cd\";\n}\n.fa-volume-high,.fa-volume-up {\n  --fa: \"\\f028\";\n}\n.fa-users-rays {\n  --fa: \"\\e593\";\n}\n.fa-wallet {\n  --fa: \"\\f555\";\n}\n.fa-clipboard-check {\n  --fa: \"\\f46c\";\n}\n.fa-file-audio {\n  --fa: \"\\f1c7\";\n}\n.fa-burger,.fa-hamburger {\n  --fa: \"\\f805\";\n}\n.fa-wrench {\n  --fa: \"\\f0ad\";\n}\n.fa-bugs {\n  --fa: \"\\e4d0\";\n}\n.fa-rupee,.fa-rupee-sign {\n  --fa: \"\\f156\";\n}\n.fa-file-image {\n  --fa: \"\\f1c5\";\n}\n.fa-circle-question,.fa-question-circle {\n  --fa: \"\\f059\";\n}\n.fa-plane-departure {\n  --fa: \"\\f5b0\";\n}\n.fa-handshake-slash {\n  --fa: \"\\e060\";\n}\n.fa-book-bookmark {\n  --fa: \"\\e0bb\";\n}\n.fa-code-branch {\n  --fa: \"\\f126\";\n}\n.fa-hat-cowboy {\n  --fa: \"\\f8c0\";\n}\n.fa-bridge {\n  --fa: \"\\e4c8\";\n}\n.fa-phone-alt,.fa-phone-flip {\n  --fa: \"\\f879\";\n}\n.fa-truck-front {\n  --fa: \"\\e2b7\";\n}\n.fa-cat {\n  --fa: \"\\f6be\";\n}\n.fa-anchor-circle-exclamation {\n  --fa: \"\\e4ab\";\n}\n.fa-truck-field {\n  --fa: \"\\e58d\";\n}\n.fa-route {\n  --fa: \"\\f4d7\";\n}\n.fa-clipboard-question {\n  --fa: \"\\e4e3\";\n}\n.fa-panorama {\n  --fa: \"\\e209\";\n}\n.fa-comment-medical {\n  --fa: \"\\f7f5\";\n}\n.fa-teeth-open {\n  --fa: \"\\f62f\";\n}\n.fa-file-circle-minus {\n  --fa: \"\\e4ed\";\n}\n.fa-tags {\n  --fa: \"\\f02c\";\n}\n.fa-wine-glass {\n  --fa: \"\\f4e3\";\n}\n.fa-fast-forward,.fa-forward-fast {\n  --fa: \"\\f050\";\n}\n.fa-face-meh-blank,.fa-meh-blank {\n  --fa: \"\\f5a4\";\n}\n.fa-parking,.fa-square-parking {\n  --fa: \"\\f540\";\n}\n.fa-house-signal {\n  --fa: \"\\e012\";\n}\n.fa-bars-progress,.fa-tasks-alt {\n  --fa: \"\\f828\";\n}\n.fa-faucet-drip {\n  --fa: \"\\e006\";\n}\n.fa-cart-flatbed,.fa-dolly-flatbed {\n  --fa: \"\\f474\";\n}\n.fa-ban-smoking,.fa-smoking-ban {\n  --fa: \"\\f54d\";\n}\n.fa-terminal {\n  --fa: \"\\f120\";\n}\n.fa-mobile-button {\n  --fa: \"\\f10b\";\n}\n.fa-house-medical-flag {\n  --fa: \"\\e514\";\n}\n.fa-basket-shopping,.fa-shopping-basket {\n  --fa: \"\\f291\";\n}\n.fa-tape {\n  --fa: \"\\f4db\";\n}\n.fa-bus-alt,.fa-bus-simple {\n  --fa: \"\\f55e\";\n}\n.fa-eye {\n  --fa: \"\\f06e\";\n}\n.fa-face-sad-cry,.fa-sad-cry {\n  --fa: \"\\f5b3\";\n}\n.fa-audio-description {\n  --fa: \"\\f29e\";\n}\n.fa-person-military-to-person {\n  --fa: \"\\e54c\";\n}\n.fa-file-shield {\n  --fa: \"\\e4f0\";\n}\n.fa-user-slash {\n  --fa: \"\\f506\";\n}\n.fa-pen {\n  --fa: \"\\f304\";\n}\n.fa-tower-observation {\n  --fa: \"\\e586\";\n}\n.fa-file-code {\n  --fa: \"\\f1c9\";\n}\n.fa-signal,.fa-signal-5,.fa-signal-perfect {\n  --fa: \"\\f012\";\n}\n.fa-bus {\n  --fa: \"\\f207\";\n}\n.fa-heart-circle-xmark {\n  --fa: \"\\e501\";\n}\n.fa-home-lg,.fa-house-chimney {\n  --fa: \"\\e3af\";\n}\n.fa-window-maximize {\n  --fa: \"\\f2d0\";\n}\n.fa-face-frown,.fa-frown {\n  --fa: \"\\f119\";\n}\n.fa-prescription {\n  --fa: \"\\f5b1\";\n}\n.fa-shop,.fa-store-alt {\n  --fa: \"\\f54f\";\n}\n.fa-floppy-disk,.fa-save {\n  --fa: \"\\f0c7\";\n}\n.fa-vihara {\n  --fa: \"\\f6a7\";\n}\n.fa-balance-scale-left,.fa-scale-unbalanced {\n  --fa: \"\\f515\";\n}\n.fa-sort-asc,.fa-sort-up {\n  --fa: \"\\f0de\";\n}\n.fa-comment-dots,.fa-commenting {\n  --fa: \"\\f4ad\";\n}\n.fa-plant-wilt {\n  --fa: \"\\e5aa\";\n}\n.fa-diamond {\n  --fa: \"\\f219\";\n}\n.fa-face-grin-squint,.fa-grin-squint {\n  --fa: \"\\f585\";\n}\n.fa-hand-holding-dollar,.fa-hand-holding-usd {\n  --fa: \"\\f4c0\";\n}\n.fa-chart-diagram {\n  --fa: \"\\e695\";\n}\n.fa-bacterium {\n  --fa: \"\\e05a\";\n}\n.fa-hand-pointer {\n  --fa: \"\\f25a\";\n}\n.fa-drum-steelpan {\n  --fa: \"\\f56a\";\n}\n.fa-hand-scissors {\n  --fa: \"\\f257\";\n}\n.fa-hands-praying,.fa-praying-hands {\n  --fa: \"\\f684\";\n}\n.fa-arrow-right-rotate,.fa-arrow-rotate-forward,.fa-arrow-rotate-right,.fa-redo {\n  --fa: \"\\f01e\";\n}\n.fa-biohazard {\n  --fa: \"\\f780\";\n}\n.fa-location,.fa-location-crosshairs {\n  --fa: \"\\f601\";\n}\n.fa-mars-double {\n  --fa: \"\\f227\";\n}\n.fa-child-dress {\n  --fa: \"\\e59c\";\n}\n.fa-users-between-lines {\n  --fa: \"\\e591\";\n}\n.fa-lungs-virus {\n  --fa: \"\\e067\";\n}\n.fa-face-grin-tears,.fa-grin-tears {\n  --fa: \"\\f588\";\n}\n.fa-phone {\n  --fa: \"\\f095\";\n}\n.fa-calendar-times,.fa-calendar-xmark {\n  --fa: \"\\f273\";\n}\n.fa-child-reaching {\n  --fa: \"\\e59d\";\n}\n.fa-head-side-virus {\n  --fa: \"\\e064\";\n}\n.fa-user-cog,.fa-user-gear {\n  --fa: \"\\f4fe\";\n}\n.fa-arrow-up-1-9,.fa-sort-numeric-up {\n  --fa: \"\\f163\";\n}\n.fa-door-closed {\n  --fa: \"\\f52a\";\n}\n.fa-shield-virus {\n  --fa: \"\\e06c\";\n}\n.fa-dice-six {\n  --fa: \"\\f526\";\n}\n.fa-mosquito-net {\n  --fa: \"\\e52c\";\n}\n.fa-file-fragment {\n  --fa: \"\\e697\";\n}\n.fa-bridge-water {\n  --fa: \"\\e4ce\";\n}\n.fa-person-booth {\n  --fa: \"\\f756\";\n}\n.fa-text-width {\n  --fa: \"\\f035\";\n}\n.fa-hat-wizard {\n  --fa: \"\\f6e8\";\n}\n.fa-pen-fancy {\n  --fa: \"\\f5ac\";\n}\n.fa-digging,.fa-person-digging {\n  --fa: \"\\f85e\";\n}\n.fa-trash {\n  --fa: \"\\f1f8\";\n}\n.fa-gauge-simple,.fa-gauge-simple-med,.fa-tachometer-average {\n  --fa: \"\\f629\";\n}\n.fa-book-medical {\n  --fa: \"\\f7e6\";\n}\n.fa-poo {\n  --fa: \"\\f2fe\";\n}\n.fa-quote-right,.fa-quote-right-alt {\n  --fa: \"\\f10e\";\n}\n.fa-shirt,.fa-t-shirt,.fa-tshirt {\n  --fa: \"\\f553\";\n}\n.fa-cubes {\n  --fa: \"\\f1b3\";\n}\n.fa-divide {\n  --fa: \"\\f529\";\n}\n.fa-tenge,.fa-tenge-sign {\n  --fa: \"\\f7d7\";\n}\n.fa-headphones {\n  --fa: \"\\f025\";\n}\n.fa-hands-holding {\n  --fa: \"\\f4c2\";\n}\n.fa-hands-clapping {\n  --fa: \"\\e1a8\";\n}\n.fa-republican {\n  --fa: \"\\f75e\";\n}\n.fa-arrow-left {\n  --fa: \"\\f060\";\n}\n.fa-person-circle-xmark {\n  --fa: \"\\e543\";\n}\n.fa-ruler {\n  --fa: \"\\f545\";\n}\n.fa-align-left {\n  --fa: \"\\f036\";\n}\n.fa-dice-d6 {\n  --fa: \"\\f6d1\";\n}\n.fa-restroom {\n  --fa: \"\\f7bd\";\n}\n.fa-j {\n  --fa: \"\\4a\";\n}\n.fa-users-viewfinder {\n  --fa: \"\\e595\";\n}\n.fa-file-video {\n  --fa: \"\\f1c8\";\n}\n.fa-external-link-alt,.fa-up-right-from-square {\n  --fa: \"\\f35d\";\n}\n.fa-table-cells,.fa-th {\n  --fa: \"\\f00a\";\n}\n.fa-file-pdf {\n  --fa: \"\\f1c1\";\n}\n.fa-bible,.fa-book-bible {\n  --fa: \"\\f647\";\n}\n.fa-o {\n  --fa: \"\\4f\";\n}\n.fa-medkit,.fa-suitcase-medical {\n  --fa: \"\\f0fa\";\n}\n.fa-user-secret {\n  --fa: \"\\f21b\";\n}\n.fa-otter {\n  --fa: \"\\f700\";\n}\n.fa-female,.fa-person-dress {\n  --fa: \"\\f182\";\n}\n.fa-comment-dollar {\n  --fa: \"\\f651\";\n}\n.fa-briefcase-clock,.fa-business-time {\n  --fa: \"\\f64a\";\n}\n.fa-table-cells-large,.fa-th-large {\n  --fa: \"\\f009\";\n}\n.fa-book-tanakh,.fa-tanakh {\n  --fa: \"\\f827\";\n}\n.fa-phone-volume,.fa-volume-control-phone {\n  --fa: \"\\f2a0\";\n}\n.fa-hat-cowboy-side {\n  --fa: \"\\f8c1\";\n}\n.fa-clipboard-user {\n  --fa: \"\\f7f3\";\n}\n.fa-child {\n  --fa: \"\\f1ae\";\n}\n.fa-lira-sign {\n  --fa: \"\\f195\";\n}\n.fa-satellite {\n  --fa: \"\\f7bf\";\n}\n.fa-plane-lock {\n  --fa: \"\\e558\";\n}\n.fa-tag {\n  --fa: \"\\f02b\";\n}\n.fa-comment {\n  --fa: \"\\f075\";\n}\n.fa-birthday-cake,.fa-cake,.fa-cake-candles {\n  --fa: \"\\f1fd\";\n}\n.fa-envelope {\n  --fa: \"\\f0e0\";\n}\n.fa-angle-double-up,.fa-angles-up {\n  --fa: \"\\f102\";\n}\n.fa-paperclip {\n  --fa: \"\\f0c6\";\n}\n.fa-arrow-right-to-city {\n  --fa: \"\\e4b3\";\n}\n.fa-ribbon {\n  --fa: \"\\f4d6\";\n}\n.fa-lungs {\n  --fa: \"\\f604\";\n}\n.fa-arrow-up-9-1,.fa-sort-numeric-up-alt {\n  --fa: \"\\f887\";\n}\n.fa-litecoin-sign {\n  --fa: \"\\e1d3\";\n}\n.fa-border-none {\n  --fa: \"\\f850\";\n}\n.fa-circle-nodes {\n  --fa: \"\\e4e2\";\n}\n.fa-parachute-box {\n  --fa: \"\\f4cd\";\n}\n.fa-indent {\n  --fa: \"\\f03c\";\n}\n.fa-truck-field-un {\n  --fa: \"\\e58e\";\n}\n.fa-hourglass,.fa-hourglass-empty {\n  --fa: \"\\f254\";\n}\n.fa-mountain {\n  --fa: \"\\f6fc\";\n}\n.fa-user-doctor,.fa-user-md {\n  --fa: \"\\f0f0\";\n}\n.fa-circle-info,.fa-info-circle {\n  --fa: \"\\f05a\";\n}\n.fa-cloud-meatball {\n  --fa: \"\\f73b\";\n}\n.fa-camera,.fa-camera-alt {\n  --fa: \"\\f030\";\n}\n.fa-square-virus {\n  --fa: \"\\e578\";\n}\n.fa-meteor {\n  --fa: \"\\f753\";\n}\n.fa-car-on {\n  --fa: \"\\e4dd\";\n}\n.fa-sleigh {\n  --fa: \"\\f7cc\";\n}\n.fa-arrow-down-1-9,.fa-sort-numeric-asc,.fa-sort-numeric-down {\n  --fa: \"\\f162\";\n}\n.fa-hand-holding-droplet,.fa-hand-holding-water {\n  --fa: \"\\f4c1\";\n}\n.fa-water {\n  --fa: \"\\f773\";\n}\n.fa-calendar-check {\n  --fa: \"\\f274\";\n}\n.fa-braille {\n  --fa: \"\\f2a1\";\n}\n.fa-prescription-bottle-alt,.fa-prescription-bottle-medical {\n  --fa: \"\\f486\";\n}\n.fa-landmark {\n  --fa: \"\\f66f\";\n}\n.fa-truck {\n  --fa: \"\\f0d1\";\n}\n.fa-crosshairs {\n  --fa: \"\\f05b\";\n}\n.fa-person-cane {\n  --fa: \"\\e53c\";\n}\n.fa-tent {\n  --fa: \"\\e57d\";\n}\n.fa-vest-patches {\n  --fa: \"\\e086\";\n}\n.fa-check-double {\n  --fa: \"\\f560\";\n}\n.fa-arrow-down-a-z,.fa-sort-alpha-asc,.fa-sort-alpha-down {\n  --fa: \"\\f15d\";\n}\n.fa-money-bill-wheat {\n  --fa: \"\\e52a\";\n}\n.fa-cookie {\n  --fa: \"\\f563\";\n}\n.fa-arrow-left-rotate,.fa-arrow-rotate-back,.fa-arrow-rotate-backward,.fa-arrow-rotate-left,.fa-undo {\n  --fa: \"\\f0e2\";\n}\n.fa-hard-drive,.fa-hdd {\n  --fa: \"\\f0a0\";\n}\n.fa-face-grin-squint-tears,.fa-grin-squint-tears {\n  --fa: \"\\f586\";\n}\n.fa-dumbbell {\n  --fa: \"\\f44b\";\n}\n.fa-list-alt,.fa-rectangle-list {\n  --fa: \"\\f022\";\n}\n.fa-tarp-droplet {\n  --fa: \"\\e57c\";\n}\n.fa-house-medical-circle-check {\n  --fa: \"\\e511\";\n}\n.fa-person-skiing-nordic,.fa-skiing-nordic {\n  --fa: \"\\f7ca\";\n}\n.fa-calendar-plus {\n  --fa: \"\\f271\";\n}\n.fa-plane-arrival {\n  --fa: \"\\f5af\";\n}\n.fa-arrow-alt-circle-left,.fa-circle-left {\n  --fa: \"\\f359\";\n}\n.fa-subway,.fa-train-subway {\n  --fa: \"\\f239\";\n}\n.fa-chart-gantt {\n  --fa: \"\\e0e4\";\n}\n.fa-indian-rupee,.fa-indian-rupee-sign,.fa-inr {\n  --fa: \"\\e1bc\";\n}\n.fa-crop-alt,.fa-crop-simple {\n  --fa: \"\\f565\";\n}\n.fa-money-bill-1,.fa-money-bill-alt {\n  --fa: \"\\f3d1\";\n}\n.fa-left-long,.fa-long-arrow-alt-left {\n  --fa: \"\\f30a\";\n}\n.fa-dna {\n  --fa: \"\\f471\";\n}\n.fa-virus-slash {\n  --fa: \"\\e075\";\n}\n.fa-minus,.fa-subtract {\n  --fa: \"\\f068\";\n}\n.fa-chess {\n  --fa: \"\\f439\";\n}\n.fa-arrow-left-long,.fa-long-arrow-left {\n  --fa: \"\\f177\";\n}\n.fa-plug-circle-check {\n  --fa: \"\\e55c\";\n}\n.fa-street-view {\n  --fa: \"\\f21d\";\n}\n.fa-franc-sign {\n  --fa: \"\\e18f\";\n}\n.fa-volume-off {\n  --fa: \"\\f026\";\n}\n.fa-american-sign-language-interpreting,.fa-asl-interpreting,.fa-hands-american-sign-language-interpreting,.fa-hands-asl-interpreting {\n  --fa: \"\\f2a3\";\n}\n.fa-cog,.fa-gear {\n  --fa: \"\\f013\";\n}\n.fa-droplet-slash,.fa-tint-slash {\n  --fa: \"\\f5c7\";\n}\n.fa-mosque {\n  --fa: \"\\f678\";\n}\n.fa-mosquito {\n  --fa: \"\\e52b\";\n}\n.fa-star-of-david {\n  --fa: \"\\f69a\";\n}\n.fa-person-military-rifle {\n  --fa: \"\\e54b\";\n}\n.fa-cart-shopping,.fa-shopping-cart {\n  --fa: \"\\f07a\";\n}\n.fa-vials {\n  --fa: \"\\f493\";\n}\n.fa-plug-circle-plus {\n  --fa: \"\\e55f\";\n}\n.fa-place-of-worship {\n  --fa: \"\\f67f\";\n}\n.fa-grip-vertical {\n  --fa: \"\\f58e\";\n}\n.fa-hexagon-nodes {\n  --fa: \"\\e699\";\n}\n.fa-arrow-turn-up,.fa-level-up {\n  --fa: \"\\f148\";\n}\n.fa-u {\n  --fa: \"\\55\";\n}\n.fa-square-root-alt,.fa-square-root-variable {\n  --fa: \"\\f698\";\n}\n.fa-clock,.fa-clock-four {\n  --fa: \"\\f017\";\n}\n.fa-backward-step,.fa-step-backward {\n  --fa: \"\\f048\";\n}\n.fa-pallet {\n  --fa: \"\\f482\";\n}\n.fa-faucet {\n  --fa: \"\\e005\";\n}\n.fa-baseball-bat-ball {\n  --fa: \"\\f432\";\n}\n.fa-s {\n  --fa: \"\\53\";\n}\n.fa-timeline {\n  --fa: \"\\e29c\";\n}\n.fa-keyboard {\n  --fa: \"\\f11c\";\n}\n.fa-caret-down {\n  --fa: \"\\f0d7\";\n}\n.fa-clinic-medical,.fa-house-chimney-medical {\n  --fa: \"\\f7f2\";\n}\n.fa-temperature-3,.fa-temperature-three-quarters,.fa-thermometer-3,.fa-thermometer-three-quarters {\n  --fa: \"\\f2c8\";\n}\n.fa-mobile-android-alt,.fa-mobile-screen {\n  --fa: \"\\f3cf\";\n}\n.fa-plane-up {\n  --fa: \"\\e22d\";\n}\n.fa-piggy-bank {\n  --fa: \"\\f4d3\";\n}\n.fa-battery-3,.fa-battery-half {\n  --fa: \"\\f242\";\n}\n.fa-mountain-city {\n  --fa: \"\\e52e\";\n}\n.fa-coins {\n  --fa: \"\\f51e\";\n}\n.fa-khanda {\n  --fa: \"\\f66d\";\n}\n.fa-sliders,.fa-sliders-h {\n  --fa: \"\\f1de\";\n}\n.fa-folder-tree {\n  --fa: \"\\f802\";\n}\n.fa-network-wired {\n  --fa: \"\\f6ff\";\n}\n.fa-map-pin {\n  --fa: \"\\f276\";\n}\n.fa-hamsa {\n  --fa: \"\\f665\";\n}\n.fa-cent-sign {\n  --fa: \"\\e3f5\";\n}\n.fa-flask {\n  --fa: \"\\f0c3\";\n}\n.fa-person-pregnant {\n  --fa: \"\\e31e\";\n}\n.fa-wand-sparkles {\n  --fa: \"\\f72b\";\n}\n.fa-ellipsis-v,.fa-ellipsis-vertical {\n  --fa: \"\\f142\";\n}\n.fa-ticket {\n  --fa: \"\\f145\";\n}\n.fa-power-off {\n  --fa: \"\\f011\";\n}\n.fa-long-arrow-alt-right,.fa-right-long {\n  --fa: \"\\f30b\";\n}\n.fa-flag-usa {\n  --fa: \"\\f74d\";\n}\n.fa-laptop-file {\n  --fa: \"\\e51d\";\n}\n.fa-teletype,.fa-tty {\n  --fa: \"\\f1e4\";\n}\n.fa-diagram-next {\n  --fa: \"\\e476\";\n}\n.fa-person-rifle {\n  --fa: \"\\e54e\";\n}\n.fa-house-medical-circle-exclamation {\n  --fa: \"\\e512\";\n}\n.fa-closed-captioning {\n  --fa: \"\\f20a\";\n}\n.fa-hiking,.fa-person-hiking {\n  --fa: \"\\f6ec\";\n}\n.fa-venus-double {\n  --fa: \"\\f226\";\n}\n.fa-images {\n  --fa: \"\\f302\";\n}\n.fa-calculator {\n  --fa: \"\\f1ec\";\n}\n.fa-people-pulling {\n  --fa: \"\\e535\";\n}\n.fa-n {\n  --fa: \"\\4e\";\n}\n.fa-cable-car,.fa-tram {\n  --fa: \"\\f7da\";\n}\n.fa-cloud-rain {\n  --fa: \"\\f73d\";\n}\n.fa-building-circle-xmark {\n  --fa: \"\\e4d4\";\n}\n.fa-ship {\n  --fa: \"\\f21a\";\n}\n.fa-arrows-down-to-line {\n  --fa: \"\\e4b8\";\n}\n.fa-download {\n  --fa: \"\\f019\";\n}\n.fa-face-grin,.fa-grin {\n  --fa: \"\\f580\";\n}\n.fa-backspace,.fa-delete-left {\n  --fa: \"\\f55a\";\n}\n.fa-eye-dropper,.fa-eye-dropper-empty,.fa-eyedropper {\n  --fa: \"\\f1fb\";\n}\n.fa-file-circle-check {\n  --fa: \"\\e5a0\";\n}\n.fa-forward {\n  --fa: \"\\f04e\";\n}\n.fa-mobile,.fa-mobile-android,.fa-mobile-phone {\n  --fa: \"\\f3ce\";\n}\n.fa-face-meh,.fa-meh {\n  --fa: \"\\f11a\";\n}\n.fa-align-center {\n  --fa: \"\\f037\";\n}\n.fa-book-dead,.fa-book-skull {\n  --fa: \"\\f6b7\";\n}\n.fa-drivers-license,.fa-id-card {\n  --fa: \"\\f2c2\";\n}\n.fa-dedent,.fa-outdent {\n  --fa: \"\\f03b\";\n}\n.fa-heart-circle-exclamation {\n  --fa: \"\\e4fe\";\n}\n.fa-home,.fa-home-alt,.fa-home-lg-alt,.fa-house {\n  --fa: \"\\f015\";\n}\n.fa-calendar-week {\n  --fa: \"\\f784\";\n}\n.fa-laptop-medical {\n  --fa: \"\\f812\";\n}\n.fa-b {\n  --fa: \"\\42\";\n}\n.fa-file-medical {\n  --fa: \"\\f477\";\n}\n.fa-dice-one {\n  --fa: \"\\f525\";\n}\n.fa-kiwi-bird {\n  --fa: \"\\f535\";\n}\n.fa-arrow-right-arrow-left,.fa-exchange {\n  --fa: \"\\f0ec\";\n}\n.fa-redo-alt,.fa-rotate-forward,.fa-rotate-right {\n  --fa: \"\\f2f9\";\n}\n.fa-cutlery,.fa-utensils {\n  --fa: \"\\f2e7\";\n}\n.fa-arrow-up-wide-short,.fa-sort-amount-up {\n  --fa: \"\\f161\";\n}\n.fa-mill-sign {\n  --fa: \"\\e1ed\";\n}\n.fa-bowl-rice {\n  --fa: \"\\e2eb\";\n}\n.fa-skull {\n  --fa: \"\\f54c\";\n}\n.fa-broadcast-tower,.fa-tower-broadcast {\n  --fa: \"\\f519\";\n}\n.fa-truck-pickup {\n  --fa: \"\\f63c\";\n}\n.fa-long-arrow-alt-up,.fa-up-long {\n  --fa: \"\\f30c\";\n}\n.fa-stop {\n  --fa: \"\\f04d\";\n}\n.fa-code-merge {\n  --fa: \"\\f387\";\n}\n.fa-upload {\n  --fa: \"\\f093\";\n}\n.fa-hurricane {\n  --fa: \"\\f751\";\n}\n.fa-mound {\n  --fa: \"\\e52d\";\n}\n.fa-toilet-portable {\n  --fa: \"\\e583\";\n}\n.fa-compact-disc {\n  --fa: \"\\f51f\";\n}\n.fa-file-arrow-down,.fa-file-download {\n  --fa: \"\\f56d\";\n}\n.fa-caravan {\n  --fa: \"\\f8ff\";\n}\n.fa-shield-cat {\n  --fa: \"\\e572\";\n}\n.fa-bolt,.fa-zap {\n  --fa: \"\\f0e7\";\n}\n.fa-glass-water {\n  --fa: \"\\e4f4\";\n}\n.fa-oil-well {\n  --fa: \"\\e532\";\n}\n.fa-vault {\n  --fa: \"\\e2c5\";\n}\n.fa-mars {\n  --fa: \"\\f222\";\n}\n.fa-toilet {\n  --fa: \"\\f7d8\";\n}\n.fa-plane-circle-xmark {\n  --fa: \"\\e557\";\n}\n.fa-cny,.fa-jpy,.fa-rmb,.fa-yen,.fa-yen-sign {\n  --fa: \"\\f157\";\n}\n.fa-rouble,.fa-rub,.fa-ruble,.fa-ruble-sign {\n  --fa: \"\\f158\";\n}\n.fa-sun {\n  --fa: \"\\f185\";\n}\n.fa-guitar {\n  --fa: \"\\f7a6\";\n}\n.fa-face-laugh-wink,.fa-laugh-wink {\n  --fa: \"\\f59c\";\n}\n.fa-horse-head {\n  --fa: \"\\f7ab\";\n}\n.fa-bore-hole {\n  --fa: \"\\e4c3\";\n}\n.fa-industry {\n  --fa: \"\\f275\";\n}\n.fa-arrow-alt-circle-down,.fa-circle-down {\n  --fa: \"\\f358\";\n}\n.fa-arrows-turn-to-dots {\n  --fa: \"\\e4c1\";\n}\n.fa-florin-sign {\n  --fa: \"\\e184\";\n}\n.fa-arrow-down-short-wide,.fa-sort-amount-desc,.fa-sort-amount-down-alt {\n  --fa: \"\\f884\";\n}\n.fa-less-than {\n  --fa: \"\\3c\";\n}\n.fa-angle-down {\n  --fa: \"\\f107\";\n}\n.fa-car-tunnel {\n  --fa: \"\\e4de\";\n}\n.fa-head-side-cough {\n  --fa: \"\\e061\";\n}\n.fa-grip-lines {\n  --fa: \"\\f7a4\";\n}\n.fa-thumbs-down {\n  --fa: \"\\f165\";\n}\n.fa-user-lock {\n  --fa: \"\\f502\";\n}\n.fa-arrow-right-long,.fa-long-arrow-right {\n  --fa: \"\\f178\";\n}\n.fa-anchor-circle-xmark {\n  --fa: \"\\e4ac\";\n}\n.fa-ellipsis,.fa-ellipsis-h {\n  --fa: \"\\f141\";\n}\n.fa-chess-pawn {\n  --fa: \"\\f443\";\n}\n.fa-first-aid,.fa-kit-medical {\n  --fa: \"\\f479\";\n}\n.fa-person-through-window {\n  --fa: \"\\e5a9\";\n}\n.fa-toolbox {\n  --fa: \"\\f552\";\n}\n.fa-hands-holding-circle {\n  --fa: \"\\e4fb\";\n}\n.fa-bug {\n  --fa: \"\\f188\";\n}\n.fa-credit-card,.fa-credit-card-alt {\n  --fa: \"\\f09d\";\n}\n.fa-automobile,.fa-car {\n  --fa: \"\\f1b9\";\n}\n.fa-hand-holding-hand {\n  --fa: \"\\e4f7\";\n}\n.fa-book-open-reader,.fa-book-reader {\n  --fa: \"\\f5da\";\n}\n.fa-mountain-sun {\n  --fa: \"\\e52f\";\n}\n.fa-arrows-left-right-to-line {\n  --fa: \"\\e4ba\";\n}\n.fa-dice-d20 {\n  --fa: \"\\f6cf\";\n}\n.fa-truck-droplet {\n  --fa: \"\\e58c\";\n}\n.fa-file-circle-xmark {\n  --fa: \"\\e5a1\";\n}\n.fa-temperature-arrow-up,.fa-temperature-up {\n  --fa: \"\\e040\";\n}\n.fa-medal {\n  --fa: \"\\f5a2\";\n}\n.fa-bed {\n  --fa: \"\\f236\";\n}\n.fa-h-square,.fa-square-h {\n  --fa: \"\\f0fd\";\n}\n.fa-podcast {\n  --fa: \"\\f2ce\";\n}\n.fa-temperature-4,.fa-temperature-full,.fa-thermometer-4,.fa-thermometer-full {\n  --fa: \"\\f2c7\";\n}\n.fa-bell {\n  --fa: \"\\f0f3\";\n}\n.fa-superscript {\n  --fa: \"\\f12b\";\n}\n.fa-plug-circle-xmark {\n  --fa: \"\\e560\";\n}\n.fa-star-of-life {\n  --fa: \"\\f621\";\n}\n.fa-phone-slash {\n  --fa: \"\\f3dd\";\n}\n.fa-paint-roller {\n  --fa: \"\\f5aa\";\n}\n.fa-hands-helping,.fa-handshake-angle {\n  --fa: \"\\f4c4\";\n}\n.fa-location-dot,.fa-map-marker-alt {\n  --fa: \"\\f3c5\";\n}\n.fa-file {\n  --fa: \"\\f15b\";\n}\n.fa-greater-than {\n  --fa: \"\\3e\";\n}\n.fa-person-swimming,.fa-swimmer {\n  --fa: \"\\f5c4\";\n}\n.fa-arrow-down {\n  --fa: \"\\f063\";\n}\n.fa-droplet,.fa-tint {\n  --fa: \"\\f043\";\n}\n.fa-eraser {\n  --fa: \"\\f12d\";\n}\n.fa-earth,.fa-earth-america,.fa-earth-americas,.fa-globe-americas {\n  --fa: \"\\f57d\";\n}\n.fa-person-burst {\n  --fa: \"\\e53b\";\n}\n.fa-dove {\n  --fa: \"\\f4ba\";\n}\n.fa-battery-0,.fa-battery-empty {\n  --fa: \"\\f244\";\n}\n.fa-socks {\n  --fa: \"\\f696\";\n}\n.fa-inbox {\n  --fa: \"\\f01c\";\n}\n.fa-section {\n  --fa: \"\\e447\";\n}\n.fa-gauge-high,.fa-tachometer-alt,.fa-tachometer-alt-fast {\n  --fa: \"\\f625\";\n}\n.fa-envelope-open-text {\n  --fa: \"\\f658\";\n}\n.fa-hospital,.fa-hospital-alt,.fa-hospital-wide {\n  --fa: \"\\f0f8\";\n}\n.fa-wine-bottle {\n  --fa: \"\\f72f\";\n}\n.fa-chess-rook {\n  --fa: \"\\f447\";\n}\n.fa-bars-staggered,.fa-reorder,.fa-stream {\n  --fa: \"\\f550\";\n}\n.fa-dharmachakra {\n  --fa: \"\\f655\";\n}\n.fa-hotdog {\n  --fa: \"\\f80f\";\n}\n.fa-blind,.fa-person-walking-with-cane {\n  --fa: \"\\f29d\";\n}\n.fa-drum {\n  --fa: \"\\f569\";\n}\n.fa-ice-cream {\n  --fa: \"\\f810\";\n}\n.fa-heart-circle-bolt {\n  --fa: \"\\e4fc\";\n}\n.fa-fax {\n  --fa: \"\\f1ac\";\n}\n.fa-paragraph {\n  --fa: \"\\f1dd\";\n}\n.fa-check-to-slot,.fa-vote-yea {\n  --fa: \"\\f772\";\n}\n.fa-star-half {\n  --fa: \"\\f089\";\n}\n.fa-boxes,.fa-boxes-alt,.fa-boxes-stacked {\n  --fa: \"\\f468\";\n}\n.fa-chain,.fa-link {\n  --fa: \"\\f0c1\";\n}\n.fa-assistive-listening-systems,.fa-ear-listen {\n  --fa: \"\\f2a2\";\n}\n.fa-tree-city {\n  --fa: \"\\e587\";\n}\n.fa-play {\n  --fa: \"\\f04b\";\n}\n.fa-font {\n  --fa: \"\\f031\";\n}\n.fa-table-cells-row-lock {\n  --fa: \"\\e67a\";\n}\n.fa-rupiah-sign {\n  --fa: \"\\e23d\";\n}\n.fa-magnifying-glass,.fa-search {\n  --fa: \"\\f002\";\n}\n.fa-ping-pong-paddle-ball,.fa-table-tennis,.fa-table-tennis-paddle-ball {\n  --fa: \"\\f45d\";\n}\n.fa-diagnoses,.fa-person-dots-from-line {\n  --fa: \"\\f470\";\n}\n.fa-trash-can-arrow-up,.fa-trash-restore-alt {\n  --fa: \"\\f82a\";\n}\n.fa-naira-sign {\n  --fa: \"\\e1f6\";\n}\n.fa-cart-arrow-down {\n  --fa: \"\\f218\";\n}\n.fa-walkie-talkie {\n  --fa: \"\\f8ef\";\n}\n.fa-file-edit,.fa-file-pen {\n  --fa: \"\\f31c\";\n}\n.fa-receipt {\n  --fa: \"\\f543\";\n}\n.fa-pen-square,.fa-pencil-square,.fa-square-pen {\n  --fa: \"\\f14b\";\n}\n.fa-suitcase-rolling {\n  --fa: \"\\f5c1\";\n}\n.fa-person-circle-exclamation {\n  --fa: \"\\e53f\";\n}\n.fa-chevron-down {\n  --fa: \"\\f078\";\n}\n.fa-battery,.fa-battery-5,.fa-battery-full {\n  --fa: \"\\f240\";\n}\n.fa-skull-crossbones {\n  --fa: \"\\f714\";\n}\n.fa-code-compare {\n  --fa: \"\\e13a\";\n}\n.fa-list-dots,.fa-list-ul {\n  --fa: \"\\f0ca\";\n}\n.fa-school-lock {\n  --fa: \"\\e56f\";\n}\n.fa-tower-cell {\n  --fa: \"\\e585\";\n}\n.fa-down-long,.fa-long-arrow-alt-down {\n  --fa: \"\\f309\";\n}\n.fa-ranking-star {\n  --fa: \"\\e561\";\n}\n.fa-chess-king {\n  --fa: \"\\f43f\";\n}\n.fa-person-harassing {\n  --fa: \"\\e549\";\n}\n.fa-brazilian-real-sign {\n  --fa: \"\\e46c\";\n}\n.fa-landmark-alt,.fa-landmark-dome {\n  --fa: \"\\f752\";\n}\n.fa-arrow-up {\n  --fa: \"\\f062\";\n}\n.fa-television,.fa-tv,.fa-tv-alt {\n  --fa: \"\\f26c\";\n}\n.fa-shrimp {\n  --fa: \"\\e448\";\n}\n.fa-list-check,.fa-tasks {\n  --fa: \"\\f0ae\";\n}\n.fa-jug-detergent {\n  --fa: \"\\e519\";\n}\n.fa-circle-user,.fa-user-circle {\n  --fa: \"\\f2bd\";\n}\n.fa-user-shield {\n  --fa: \"\\f505\";\n}\n.fa-wind {\n  --fa: \"\\f72e\";\n}\n.fa-car-burst,.fa-car-crash {\n  --fa: \"\\f5e1\";\n}\n.fa-y {\n  --fa: \"\\59\";\n}\n.fa-person-snowboarding,.fa-snowboarding {\n  --fa: \"\\f7ce\";\n}\n.fa-shipping-fast,.fa-truck-fast {\n  --fa: \"\\f48b\";\n}\n.fa-fish {\n  --fa: \"\\f578\";\n}\n.fa-user-graduate {\n  --fa: \"\\f501\";\n}\n.fa-adjust,.fa-circle-half-stroke {\n  --fa: \"\\f042\";\n}\n.fa-clapperboard {\n  --fa: \"\\e131\";\n}\n.fa-circle-radiation,.fa-radiation-alt {\n  --fa: \"\\f7ba\";\n}\n.fa-baseball,.fa-baseball-ball {\n  --fa: \"\\f433\";\n}\n.fa-jet-fighter-up {\n  --fa: \"\\e518\";\n}\n.fa-diagram-project,.fa-project-diagram {\n  --fa: \"\\f542\";\n}\n.fa-copy {\n  --fa: \"\\f0c5\";\n}\n.fa-volume-mute,.fa-volume-times,.fa-volume-xmark {\n  --fa: \"\\f6a9\";\n}\n.fa-hand-sparkles {\n  --fa: \"\\e05d\";\n}\n.fa-grip,.fa-grip-horizontal {\n  --fa: \"\\f58d\";\n}\n.fa-share-from-square,.fa-share-square {\n  --fa: \"\\f14d\";\n}\n.fa-child-combatant,.fa-child-rifle {\n  --fa: \"\\e4e0\";\n}\n.fa-gun {\n  --fa: \"\\e19b\";\n}\n.fa-phone-square,.fa-square-phone {\n  --fa: \"\\f098\";\n}\n.fa-add,.fa-plus {\n  --fa: \"\\2b\";\n}\n.fa-expand {\n  --fa: \"\\f065\";\n}\n.fa-computer {\n  --fa: \"\\e4e5\";\n}\n.fa-close,.fa-multiply,.fa-remove,.fa-times,.fa-xmark {\n  --fa: \"\\f00d\";\n}\n.fa-arrows,.fa-arrows-up-down-left-right {\n  --fa: \"\\f047\";\n}\n.fa-chalkboard-teacher,.fa-chalkboard-user {\n  --fa: \"\\f51c\";\n}\n.fa-peso-sign {\n  --fa: \"\\e222\";\n}\n.fa-building-shield {\n  --fa: \"\\e4d8\";\n}\n.fa-baby {\n  --fa: \"\\f77c\";\n}\n.fa-users-line {\n  --fa: \"\\e592\";\n}\n.fa-quote-left,.fa-quote-left-alt {\n  --fa: \"\\f10d\";\n}\n.fa-tractor {\n  --fa: \"\\f722\";\n}\n.fa-trash-arrow-up,.fa-trash-restore {\n  --fa: \"\\f829\";\n}\n.fa-arrow-down-up-lock {\n  --fa: \"\\e4b0\";\n}\n.fa-lines-leaning {\n  --fa: \"\\e51e\";\n}\n.fa-ruler-combined {\n  --fa: \"\\f546\";\n}\n.fa-copyright {\n  --fa: \"\\f1f9\";\n}\n.fa-equals {\n  --fa: \"\\3d\";\n}\n.fa-blender {\n  --fa: \"\\f517\";\n}\n.fa-teeth {\n  --fa: \"\\f62e\";\n}\n.fa-ils,.fa-shekel,.fa-shekel-sign,.fa-sheqel,.fa-sheqel-sign {\n  --fa: \"\\f20b\";\n}\n.fa-map {\n  --fa: \"\\f279\";\n}\n.fa-rocket {\n  --fa: \"\\f135\";\n}\n.fa-photo-film,.fa-photo-video {\n  --fa: \"\\f87c\";\n}\n.fa-folder-minus {\n  --fa: \"\\f65d\";\n}\n.fa-hexagon-nodes-bolt {\n  --fa: \"\\e69a\";\n}\n.fa-store {\n  --fa: \"\\f54e\";\n}\n.fa-arrow-trend-up {\n  --fa: \"\\e098\";\n}\n.fa-plug-circle-minus {\n  --fa: \"\\e55e\";\n}\n.fa-sign,.fa-sign-hanging {\n  --fa: \"\\f4d9\";\n}\n.fa-bezier-curve {\n  --fa: \"\\f55b\";\n}\n.fa-bell-slash {\n  --fa: \"\\f1f6\";\n}\n.fa-tablet,.fa-tablet-android {\n  --fa: \"\\f3fb\";\n}\n.fa-school-flag {\n  --fa: \"\\e56e\";\n}\n.fa-fill {\n  --fa: \"\\f575\";\n}\n.fa-angle-up {\n  --fa: \"\\f106\";\n}\n.fa-drumstick-bite {\n  --fa: \"\\f6d7\";\n}\n.fa-holly-berry {\n  --fa: \"\\f7aa\";\n}\n.fa-chevron-left {\n  --fa: \"\\f053\";\n}\n.fa-bacteria {\n  --fa: \"\\e059\";\n}\n.fa-hand-lizard {\n  --fa: \"\\f258\";\n}\n.fa-notdef {\n  --fa: \"\\e1fe\";\n}\n.fa-disease {\n  --fa: \"\\f7fa\";\n}\n.fa-briefcase-medical {\n  --fa: \"\\f469\";\n}\n.fa-genderless {\n  --fa: \"\\f22d\";\n}\n.fa-chevron-right {\n  --fa: \"\\f054\";\n}\n.fa-retweet {\n  --fa: \"\\f079\";\n}\n.fa-car-alt,.fa-car-rear {\n  --fa: \"\\f5de\";\n}\n.fa-pump-soap {\n  --fa: \"\\e06b\";\n}\n.fa-video-slash {\n  --fa: \"\\f4e2\";\n}\n.fa-battery-2,.fa-battery-quarter {\n  --fa: \"\\f243\";\n}\n.fa-radio {\n  --fa: \"\\f8d7\";\n}\n.fa-baby-carriage,.fa-carriage-baby {\n  --fa: \"\\f77d\";\n}\n.fa-traffic-light {\n  --fa: \"\\f637\";\n}\n.fa-thermometer {\n  --fa: \"\\f491\";\n}\n.fa-vr-cardboard {\n  --fa: \"\\f729\";\n}\n.fa-hand-middle-finger {\n  --fa: \"\\f806\";\n}\n.fa-percent,.fa-percentage {\n  --fa: \"\\25\";\n}\n.fa-truck-moving {\n  --fa: \"\\f4df\";\n}\n.fa-glass-water-droplet {\n  --fa: \"\\e4f5\";\n}\n.fa-display {\n  --fa: \"\\e163\";\n}\n.fa-face-smile,.fa-smile {\n  --fa: \"\\f118\";\n}\n.fa-thumb-tack,.fa-thumbtack {\n  --fa: \"\\f08d\";\n}\n.fa-trophy {\n  --fa: \"\\f091\";\n}\n.fa-person-praying,.fa-pray {\n  --fa: \"\\f683\";\n}\n.fa-hammer {\n  --fa: \"\\f6e3\";\n}\n.fa-hand-peace {\n  --fa: \"\\f25b\";\n}\n.fa-rotate,.fa-sync-alt {\n  --fa: \"\\f2f1\";\n}\n.fa-spinner {\n  --fa: \"\\f110\";\n}\n.fa-robot {\n  --fa: \"\\f544\";\n}\n.fa-peace {\n  --fa: \"\\f67c\";\n}\n.fa-cogs,.fa-gears {\n  --fa: \"\\f085\";\n}\n.fa-warehouse {\n  --fa: \"\\f494\";\n}\n.fa-arrow-up-right-dots {\n  --fa: \"\\e4b7\";\n}\n.fa-splotch {\n  --fa: \"\\f5bc\";\n}\n.fa-face-grin-hearts,.fa-grin-hearts {\n  --fa: \"\\f584\";\n}\n.fa-dice-four {\n  --fa: \"\\f524\";\n}\n.fa-sim-card {\n  --fa: \"\\f7c4\";\n}\n.fa-transgender,.fa-transgender-alt {\n  --fa: \"\\f225\";\n}\n.fa-mercury {\n  --fa: \"\\f223\";\n}\n.fa-arrow-turn-down,.fa-level-down {\n  --fa: \"\\f149\";\n}\n.fa-person-falling-burst {\n  --fa: \"\\e547\";\n}\n.fa-award {\n  --fa: \"\\f559\";\n}\n.fa-ticket-alt,.fa-ticket-simple {\n  --fa: \"\\f3ff\";\n}\n.fa-building {\n  --fa: \"\\f1ad\";\n}\n.fa-angle-double-left,.fa-angles-left {\n  --fa: \"\\f100\";\n}\n.fa-qrcode {\n  --fa: \"\\f029\";\n}\n.fa-clock-rotate-left,.fa-history {\n  --fa: \"\\f1da\";\n}\n.fa-face-grin-beam-sweat,.fa-grin-beam-sweat {\n  --fa: \"\\f583\";\n}\n.fa-arrow-right-from-file,.fa-file-export {\n  --fa: \"\\f56e\";\n}\n.fa-shield,.fa-shield-blank {\n  --fa: \"\\f132\";\n}\n.fa-arrow-up-short-wide,.fa-sort-amount-up-alt {\n  --fa: \"\\f885\";\n}\n.fa-comment-nodes {\n  --fa: \"\\e696\";\n}\n.fa-house-medical {\n  --fa: \"\\e3b2\";\n}\n.fa-golf-ball,.fa-golf-ball-tee {\n  --fa: \"\\f450\";\n}\n.fa-chevron-circle-left,.fa-circle-chevron-left {\n  --fa: \"\\f137\";\n}\n.fa-house-chimney-window {\n  --fa: \"\\e00d\";\n}\n.fa-pen-nib {\n  --fa: \"\\f5ad\";\n}\n.fa-tent-arrow-turn-left {\n  --fa: \"\\e580\";\n}\n.fa-tents {\n  --fa: \"\\e582\";\n}\n.fa-magic,.fa-wand-magic {\n  --fa: \"\\f0d0\";\n}\n.fa-dog {\n  --fa: \"\\f6d3\";\n}\n.fa-carrot {\n  --fa: \"\\f787\";\n}\n.fa-moon {\n  --fa: \"\\f186\";\n}\n.fa-wine-glass-alt,.fa-wine-glass-empty {\n  --fa: \"\\f5ce\";\n}\n.fa-cheese {\n  --fa: \"\\f7ef\";\n}\n.fa-yin-yang {\n  --fa: \"\\f6ad\";\n}\n.fa-music {\n  --fa: \"\\f001\";\n}\n.fa-code-commit {\n  --fa: \"\\f386\";\n}\n.fa-temperature-low {\n  --fa: \"\\f76b\";\n}\n.fa-biking,.fa-person-biking {\n  --fa: \"\\f84a\";\n}\n.fa-broom {\n  --fa: \"\\f51a\";\n}\n.fa-shield-heart {\n  --fa: \"\\e574\";\n}\n.fa-gopuram {\n  --fa: \"\\f664\";\n}\n.fa-earth-oceania,.fa-globe-oceania {\n  --fa: \"\\e47b\";\n}\n.fa-square-xmark,.fa-times-square,.fa-xmark-square {\n  --fa: \"\\f2d3\";\n}\n.fa-hashtag {\n  --fa: \"\\23\";\n}\n.fa-expand-alt,.fa-up-right-and-down-left-from-center {\n  --fa: \"\\f424\";\n}\n.fa-oil-can {\n  --fa: \"\\f613\";\n}\n.fa-t {\n  --fa: \"\\54\";\n}\n.fa-hippo {\n  --fa: \"\\f6ed\";\n}\n.fa-chart-column {\n  --fa: \"\\e0e3\";\n}\n.fa-infinity {\n  --fa: \"\\f534\";\n}\n.fa-vial-circle-check {\n  --fa: \"\\e596\";\n}\n.fa-person-arrow-down-to-line {\n  --fa: \"\\e538\";\n}\n.fa-voicemail {\n  --fa: \"\\f897\";\n}\n.fa-fan {\n  --fa: \"\\f863\";\n}\n.fa-person-walking-luggage {\n  --fa: \"\\e554\";\n}\n.fa-arrows-alt-v,.fa-up-down {\n  --fa: \"\\f338\";\n}\n.fa-cloud-moon-rain {\n  --fa: \"\\f73c\";\n}\n.fa-calendar {\n  --fa: \"\\f133\";\n}\n.fa-trailer {\n  --fa: \"\\e041\";\n}\n.fa-bahai,.fa-haykal {\n  --fa: \"\\f666\";\n}\n.fa-sd-card {\n  --fa: \"\\f7c2\";\n}\n.fa-dragon {\n  --fa: \"\\f6d5\";\n}\n.fa-shoe-prints {\n  --fa: \"\\f54b\";\n}\n.fa-circle-plus,.fa-plus-circle {\n  --fa: \"\\f055\";\n}\n.fa-face-grin-tongue-wink,.fa-grin-tongue-wink {\n  --fa: \"\\f58b\";\n}\n.fa-hand-holding {\n  --fa: \"\\f4bd\";\n}\n.fa-plug-circle-exclamation {\n  --fa: \"\\e55d\";\n}\n.fa-chain-broken,.fa-chain-slash,.fa-link-slash,.fa-unlink {\n  --fa: \"\\f127\";\n}\n.fa-clone {\n  --fa: \"\\f24d\";\n}\n.fa-person-walking-arrow-loop-left {\n  --fa: \"\\e551\";\n}\n.fa-arrow-up-z-a,.fa-sort-alpha-up-alt {\n  --fa: \"\\f882\";\n}\n.fa-fire-alt,.fa-fire-flame-curved {\n  --fa: \"\\f7e4\";\n}\n.fa-tornado {\n  --fa: \"\\f76f\";\n}\n.fa-file-circle-plus {\n  --fa: \"\\e494\";\n}\n.fa-book-quran,.fa-quran {\n  --fa: \"\\f687\";\n}\n.fa-anchor {\n  --fa: \"\\f13d\";\n}\n.fa-border-all {\n  --fa: \"\\f84c\";\n}\n.fa-angry,.fa-face-angry {\n  --fa: \"\\f556\";\n}\n.fa-cookie-bite {\n  --fa: \"\\f564\";\n}\n.fa-arrow-trend-down {\n  --fa: \"\\e097\";\n}\n.fa-feed,.fa-rss {\n  --fa: \"\\f09e\";\n}\n.fa-draw-polygon {\n  --fa: \"\\f5ee\";\n}\n.fa-balance-scale,.fa-scale-balanced {\n  --fa: \"\\f24e\";\n}\n.fa-gauge-simple-high,.fa-tachometer,.fa-tachometer-fast {\n  --fa: \"\\f62a\";\n}\n.fa-shower {\n  --fa: \"\\f2cc\";\n}\n.fa-desktop,.fa-desktop-alt {\n  --fa: \"\\f390\";\n}\n.fa-m {\n  --fa: \"\\4d\";\n}\n.fa-table-list,.fa-th-list {\n  --fa: \"\\f00b\";\n}\n.fa-comment-sms,.fa-sms {\n  --fa: \"\\f7cd\";\n}\n.fa-book {\n  --fa: \"\\f02d\";\n}\n.fa-user-plus {\n  --fa: \"\\f234\";\n}\n.fa-check {\n  --fa: \"\\f00c\";\n}\n.fa-battery-4,.fa-battery-three-quarters {\n  --fa: \"\\f241\";\n}\n.fa-house-circle-check {\n  --fa: \"\\e509\";\n}\n.fa-angle-left {\n  --fa: \"\\f104\";\n}\n.fa-diagram-successor {\n  --fa: \"\\e47a\";\n}\n.fa-truck-arrow-right {\n  --fa: \"\\e58b\";\n}\n.fa-arrows-split-up-and-left {\n  --fa: \"\\e4bc\";\n}\n.fa-fist-raised,.fa-hand-fist {\n  --fa: \"\\f6de\";\n}\n.fa-cloud-moon {\n  --fa: \"\\f6c3\";\n}\n.fa-briefcase {\n  --fa: \"\\f0b1\";\n}\n.fa-person-falling {\n  --fa: \"\\e546\";\n}\n.fa-image-portrait,.fa-portrait {\n  --fa: \"\\f3e0\";\n}\n.fa-user-tag {\n  --fa: \"\\f507\";\n}\n.fa-rug {\n  --fa: \"\\e569\";\n}\n.fa-earth-europe,.fa-globe-europe {\n  --fa: \"\\f7a2\";\n}\n.fa-cart-flatbed-suitcase,.fa-luggage-cart {\n  --fa: \"\\f59d\";\n}\n.fa-rectangle-times,.fa-rectangle-xmark,.fa-times-rectangle,.fa-window-close {\n  --fa: \"\\f410\";\n}\n.fa-baht-sign {\n  --fa: \"\\e0ac\";\n}\n.fa-book-open {\n  --fa: \"\\f518\";\n}\n.fa-book-journal-whills,.fa-journal-whills {\n  --fa: \"\\f66a\";\n}\n.fa-handcuffs {\n  --fa: \"\\e4f8\";\n}\n.fa-exclamation-triangle,.fa-triangle-exclamation,.fa-warning {\n  --fa: \"\\f071\";\n}\n.fa-database {\n  --fa: \"\\f1c0\";\n}\n.fa-mail-forward,.fa-share {\n  --fa: \"\\f064\";\n}\n.fa-bottle-droplet {\n  --fa: \"\\e4c4\";\n}\n.fa-mask-face {\n  --fa: \"\\e1d7\";\n}\n.fa-hill-rockslide {\n  --fa: \"\\e508\";\n}\n.fa-exchange-alt,.fa-right-left {\n  --fa: \"\\f362\";\n}\n.fa-paper-plane {\n  --fa: \"\\f1d8\";\n}\n.fa-road-circle-exclamation {\n  --fa: \"\\e565\";\n}\n.fa-dungeon {\n  --fa: \"\\f6d9\";\n}\n.fa-align-right {\n  --fa: \"\\f038\";\n}\n.fa-money-bill-1-wave,.fa-money-bill-wave-alt {\n  --fa: \"\\f53b\";\n}\n.fa-life-ring {\n  --fa: \"\\f1cd\";\n}\n.fa-hands,.fa-sign-language,.fa-signing {\n  --fa: \"\\f2a7\";\n}\n.fa-calendar-day {\n  --fa: \"\\f783\";\n}\n.fa-ladder-water,.fa-swimming-pool,.fa-water-ladder {\n  --fa: \"\\f5c5\";\n}\n.fa-arrows-up-down,.fa-arrows-v {\n  --fa: \"\\f07d\";\n}\n.fa-face-grimace,.fa-grimace {\n  --fa: \"\\f57f\";\n}\n.fa-wheelchair-alt,.fa-wheelchair-move {\n  --fa: \"\\e2ce\";\n}\n.fa-level-down-alt,.fa-turn-down {\n  --fa: \"\\f3be\";\n}\n.fa-person-walking-arrow-right {\n  --fa: \"\\e552\";\n}\n.fa-envelope-square,.fa-square-envelope {\n  --fa: \"\\f199\";\n}\n.fa-dice {\n  --fa: \"\\f522\";\n}\n.fa-bowling-ball {\n  --fa: \"\\f436\";\n}\n.fa-brain {\n  --fa: \"\\f5dc\";\n}\n.fa-band-aid,.fa-bandage {\n  --fa: \"\\f462\";\n}\n.fa-calendar-minus {\n  --fa: \"\\f272\";\n}\n.fa-circle-xmark,.fa-times-circle,.fa-xmark-circle {\n  --fa: \"\\f057\";\n}\n.fa-gifts {\n  --fa: \"\\f79c\";\n}\n.fa-hotel {\n  --fa: \"\\f594\";\n}\n.fa-earth-asia,.fa-globe-asia {\n  --fa: \"\\f57e\";\n}\n.fa-id-card-alt,.fa-id-card-clip {\n  --fa: \"\\f47f\";\n}\n.fa-magnifying-glass-plus,.fa-search-plus {\n  --fa: \"\\f00e\";\n}\n.fa-thumbs-up {\n  --fa: \"\\f164\";\n}\n.fa-user-clock {\n  --fa: \"\\f4fd\";\n}\n.fa-allergies,.fa-hand-dots {\n  --fa: \"\\f461\";\n}\n.fa-file-invoice {\n  --fa: \"\\f570\";\n}\n.fa-window-minimize {\n  --fa: \"\\f2d1\";\n}\n.fa-coffee,.fa-mug-saucer {\n  --fa: \"\\f0f4\";\n}\n.fa-brush {\n  --fa: \"\\f55d\";\n}\n.fa-file-half-dashed {\n  --fa: \"\\e698\";\n}\n.fa-mask {\n  --fa: \"\\f6fa\";\n}\n.fa-magnifying-glass-minus,.fa-search-minus {\n  --fa: \"\\f010\";\n}\n.fa-ruler-vertical {\n  --fa: \"\\f548\";\n}\n.fa-user-alt,.fa-user-large {\n  --fa: \"\\f406\";\n}\n.fa-train-tram {\n  --fa: \"\\e5b4\";\n}\n.fa-user-nurse {\n  --fa: \"\\f82f\";\n}\n.fa-syringe {\n  --fa: \"\\f48e\";\n}\n.fa-cloud-sun {\n  --fa: \"\\f6c4\";\n}\n.fa-stopwatch-20 {\n  --fa: \"\\e06f\";\n}\n.fa-square-full {\n  --fa: \"\\f45c\";\n}\n.fa-magnet {\n  --fa: \"\\f076\";\n}\n.fa-jar {\n  --fa: \"\\e516\";\n}\n.fa-note-sticky,.fa-sticky-note {\n  --fa: \"\\f249\";\n}\n.fa-bug-slash {\n  --fa: \"\\e490\";\n}\n.fa-arrow-up-from-water-pump {\n  --fa: \"\\e4b6\";\n}\n.fa-bone {\n  --fa: \"\\f5d7\";\n}\n.fa-table-cells-row-unlock {\n  --fa: \"\\e691\";\n}\n.fa-user-injured {\n  --fa: \"\\f728\";\n}\n.fa-face-sad-tear,.fa-sad-tear {\n  --fa: \"\\f5b4\";\n}\n.fa-plane {\n  --fa: \"\\f072\";\n}\n.fa-tent-arrows-down {\n  --fa: \"\\e581\";\n}\n.fa-exclamation {\n  --fa: \"\\21\";\n}\n.fa-arrows-spin {\n  --fa: \"\\e4bb\";\n}\n.fa-print {\n  --fa: \"\\f02f\";\n}\n.fa-try,.fa-turkish-lira,.fa-turkish-lira-sign {\n  --fa: \"\\e2bb\";\n}\n.fa-dollar,.fa-dollar-sign,.fa-usd {\n  --fa: \"\\24\";\n}\n.fa-x {\n  --fa: \"\\58\";\n}\n.fa-magnifying-glass-dollar,.fa-search-dollar {\n  --fa: \"\\f688\";\n}\n.fa-users-cog,.fa-users-gear {\n  --fa: \"\\f509\";\n}\n.fa-person-military-pointing {\n  --fa: \"\\e54a\";\n}\n.fa-bank,.fa-building-columns,.fa-institution,.fa-museum,.fa-university {\n  --fa: \"\\f19c\";\n}\n.fa-umbrella {\n  --fa: \"\\f0e9\";\n}\n.fa-trowel {\n  --fa: \"\\e589\";\n}\n.fa-d {\n  --fa: \"\\44\";\n}\n.fa-stapler {\n  --fa: \"\\e5af\";\n}\n.fa-masks-theater,.fa-theater-masks {\n  --fa: \"\\f630\";\n}\n.fa-kip-sign {\n  --fa: \"\\e1c4\";\n}\n.fa-hand-point-left {\n  --fa: \"\\f0a5\";\n}\n.fa-handshake-alt,.fa-handshake-simple {\n  --fa: \"\\f4c6\";\n}\n.fa-fighter-jet,.fa-jet-fighter {\n  --fa: \"\\f0fb\";\n}\n.fa-share-alt-square,.fa-square-share-nodes {\n  --fa: \"\\f1e1\";\n}\n.fa-barcode {\n  --fa: \"\\f02a\";\n}\n.fa-plus-minus {\n  --fa: \"\\e43c\";\n}\n.fa-video,.fa-video-camera {\n  --fa: \"\\f03d\";\n}\n.fa-graduation-cap,.fa-mortar-board {\n  --fa: \"\\f19d\";\n}\n.fa-hand-holding-medical {\n  --fa: \"\\e05c\";\n}\n.fa-person-circle-check {\n  --fa: \"\\e53e\";\n}\n.fa-level-up-alt,.fa-turn-up {\n  --fa: \"\\f3bf\";\n}\n.fa-sr-only,.fa-sr-only-focusable:not(:focus),.sr-only,.sr-only-focusable:not(:focus) {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0,0,0,0);\n  white-space: nowrap;\n  border-width: 0;\n}\n:host,:root {\n  --fa-style-family-brands: \"Font Awesome 6 Brands\";\n  --fa-font-brands: normal 400 1em/1 \"Font Awesome 6 Brands\";\n}\n@font-face {\n  font-family: \"Font Awesome 6 Brands\";\n  font-style: normal;\n  font-weight: 400;\n  font-display: block;\n  src: url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-brands-400.woff2) format(\"woff2\"),url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-brands-400.ttf) format(\"truetype\");\n}\n.fa-brands,.fab {\n  font-weight: 400;\n}\n.fa-monero {\n  --fa: \"\\f3d0\";\n}\n.fa-hooli {\n  --fa: \"\\f427\";\n}\n.fa-yelp {\n  --fa: \"\\f1e9\";\n}\n.fa-cc-visa {\n  --fa: \"\\f1f0\";\n}\n.fa-lastfm {\n  --fa: \"\\f202\";\n}\n.fa-shopware {\n  --fa: \"\\f5b5\";\n}\n.fa-creative-commons-nc {\n  --fa: \"\\f4e8\";\n}\n.fa-aws {\n  --fa: \"\\f375\";\n}\n.fa-redhat {\n  --fa: \"\\f7bc\";\n}\n.fa-yoast {\n  --fa: \"\\f2b1\";\n}\n.fa-cloudflare {\n  --fa: \"\\e07d\";\n}\n.fa-ups {\n  --fa: \"\\f7e0\";\n}\n.fa-pixiv {\n  --fa: \"\\e640\";\n}\n.fa-wpexplorer {\n  --fa: \"\\f2de\";\n}\n.fa-dyalog {\n  --fa: \"\\f399\";\n}\n.fa-bity {\n  --fa: \"\\f37a\";\n}\n.fa-stackpath {\n  --fa: \"\\f842\";\n}\n.fa-buysellads {\n  --fa: \"\\f20d\";\n}\n.fa-first-order {\n  --fa: \"\\f2b0\";\n}\n.fa-modx {\n  --fa: \"\\f285\";\n}\n.fa-guilded {\n  --fa: \"\\e07e\";\n}\n.fa-vnv {\n  --fa: \"\\f40b\";\n}\n.fa-js-square,.fa-square-js {\n  --fa: \"\\f3b9\";\n}\n.fa-microsoft {\n  --fa: \"\\f3ca\";\n}\n.fa-qq {\n  --fa: \"\\f1d6\";\n}\n.fa-orcid {\n  --fa: \"\\f8d2\";\n}\n.fa-java {\n  --fa: \"\\f4e4\";\n}\n.fa-invision {\n  --fa: \"\\f7b0\";\n}\n.fa-creative-commons-pd-alt {\n  --fa: \"\\f4ed\";\n}\n.fa-centercode {\n  --fa: \"\\f380\";\n}\n.fa-glide-g {\n  --fa: \"\\f2a6\";\n}\n.fa-drupal {\n  --fa: \"\\f1a9\";\n}\n.fa-jxl {\n  --fa: \"\\e67b\";\n}\n.fa-dart-lang {\n  --fa: \"\\e693\";\n}\n.fa-hire-a-helper {\n  --fa: \"\\f3b0\";\n}\n.fa-creative-commons-by {\n  --fa: \"\\f4e7\";\n}\n.fa-unity {\n  --fa: \"\\e049\";\n}\n.fa-whmcs {\n  --fa: \"\\f40d\";\n}\n.fa-rocketchat {\n  --fa: \"\\f3e8\";\n}\n.fa-vk {\n  --fa: \"\\f189\";\n}\n.fa-untappd {\n  --fa: \"\\f405\";\n}\n.fa-mailchimp {\n  --fa: \"\\f59e\";\n}\n.fa-css3-alt {\n  --fa: \"\\f38b\";\n}\n.fa-reddit-square,.fa-square-reddit {\n  --fa: \"\\f1a2\";\n}\n.fa-vimeo-v {\n  --fa: \"\\f27d\";\n}\n.fa-contao {\n  --fa: \"\\f26d\";\n}\n.fa-square-font-awesome {\n  --fa: \"\\e5ad\";\n}\n.fa-deskpro {\n  --fa: \"\\f38f\";\n}\n.fa-brave {\n  --fa: \"\\e63c\";\n}\n.fa-sistrix {\n  --fa: \"\\f3ee\";\n}\n.fa-instagram-square,.fa-square-instagram {\n  --fa: \"\\e055\";\n}\n.fa-battle-net {\n  --fa: \"\\f835\";\n}\n.fa-the-red-yeti {\n  --fa: \"\\f69d\";\n}\n.fa-hacker-news-square,.fa-square-hacker-news {\n  --fa: \"\\f3af\";\n}\n.fa-edge {\n  --fa: \"\\f282\";\n}\n.fa-threads {\n  --fa: \"\\e618\";\n}\n.fa-napster {\n  --fa: \"\\f3d2\";\n}\n.fa-snapchat-square,.fa-square-snapchat {\n  --fa: \"\\f2ad\";\n}\n.fa-google-plus-g {\n  --fa: \"\\f0d5\";\n}\n.fa-artstation {\n  --fa: \"\\f77a\";\n}\n.fa-markdown {\n  --fa: \"\\f60f\";\n}\n.fa-sourcetree {\n  --fa: \"\\f7d3\";\n}\n.fa-google-plus {\n  --fa: \"\\f2b3\";\n}\n.fa-diaspora {\n  --fa: \"\\f791\";\n}\n.fa-foursquare {\n  --fa: \"\\f180\";\n}\n.fa-stack-overflow {\n  --fa: \"\\f16c\";\n}\n.fa-github-alt {\n  --fa: \"\\f113\";\n}\n.fa-phoenix-squadron {\n  --fa: \"\\f511\";\n}\n.fa-pagelines {\n  --fa: \"\\f18c\";\n}\n.fa-algolia {\n  --fa: \"\\f36c\";\n}\n.fa-red-river {\n  --fa: \"\\f3e3\";\n}\n.fa-creative-commons-sa {\n  --fa: \"\\f4ef\";\n}\n.fa-safari {\n  --fa: \"\\f267\";\n}\n.fa-google {\n  --fa: \"\\f1a0\";\n}\n.fa-font-awesome-alt,.fa-square-font-awesome-stroke {\n  --fa: \"\\f35c\";\n}\n.fa-atlassian {\n  --fa: \"\\f77b\";\n}\n.fa-linkedin-in {\n  --fa: \"\\f0e1\";\n}\n.fa-digital-ocean {\n  --fa: \"\\f391\";\n}\n.fa-nimblr {\n  --fa: \"\\f5a8\";\n}\n.fa-chromecast {\n  --fa: \"\\f838\";\n}\n.fa-evernote {\n  --fa: \"\\f839\";\n}\n.fa-hacker-news {\n  --fa: \"\\f1d4\";\n}\n.fa-creative-commons-sampling {\n  --fa: \"\\f4f0\";\n}\n.fa-adversal {\n  --fa: \"\\f36a\";\n}\n.fa-creative-commons {\n  --fa: \"\\f25e\";\n}\n.fa-watchman-monitoring {\n  --fa: \"\\e087\";\n}\n.fa-fonticons {\n  --fa: \"\\f280\";\n}\n.fa-weixin {\n  --fa: \"\\f1d7\";\n}\n.fa-shirtsinbulk {\n  --fa: \"\\f214\";\n}\n.fa-codepen {\n  --fa: \"\\f1cb\";\n}\n.fa-git-alt {\n  --fa: \"\\f841\";\n}\n.fa-lyft {\n  --fa: \"\\f3c3\";\n}\n.fa-rev {\n  --fa: \"\\f5b2\";\n}\n.fa-windows {\n  --fa: \"\\f17a\";\n}\n.fa-wizards-of-the-coast {\n  --fa: \"\\f730\";\n}\n.fa-square-viadeo,.fa-viadeo-square {\n  --fa: \"\\f2aa\";\n}\n.fa-meetup {\n  --fa: \"\\f2e0\";\n}\n.fa-centos {\n  --fa: \"\\f789\";\n}\n.fa-adn {\n  --fa: \"\\f170\";\n}\n.fa-cloudsmith {\n  --fa: \"\\f384\";\n}\n.fa-opensuse {\n  --fa: \"\\e62b\";\n}\n.fa-pied-piper-alt {\n  --fa: \"\\f1a8\";\n}\n.fa-dribbble-square,.fa-square-dribbble {\n  --fa: \"\\f397\";\n}\n.fa-codiepie {\n  --fa: \"\\f284\";\n}\n.fa-node {\n  --fa: \"\\f419\";\n}\n.fa-mix {\n  --fa: \"\\f3cb\";\n}\n.fa-steam {\n  --fa: \"\\f1b6\";\n}\n.fa-cc-apple-pay {\n  --fa: \"\\f416\";\n}\n.fa-scribd {\n  --fa: \"\\f28a\";\n}\n.fa-debian {\n  --fa: \"\\e60b\";\n}\n.fa-openid {\n  --fa: \"\\f19b\";\n}\n.fa-instalod {\n  --fa: \"\\e081\";\n}\n.fa-files-pinwheel {\n  --fa: \"\\e69f\";\n}\n.fa-expeditedssl {\n  --fa: \"\\f23e\";\n}\n.fa-sellcast {\n  --fa: \"\\f2da\";\n}\n.fa-square-twitter,.fa-twitter-square {\n  --fa: \"\\f081\";\n}\n.fa-r-project {\n  --fa: \"\\f4f7\";\n}\n.fa-delicious {\n  --fa: \"\\f1a5\";\n}\n.fa-freebsd {\n  --fa: \"\\f3a4\";\n}\n.fa-vuejs {\n  --fa: \"\\f41f\";\n}\n.fa-accusoft {\n  --fa: \"\\f369\";\n}\n.fa-ioxhost {\n  --fa: \"\\f208\";\n}\n.fa-fonticons-fi {\n  --fa: \"\\f3a2\";\n}\n.fa-app-store {\n  --fa: \"\\f36f\";\n}\n.fa-cc-mastercard {\n  --fa: \"\\f1f1\";\n}\n.fa-itunes-note {\n  --fa: \"\\f3b5\";\n}\n.fa-golang {\n  --fa: \"\\e40f\";\n}\n.fa-kickstarter,.fa-square-kickstarter {\n  --fa: \"\\f3bb\";\n}\n.fa-grav {\n  --fa: \"\\f2d6\";\n}\n.fa-weibo {\n  --fa: \"\\f18a\";\n}\n.fa-uncharted {\n  --fa: \"\\e084\";\n}\n.fa-firstdraft {\n  --fa: \"\\f3a1\";\n}\n.fa-square-youtube,.fa-youtube-square {\n  --fa: \"\\f431\";\n}\n.fa-wikipedia-w {\n  --fa: \"\\f266\";\n}\n.fa-rendact,.fa-wpressr {\n  --fa: \"\\f3e4\";\n}\n.fa-angellist {\n  --fa: \"\\f209\";\n}\n.fa-galactic-republic {\n  --fa: \"\\f50c\";\n}\n.fa-nfc-directional {\n  --fa: \"\\e530\";\n}\n.fa-skype {\n  --fa: \"\\f17e\";\n}\n.fa-joget {\n  --fa: \"\\f3b7\";\n}\n.fa-fedora {\n  --fa: \"\\f798\";\n}\n.fa-stripe-s {\n  --fa: \"\\f42a\";\n}\n.fa-meta {\n  --fa: \"\\e49b\";\n}\n.fa-laravel {\n  --fa: \"\\f3bd\";\n}\n.fa-hotjar {\n  --fa: \"\\f3b1\";\n}\n.fa-bluetooth-b {\n  --fa: \"\\f294\";\n}\n.fa-square-letterboxd {\n  --fa: \"\\e62e\";\n}\n.fa-sticker-mule {\n  --fa: \"\\f3f7\";\n}\n.fa-creative-commons-zero {\n  --fa: \"\\f4f3\";\n}\n.fa-hips {\n  --fa: \"\\f452\";\n}\n.fa-css {\n  --fa: \"\\e6a2\";\n}\n.fa-behance {\n  --fa: \"\\f1b4\";\n}\n.fa-reddit {\n  --fa: \"\\f1a1\";\n}\n.fa-discord {\n  --fa: \"\\f392\";\n}\n.fa-chrome {\n  --fa: \"\\f268\";\n}\n.fa-app-store-ios {\n  --fa: \"\\f370\";\n}\n.fa-cc-discover {\n  --fa: \"\\f1f2\";\n}\n.fa-wpbeginner {\n  --fa: \"\\f297\";\n}\n.fa-confluence {\n  --fa: \"\\f78d\";\n}\n.fa-shoelace {\n  --fa: \"\\e60c\";\n}\n.fa-mdb {\n  --fa: \"\\f8ca\";\n}\n.fa-dochub {\n  --fa: \"\\f394\";\n}\n.fa-accessible-icon {\n  --fa: \"\\f368\";\n}\n.fa-ebay {\n  --fa: \"\\f4f4\";\n}\n.fa-amazon {\n  --fa: \"\\f270\";\n}\n.fa-unsplash {\n  --fa: \"\\e07c\";\n}\n.fa-yarn {\n  --fa: \"\\f7e3\";\n}\n.fa-square-steam,.fa-steam-square {\n  --fa: \"\\f1b7\";\n}\n.fa-500px {\n  --fa: \"\\f26e\";\n}\n.fa-square-vimeo,.fa-vimeo-square {\n  --fa: \"\\f194\";\n}\n.fa-asymmetrik {\n  --fa: \"\\f372\";\n}\n.fa-font-awesome,.fa-font-awesome-flag,.fa-font-awesome-logo-full {\n  --fa: \"\\f2b4\";\n}\n.fa-gratipay {\n  --fa: \"\\f184\";\n}\n.fa-apple {\n  --fa: \"\\f179\";\n}\n.fa-hive {\n  --fa: \"\\e07f\";\n}\n.fa-gitkraken {\n  --fa: \"\\f3a6\";\n}\n.fa-keybase {\n  --fa: \"\\f4f5\";\n}\n.fa-apple-pay {\n  --fa: \"\\f415\";\n}\n.fa-padlet {\n  --fa: \"\\e4a0\";\n}\n.fa-amazon-pay {\n  --fa: \"\\f42c\";\n}\n.fa-github-square,.fa-square-github {\n  --fa: \"\\f092\";\n}\n.fa-stumbleupon {\n  --fa: \"\\f1a4\";\n}\n.fa-fedex {\n  --fa: \"\\f797\";\n}\n.fa-phoenix-framework {\n  --fa: \"\\f3dc\";\n}\n.fa-shopify {\n  --fa: \"\\e057\";\n}\n.fa-neos {\n  --fa: \"\\f612\";\n}\n.fa-square-threads {\n  --fa: \"\\e619\";\n}\n.fa-hackerrank {\n  --fa: \"\\f5f7\";\n}\n.fa-researchgate {\n  --fa: \"\\f4f8\";\n}\n.fa-swift {\n  --fa: \"\\f8e1\";\n}\n.fa-angular {\n  --fa: \"\\f420\";\n}\n.fa-speakap {\n  --fa: \"\\f3f3\";\n}\n.fa-angrycreative {\n  --fa: \"\\f36e\";\n}\n.fa-y-combinator {\n  --fa: \"\\f23b\";\n}\n.fa-empire {\n  --fa: \"\\f1d1\";\n}\n.fa-envira {\n  --fa: \"\\f299\";\n}\n.fa-google-scholar {\n  --fa: \"\\e63b\";\n}\n.fa-gitlab-square,.fa-square-gitlab {\n  --fa: \"\\e5ae\";\n}\n.fa-studiovinari {\n  --fa: \"\\f3f8\";\n}\n.fa-pied-piper {\n  --fa: \"\\f2ae\";\n}\n.fa-wordpress {\n  --fa: \"\\f19a\";\n}\n.fa-product-hunt {\n  --fa: \"\\f288\";\n}\n.fa-firefox {\n  --fa: \"\\f269\";\n}\n.fa-linode {\n  --fa: \"\\f2b8\";\n}\n.fa-goodreads {\n  --fa: \"\\f3a8\";\n}\n.fa-odnoklassniki-square,.fa-square-odnoklassniki {\n  --fa: \"\\f264\";\n}\n.fa-jsfiddle {\n  --fa: \"\\f1cc\";\n}\n.fa-sith {\n  --fa: \"\\f512\";\n}\n.fa-themeisle {\n  --fa: \"\\f2b2\";\n}\n.fa-page4 {\n  --fa: \"\\f3d7\";\n}\n.fa-hashnode {\n  --fa: \"\\e499\";\n}\n.fa-react {\n  --fa: \"\\f41b\";\n}\n.fa-cc-paypal {\n  --fa: \"\\f1f4\";\n}\n.fa-squarespace {\n  --fa: \"\\f5be\";\n}\n.fa-cc-stripe {\n  --fa: \"\\f1f5\";\n}\n.fa-creative-commons-share {\n  --fa: \"\\f4f2\";\n}\n.fa-bitcoin {\n  --fa: \"\\f379\";\n}\n.fa-keycdn {\n  --fa: \"\\f3ba\";\n}\n.fa-opera {\n  --fa: \"\\f26a\";\n}\n.fa-itch-io {\n  --fa: \"\\f83a\";\n}\n.fa-umbraco {\n  --fa: \"\\f8e8\";\n}\n.fa-galactic-senate {\n  --fa: \"\\f50d\";\n}\n.fa-ubuntu {\n  --fa: \"\\f7df\";\n}\n.fa-draft2digital {\n  --fa: \"\\f396\";\n}\n.fa-stripe {\n  --fa: \"\\f429\";\n}\n.fa-houzz {\n  --fa: \"\\f27c\";\n}\n.fa-gg {\n  --fa: \"\\f260\";\n}\n.fa-dhl {\n  --fa: \"\\f790\";\n}\n.fa-pinterest-square,.fa-square-pinterest {\n  --fa: \"\\f0d3\";\n}\n.fa-xing {\n  --fa: \"\\f168\";\n}\n.fa-blackberry {\n  --fa: \"\\f37b\";\n}\n.fa-creative-commons-pd {\n  --fa: \"\\f4ec\";\n}\n.fa-playstation {\n  --fa: \"\\f3df\";\n}\n.fa-quinscape {\n  --fa: \"\\f459\";\n}\n.fa-less {\n  --fa: \"\\f41d\";\n}\n.fa-blogger-b {\n  --fa: \"\\f37d\";\n}\n.fa-opencart {\n  --fa: \"\\f23d\";\n}\n.fa-vine {\n  --fa: \"\\f1ca\";\n}\n.fa-signal-messenger {\n  --fa: \"\\e663\";\n}\n.fa-paypal {\n  --fa: \"\\f1ed\";\n}\n.fa-gitlab {\n  --fa: \"\\f296\";\n}\n.fa-typo3 {\n  --fa: \"\\f42b\";\n}\n.fa-reddit-alien {\n  --fa: \"\\f281\";\n}\n.fa-yahoo {\n  --fa: \"\\f19e\";\n}\n.fa-dailymotion {\n  --fa: \"\\e052\";\n}\n.fa-affiliatetheme {\n  --fa: \"\\f36b\";\n}\n.fa-pied-piper-pp {\n  --fa: \"\\f1a7\";\n}\n.fa-bootstrap {\n  --fa: \"\\f836\";\n}\n.fa-odnoklassniki {\n  --fa: \"\\f263\";\n}\n.fa-nfc-symbol {\n  --fa: \"\\e531\";\n}\n.fa-mintbit {\n  --fa: \"\\e62f\";\n}\n.fa-ethereum {\n  --fa: \"\\f42e\";\n}\n.fa-speaker-deck {\n  --fa: \"\\f83c\";\n}\n.fa-creative-commons-nc-eu {\n  --fa: \"\\f4e9\";\n}\n.fa-patreon {\n  --fa: \"\\f3d9\";\n}\n.fa-avianex {\n  --fa: \"\\f374\";\n}\n.fa-ello {\n  --fa: \"\\f5f1\";\n}\n.fa-gofore {\n  --fa: \"\\f3a7\";\n}\n.fa-bimobject {\n  --fa: \"\\f378\";\n}\n.fa-brave-reverse {\n  --fa: \"\\e63d\";\n}\n.fa-facebook-f {\n  --fa: \"\\f39e\";\n}\n.fa-google-plus-square,.fa-square-google-plus {\n  --fa: \"\\f0d4\";\n}\n.fa-web-awesome {\n  --fa: \"\\e682\";\n}\n.fa-mandalorian {\n  --fa: \"\\f50f\";\n}\n.fa-first-order-alt {\n  --fa: \"\\f50a\";\n}\n.fa-osi {\n  --fa: \"\\f41a\";\n}\n.fa-google-wallet {\n  --fa: \"\\f1ee\";\n}\n.fa-d-and-d-beyond {\n  --fa: \"\\f6ca\";\n}\n.fa-periscope {\n  --fa: \"\\f3da\";\n}\n.fa-fulcrum {\n  --fa: \"\\f50b\";\n}\n.fa-cloudscale {\n  --fa: \"\\f383\";\n}\n.fa-forumbee {\n  --fa: \"\\f211\";\n}\n.fa-mizuni {\n  --fa: \"\\f3cc\";\n}\n.fa-schlix {\n  --fa: \"\\f3ea\";\n}\n.fa-square-xing,.fa-xing-square {\n  --fa: \"\\f169\";\n}\n.fa-bandcamp {\n  --fa: \"\\f2d5\";\n}\n.fa-wpforms {\n  --fa: \"\\f298\";\n}\n.fa-cloudversify {\n  --fa: \"\\f385\";\n}\n.fa-usps {\n  --fa: \"\\f7e1\";\n}\n.fa-megaport {\n  --fa: \"\\f5a3\";\n}\n.fa-magento {\n  --fa: \"\\f3c4\";\n}\n.fa-spotify {\n  --fa: \"\\f1bc\";\n}\n.fa-optin-monster {\n  --fa: \"\\f23c\";\n}\n.fa-fly {\n  --fa: \"\\f417\";\n}\n.fa-square-bluesky {\n  --fa: \"\\e6a3\";\n}\n.fa-aviato {\n  --fa: \"\\f421\";\n}\n.fa-itunes {\n  --fa: \"\\f3b4\";\n}\n.fa-cuttlefish {\n  --fa: \"\\f38c\";\n}\n.fa-blogger {\n  --fa: \"\\f37c\";\n}\n.fa-flickr {\n  --fa: \"\\f16e\";\n}\n.fa-viber {\n  --fa: \"\\f409\";\n}\n.fa-soundcloud {\n  --fa: \"\\f1be\";\n}\n.fa-digg {\n  --fa: \"\\f1a6\";\n}\n.fa-tencent-weibo {\n  --fa: \"\\f1d5\";\n}\n.fa-letterboxd {\n  --fa: \"\\e62d\";\n}\n.fa-symfony {\n  --fa: \"\\f83d\";\n}\n.fa-maxcdn {\n  --fa: \"\\f136\";\n}\n.fa-etsy {\n  --fa: \"\\f2d7\";\n}\n.fa-facebook-messenger {\n  --fa: \"\\f39f\";\n}\n.fa-audible {\n  --fa: \"\\f373\";\n}\n.fa-think-peaks {\n  --fa: \"\\f731\";\n}\n.fa-bilibili {\n  --fa: \"\\e3d9\";\n}\n.fa-erlang {\n  --fa: \"\\f39d\";\n}\n.fa-x-twitter {\n  --fa: \"\\e61b\";\n}\n.fa-cotton-bureau {\n  --fa: \"\\f89e\";\n}\n.fa-dashcube {\n  --fa: \"\\f210\";\n}\n.fa-42-group,.fa-innosoft {\n  --fa: \"\\e080\";\n}\n.fa-stack-exchange {\n  --fa: \"\\f18d\";\n}\n.fa-elementor {\n  --fa: \"\\f430\";\n}\n.fa-pied-piper-square,.fa-square-pied-piper {\n  --fa: \"\\e01e\";\n}\n.fa-creative-commons-nd {\n  --fa: \"\\f4eb\";\n}\n.fa-palfed {\n  --fa: \"\\f3d8\";\n}\n.fa-superpowers {\n  --fa: \"\\f2dd\";\n}\n.fa-resolving {\n  --fa: \"\\f3e7\";\n}\n.fa-xbox {\n  --fa: \"\\f412\";\n}\n.fa-square-web-awesome-stroke {\n  --fa: \"\\e684\";\n}\n.fa-searchengin {\n  --fa: \"\\f3eb\";\n}\n.fa-tiktok {\n  --fa: \"\\e07b\";\n}\n.fa-facebook-square,.fa-square-facebook {\n  --fa: \"\\f082\";\n}\n.fa-renren {\n  --fa: \"\\f18b\";\n}\n.fa-linux {\n  --fa: \"\\f17c\";\n}\n.fa-glide {\n  --fa: \"\\f2a5\";\n}\n.fa-linkedin {\n  --fa: \"\\f08c\";\n}\n.fa-hubspot {\n  --fa: \"\\f3b2\";\n}\n.fa-deploydog {\n  --fa: \"\\f38e\";\n}\n.fa-twitch {\n  --fa: \"\\f1e8\";\n}\n.fa-flutter {\n  --fa: \"\\e694\";\n}\n.fa-ravelry {\n  --fa: \"\\f2d9\";\n}\n.fa-mixer {\n  --fa: \"\\e056\";\n}\n.fa-lastfm-square,.fa-square-lastfm {\n  --fa: \"\\f203\";\n}\n.fa-vimeo {\n  --fa: \"\\f40a\";\n}\n.fa-mendeley {\n  --fa: \"\\f7b3\";\n}\n.fa-uniregistry {\n  --fa: \"\\f404\";\n}\n.fa-figma {\n  --fa: \"\\f799\";\n}\n.fa-creative-commons-remix {\n  --fa: \"\\f4ee\";\n}\n.fa-cc-amazon-pay {\n  --fa: \"\\f42d\";\n}\n.fa-dropbox {\n  --fa: \"\\f16b\";\n}\n.fa-instagram {\n  --fa: \"\\f16d\";\n}\n.fa-cmplid {\n  --fa: \"\\e360\";\n}\n.fa-upwork {\n  --fa: \"\\e641\";\n}\n.fa-facebook {\n  --fa: \"\\f09a\";\n}\n.fa-gripfire {\n  --fa: \"\\f3ac\";\n}\n.fa-jedi-order {\n  --fa: \"\\f50e\";\n}\n.fa-uikit {\n  --fa: \"\\f403\";\n}\n.fa-fort-awesome-alt {\n  --fa: \"\\f3a3\";\n}\n.fa-phabricator {\n  --fa: \"\\f3db\";\n}\n.fa-ussunnah {\n  --fa: \"\\f407\";\n}\n.fa-earlybirds {\n  --fa: \"\\f39a\";\n}\n.fa-trade-federation {\n  --fa: \"\\f513\";\n}\n.fa-autoprefixer {\n  --fa: \"\\f41c\";\n}\n.fa-whatsapp {\n  --fa: \"\\f232\";\n}\n.fa-square-upwork {\n  --fa: \"\\e67c\";\n}\n.fa-slideshare {\n  --fa: \"\\f1e7\";\n}\n.fa-google-play {\n  --fa: \"\\f3ab\";\n}\n.fa-viadeo {\n  --fa: \"\\f2a9\";\n}\n.fa-line {\n  --fa: \"\\f3c0\";\n}\n.fa-google-drive {\n  --fa: \"\\f3aa\";\n}\n.fa-servicestack {\n  --fa: \"\\f3ec\";\n}\n.fa-simplybuilt {\n  --fa: \"\\f215\";\n}\n.fa-bitbucket {\n  --fa: \"\\f171\";\n}\n.fa-imdb {\n  --fa: \"\\f2d8\";\n}\n.fa-deezer {\n  --fa: \"\\e077\";\n}\n.fa-raspberry-pi {\n  --fa: \"\\f7bb\";\n}\n.fa-jira {\n  --fa: \"\\f7b1\";\n}\n.fa-docker {\n  --fa: \"\\f395\";\n}\n.fa-screenpal {\n  --fa: \"\\e570\";\n}\n.fa-bluetooth {\n  --fa: \"\\f293\";\n}\n.fa-gitter {\n  --fa: \"\\f426\";\n}\n.fa-d-and-d {\n  --fa: \"\\f38d\";\n}\n.fa-microblog {\n  --fa: \"\\e01a\";\n}\n.fa-cc-diners-club {\n  --fa: \"\\f24c\";\n}\n.fa-gg-circle {\n  --fa: \"\\f261\";\n}\n.fa-pied-piper-hat {\n  --fa: \"\\f4e5\";\n}\n.fa-kickstarter-k {\n  --fa: \"\\f3bc\";\n}\n.fa-yandex {\n  --fa: \"\\f413\";\n}\n.fa-readme {\n  --fa: \"\\f4d5\";\n}\n.fa-html5 {\n  --fa: \"\\f13b\";\n}\n.fa-sellsy {\n  --fa: \"\\f213\";\n}\n.fa-square-web-awesome {\n  --fa: \"\\e683\";\n}\n.fa-sass {\n  --fa: \"\\f41e\";\n}\n.fa-wirsindhandwerk,.fa-wsh {\n  --fa: \"\\e2d0\";\n}\n.fa-buromobelexperte {\n  --fa: \"\\f37f\";\n}\n.fa-salesforce {\n  --fa: \"\\f83b\";\n}\n.fa-octopus-deploy {\n  --fa: \"\\e082\";\n}\n.fa-medapps {\n  --fa: \"\\f3c6\";\n}\n.fa-ns8 {\n  --fa: \"\\f3d5\";\n}\n.fa-pinterest-p {\n  --fa: \"\\f231\";\n}\n.fa-apper {\n  --fa: \"\\f371\";\n}\n.fa-fort-awesome {\n  --fa: \"\\f286\";\n}\n.fa-waze {\n  --fa: \"\\f83f\";\n}\n.fa-bluesky {\n  --fa: \"\\e671\";\n}\n.fa-cc-jcb {\n  --fa: \"\\f24b\";\n}\n.fa-snapchat,.fa-snapchat-ghost {\n  --fa: \"\\f2ab\";\n}\n.fa-fantasy-flight-games {\n  --fa: \"\\f6dc\";\n}\n.fa-rust {\n  --fa: \"\\e07a\";\n}\n.fa-wix {\n  --fa: \"\\f5cf\";\n}\n.fa-behance-square,.fa-square-behance {\n  --fa: \"\\f1b5\";\n}\n.fa-supple {\n  --fa: \"\\f3f9\";\n}\n.fa-webflow {\n  --fa: \"\\e65c\";\n}\n.fa-rebel {\n  --fa: \"\\f1d0\";\n}\n.fa-css3 {\n  --fa: \"\\f13c\";\n}\n.fa-staylinked {\n  --fa: \"\\f3f5\";\n}\n.fa-kaggle {\n  --fa: \"\\f5fa\";\n}\n.fa-space-awesome {\n  --fa: \"\\e5ac\";\n}\n.fa-deviantart {\n  --fa: \"\\f1bd\";\n}\n.fa-cpanel {\n  --fa: \"\\f388\";\n}\n.fa-goodreads-g {\n  --fa: \"\\f3a9\";\n}\n.fa-git-square,.fa-square-git {\n  --fa: \"\\f1d2\";\n}\n.fa-square-tumblr,.fa-tumblr-square {\n  --fa: \"\\f174\";\n}\n.fa-trello {\n  --fa: \"\\f181\";\n}\n.fa-creative-commons-nc-jp {\n  --fa: \"\\f4ea\";\n}\n.fa-get-pocket {\n  --fa: \"\\f265\";\n}\n.fa-perbyte {\n  --fa: \"\\e083\";\n}\n.fa-grunt {\n  --fa: \"\\f3ad\";\n}\n.fa-weebly {\n  --fa: \"\\f5cc\";\n}\n.fa-connectdevelop {\n  --fa: \"\\f20e\";\n}\n.fa-leanpub {\n  --fa: \"\\f212\";\n}\n.fa-black-tie {\n  --fa: \"\\f27e\";\n}\n.fa-themeco {\n  --fa: \"\\f5c6\";\n}\n.fa-python {\n  --fa: \"\\f3e2\";\n}\n.fa-android {\n  --fa: \"\\f17b\";\n}\n.fa-bots {\n  --fa: \"\\e340\";\n}\n.fa-free-code-camp {\n  --fa: \"\\f2c5\";\n}\n.fa-hornbill {\n  --fa: \"\\f592\";\n}\n.fa-js {\n  --fa: \"\\f3b8\";\n}\n.fa-ideal {\n  --fa: \"\\e013\";\n}\n.fa-git {\n  --fa: \"\\f1d3\";\n}\n.fa-dev {\n  --fa: \"\\f6cc\";\n}\n.fa-sketch {\n  --fa: \"\\f7c6\";\n}\n.fa-yandex-international {\n  --fa: \"\\f414\";\n}\n.fa-cc-amex {\n  --fa: \"\\f1f3\";\n}\n.fa-uber {\n  --fa: \"\\f402\";\n}\n.fa-github {\n  --fa: \"\\f09b\";\n}\n.fa-php {\n  --fa: \"\\f457\";\n}\n.fa-alipay {\n  --fa: \"\\f642\";\n}\n.fa-youtube {\n  --fa: \"\\f167\";\n}\n.fa-skyatlas {\n  --fa: \"\\f216\";\n}\n.fa-firefox-browser {\n  --fa: \"\\e007\";\n}\n.fa-replyd {\n  --fa: \"\\f3e6\";\n}\n.fa-suse {\n  --fa: \"\\f7d6\";\n}\n.fa-jenkins {\n  --fa: \"\\f3b6\";\n}\n.fa-twitter {\n  --fa: \"\\f099\";\n}\n.fa-rockrms {\n  --fa: \"\\f3e9\";\n}\n.fa-pinterest {\n  --fa: \"\\f0d2\";\n}\n.fa-buffer {\n  --fa: \"\\f837\";\n}\n.fa-npm {\n  --fa: \"\\f3d4\";\n}\n.fa-yammer {\n  --fa: \"\\f840\";\n}\n.fa-btc {\n  --fa: \"\\f15a\";\n}\n.fa-dribbble {\n  --fa: \"\\f17d\";\n}\n.fa-stumbleupon-circle {\n  --fa: \"\\f1a3\";\n}\n.fa-internet-explorer {\n  --fa: \"\\f26b\";\n}\n.fa-stubber {\n  --fa: \"\\e5c7\";\n}\n.fa-telegram,.fa-telegram-plane {\n  --fa: \"\\f2c6\";\n}\n.fa-old-republic {\n  --fa: \"\\f510\";\n}\n.fa-odysee {\n  --fa: \"\\e5c6\";\n}\n.fa-square-whatsapp,.fa-whatsapp-square {\n  --fa: \"\\f40c\";\n}\n.fa-node-js {\n  --fa: \"\\f3d3\";\n}\n.fa-edge-legacy {\n  --fa: \"\\e078\";\n}\n.fa-slack,.fa-slack-hash {\n  --fa: \"\\f198\";\n}\n.fa-medrt {\n  --fa: \"\\f3c8\";\n}\n.fa-usb {\n  --fa: \"\\f287\";\n}\n.fa-tumblr {\n  --fa: \"\\f173\";\n}\n.fa-vaadin {\n  --fa: \"\\f408\";\n}\n.fa-quora {\n  --fa: \"\\f2c4\";\n}\n.fa-square-x-twitter {\n  --fa: \"\\e61a\";\n}\n.fa-reacteurope {\n  --fa: \"\\f75d\";\n}\n.fa-medium,.fa-medium-m {\n  --fa: \"\\f23a\";\n}\n.fa-amilia {\n  --fa: \"\\f36d\";\n}\n.fa-mixcloud {\n  --fa: \"\\f289\";\n}\n.fa-flipboard {\n  --fa: \"\\f44d\";\n}\n.fa-viacoin {\n  --fa: \"\\f237\";\n}\n.fa-critical-role {\n  --fa: \"\\f6c9\";\n}\n.fa-sitrox {\n  --fa: \"\\e44a\";\n}\n.fa-discourse {\n  --fa: \"\\f393\";\n}\n.fa-joomla {\n  --fa: \"\\f1aa\";\n}\n.fa-mastodon {\n  --fa: \"\\f4f6\";\n}\n.fa-airbnb {\n  --fa: \"\\f834\";\n}\n.fa-wolf-pack-battalion {\n  --fa: \"\\f514\";\n}\n.fa-buy-n-large {\n  --fa: \"\\f8a6\";\n}\n.fa-gulp {\n  --fa: \"\\f3ae\";\n}\n.fa-creative-commons-sampling-plus {\n  --fa: \"\\f4f1\";\n}\n.fa-strava {\n  --fa: \"\\f428\";\n}\n.fa-ember {\n  --fa: \"\\f423\";\n}\n.fa-canadian-maple-leaf {\n  --fa: \"\\f785\";\n}\n.fa-teamspeak {\n  --fa: \"\\f4f9\";\n}\n.fa-pushed {\n  --fa: \"\\f3e1\";\n}\n.fa-wordpress-simple {\n  --fa: \"\\f411\";\n}\n.fa-nutritionix {\n  --fa: \"\\f3d6\";\n}\n.fa-wodu {\n  --fa: \"\\e088\";\n}\n.fa-google-pay {\n  --fa: \"\\e079\";\n}\n.fa-intercom {\n  --fa: \"\\f7af\";\n}\n.fa-zhihu {\n  --fa: \"\\f63f\";\n}\n.fa-korvue {\n  --fa: \"\\f42f\";\n}\n.fa-pix {\n  --fa: \"\\e43a\";\n}\n.fa-steam-symbol {\n  --fa: \"\\f3f6\";\n}\n:host,:root {\n  --fa-font-regular: normal 400 1em/1 \"Font Awesome 6 Free\";\n}\n@font-face {\n  font-family: \"Font Awesome 6 Free\";\n  font-style: normal;\n  font-weight: 400;\n  font-display: block;\n  src: url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff2) format(\"woff2\"),url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-regular-400.ttf) format(\"truetype\");\n}\n.fa-regular,.far {\n  font-weight: 400;\n}\n:host,:root {\n  --fa-style-family-classic: \"Font Awesome 6 Free\";\n  --fa-font-solid: normal 900 1em/1 \"Font Awesome 6 Free\";\n}\n@font-face {\n  font-family: \"Font Awesome 6 Free\";\n  font-style: normal;\n  font-weight: 900;\n  font-display: block;\n  src: url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff2) format(\"woff2\"),url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-solid-900.ttf) format(\"truetype\");\n}\n.fa-solid,.fas {\n  font-weight: 900;\n}\n@font-face {\n  font-family: \"Font Awesome 5 Brands\";\n  font-display: block;\n  font-weight: 400;\n  src: url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-brands-400.woff2) format(\"woff2\"),url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-brands-400.ttf) format(\"truetype\");\n}\n@font-face {\n  font-family: \"Font Awesome 5 Free\";\n  font-display: block;\n  font-weight: 900;\n  src: url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff2) format(\"woff2\"),url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-solid-900.ttf) format(\"truetype\");\n}\n@font-face {\n  font-family: \"Font Awesome 5 Free\";\n  font-display: block;\n  font-weight: 400;\n  src: url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff2) format(\"woff2\"),url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-regular-400.ttf) format(\"truetype\");\n}\n@font-face {\n  font-family: \"FontAwesome\";\n  font-display: block;\n  src: url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-solid-900.woff2) format(\"woff2\"),url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-solid-900.ttf) format(\"truetype\");\n}\n@font-face {\n  font-family: \"FontAwesome\";\n  font-display: block;\n  src: url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-brands-400.woff2) format(\"woff2\"),url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-brands-400.ttf) format(\"truetype\");\n}\n@font-face {\n  font-family: \"FontAwesome\";\n  font-display: block;\n  src: url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-regular-400.woff2) format(\"woff2\"),url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-regular-400.ttf) format(\"truetype\");\n  unicode-range: u+f003,u+f006,u+f014,u+f016-f017,u+f01a-f01b,u+f01d,u+f022,u+f03e,u+f044,u+f046,u+f05c-f05d,u+f06e,u+f070,u+f087-f088,u+f08a,u+f094,u+f096-f097,u+f09d,u+f0a0,u+f0a2,u+f0a4-f0a7,u+f0c5,u+f0c7,u+f0e5-f0e6,u+f0eb,u+f0f6-f0f8,u+f10c,u+f114-f115,u+f118-f11a,u+f11c-f11d,u+f133,u+f147,u+f14e,u+f150-f152,u+f185-f186,u+f18e,u+f190-f192,u+f196,u+f1c1-f1c9,u+f1d9,u+f1db,u+f1e3,u+f1ea,u+f1f7,u+f1f9,u+f20a,u+f247-f248,u+f24a,u+f24d,u+f255-f25b,u+f25d,u+f271-f274,u+f278,u+f27b,u+f28c,u+f28e,u+f29c,u+f2b5,u+f2b7,u+f2ba,u+f2bc,u+f2be,u+f2c0-f2c1,u+f2c3,u+f2d0,u+f2d2,u+f2d4,u+f2dc;\n}\n@font-face {\n  font-family: \"FontAwesome\";\n  font-display: block;\n  src: url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-v4compatibility.woff2) format(\"woff2\"),url(../../node_modules/@fortawesome/fontawesome-free/webfonts/fa-v4compatibility.ttf) format(\"truetype\");\n  unicode-range: u+f041,u+f047,u+f065-f066,u+f07d-f07e,u+f080,u+f08b,u+f08e,u+f090,u+f09a,u+f0ac,u+f0ae,u+f0b2,u+f0d0,u+f0d6,u+f0e4,u+f0ec,u+f10a-f10b,u+f123,u+f13e,u+f148-f149,u+f14c,u+f156,u+f15e,u+f160-f161,u+f163,u+f175-f178,u+f195,u+f1f8,u+f219,u+f27a;\n}\n:root {\n  --background: #ffffff;\n  --foreground: #000000;\n}\n@media (prefers-color-scheme: dark) {\n  :root {\n    --background: #0a0a0a;\n    --foreground: #ededed;\n  }\n}\nbody {\n  background: var(--background);\n  color: var(--foreground);\n  font-family: Arial, Helvetica, sans-serif;\n}\n@keyframes marquee {\n  0% {\n    transform: translateX(100%);\n  }\n  100% {\n    transform: translateX(-100%);\n  }\n}\n.animate-marquee {\n  display: inline-block;\n  min-width: 100%;\n  animation: marquee 15s linear infinite;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@keyframes bounce {\n  0%, 100% {\n    transform: translateY(-25%);\n    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\n  }\n  50% {\n    transform: none;\n    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n    }\n  }\n}"], "names": [], "mappings": "AACA;EAk5OE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAl5OJ;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIE;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAOA;;;;;;AAMF;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAEE;EAAgD;;;;;AAIlD;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAMI;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;;AAOzB;;;;;AAMA;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;;AAMzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAKzB;EAAyB;;;;;AAI3B;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EA8EE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AA1NF;;AAoOA;;;;;AAIA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;;;;AAOA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;AAQA;;;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;;;;AAGA;;;;;;;;AAOA;EACE;;;;;;;;;AAQF;;;;;;;;;;AAQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA;;;;;;AAKA;;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA;;;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;AAQA;;;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;;;AAWA;;;;;AAIA;;;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;AAOA;;;;AAGA;;;;;AAIA;;;;;;;;AAOA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AAMA;;;;;AAIA;EACE;;;;;;AAKF;;;;;;AAKA;;;;;;;;;;AAQA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA"}}]}