{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/src/app/committees/page.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\n\r\nexport default function Page() {\r\n  return (\r\n    <div>\r\n      {/* صورة الهيرو */}\r\n      <div className=\"relative h-[300px] md:h-[400px] w-full\">\r\n        <img\r\n          src=\"/photo1.png\"\r\n          alt=\"About IREGO\"\r\n          className=\"object-cover w-full h-full\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n          <h1 className=\"text-3xl md:text-5xl font-bold text-white\">Committees</h1>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Conference Leadership */}\r\n      <div className=\"mt-16 text-center\">\r\n        <span className=\"text-black text-3xl font-bold\">Conference </span>\r\n        <span className=\"text-orange-500 text-3xl font-semibold\">Leadership</span>\r\n      </div>\r\n\r\n      <div className=\"max-w-7xl mx-auto px-4 py-12 grid grid-cols-1 md:grid-cols-2 gap-8 mt-8\">\r\n        {/* Card 1 */}\r\n        <div className=\"flex flex-col md:flex-row items-center bg-white shadow-md rounded-lg overflow-hidden\">\r\n          <img\r\n            src=\"/ibrahim1.png\"\r\n            alt=\"Prof. Ibrahim Rahoma\"\r\n            className=\"w-24 h-24 object-cover rounded-full\"\r\n          />\r\n          <div className=\"p-6 text-center md:text-left\">\r\n            <h3 className=\"text-xl font-bold\">Prof. Ibrahim Rahoma</h3>\r\n            <p className=\"text-orange-500 font-semibold\">Conference Chair</p>\r\n            <p className=\"text-gray-600 mt-2\">\r\n              College of Engineering Technology Janzour<br />\r\n              Tripoli-Libya\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Card 2 */}\r\n        <div className=\"flex flex-col md:flex-row items-center bg-white shadow-md rounded-lg overflow-hidden\">\r\n          <img\r\n            src=\"/mahboub.png\"\r\n            alt=\"Dr. Mahboub Edreder\"\r\n            className=\"w-24 h-24 object-cover rounded-full\"\r\n          />\r\n          <div className=\"p-6 text-center md:text-left\">\r\n            <h3 className=\"text-xl font-bold\">Dr. Mahboub Edreder</h3>\r\n            <p className=\"text-orange-500 font-semibold\">Conference Chair</p>\r\n            <p className=\"text-gray-600 mt-2\">\r\n              National Oil Corporation<br />\r\n              Tripoli-Libya\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Advisory Committee */}\r\n      <div className=\"mt-16 text-center\">\r\n        <span className=\"text-black text-4xl font-bold\">Advisory </span>\r\n        <span className=\"text-orange-500 text-4xl font-semibold\">Committee</span>\r\n      </div>\r\n\r\n      <div className=\"max-w-5xl mx-auto mt-10 px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6\">\r\n        {[\r\n          { name: 'Dr. Nouri Al-Fallo', icon: 'fa-solar-panel' },\r\n          { name: 'Professor Dr. Mohamed Ben Ali', icon: 'fa-leaf' },\r\n          { name: 'Professor Dr. Ahmed Aboudheir', icon: 'fa-globe-americas' },\r\n          { name: 'Prof. Dr. Mohamed Al-jemni', icon: 'fa-industry', extra: '(IEEE Senior Member)' },\r\n          { name: 'Dr Refai taher refai', icon: 'fa-leaf' },\r\n          { name: 'Professor Dr. Habib Kammoun', icon: 'fa-leaf' },\r\n        ].map((person, index) => (\r\n          <div key={index} className=\"bg-white shadow-md rounded-lg p-6\">\r\n            <div className=\"flex items-center gap-3 mb-2\">\r\n              <i className={`fas ${person.icon} text-orange-500`}></i>\r\n              <h3 className=\"text-lg font-semibold\">\r\n                {person.name}\r\n                {person.extra && (\r\n                  <span className=\"block text-sm text-gray-500\">{person.extra}</span>\r\n                )}\r\n              </h3>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Scientific Committee */}\r\n      <div className=\"mt-16 text-center\">\r\n        <span className=\"text-black text-4xl font-bold\">Scientific </span>\r\n        <span className=\"text-orange-500 text-4xl font-semibold\">Committee</span>\r\n      </div>\r\n\r\n      <div className=\"flex justify-center mt-16 px-4\">\r\n        <div className=\"flex flex-col md:flex-row items-center bg-white shadow-md rounded-lg overflow-hidden max-w-md w-full\">\r\n          <img\r\n            src=\"/mohamed.png\"\r\n            alt=\"Dr. Mohamed Al-Tuhami\"\r\n            className=\"w-24 h-24 object-cover rounded-full\"\r\n          />\r\n          <div className=\"p-6 text-center md:text-left\">\r\n            <h3 className=\"text-xl font-bold\">Dr. Mohamed Al-Tuhami</h3>\r\n            <p className=\"text-orange-500 font-semibold\">Conference Chair</p>\r\n            <p className=\"text-gray-600 mt-2\">\r\n              College of Engineering Technology<br />\r\n              Tripoli-Libya\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Extra Members */}\r\n      <div className=\" transition duration-700 ease-in-out ... max-w-5xl mx-auto mt-16 px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6\">\r\n        {[\r\n          {\r\n            name: 'Mr. Osama Al-Ghoul',\r\n            img: '/Z.png',\r\n            role: 'College of Engineering Technology, Janzour – Tripoli, Libya',\r\n          },\r\n          {\r\n            name: 'Engg. Sohaila Abdelkarim',\r\n            img: '/E.jpg',\r\n            role: 'College of Engineering Technology, Janzour – Tripoli',\r\n          },\r\n          {\r\n            name: 'Dr. Mohamed Karim KEFI',\r\n            img: '/M.png',\r\n            role: 'IDRAC Business School - France',\r\n          },\r\n          {\r\n            name: 'Dr. Riyadh Ramadhan Ikreedeegh',\r\n            img: '/T.png',\r\n            role: 'Arabian Gulf Oil Company - Benghazi, Libya',\r\n          },\r\n {\r\n            name: 'Dr. Fathi Al-Hashimi',\r\n            img: '/F.png',\r\n            role: 'Libyan Academy for Postgraduate Studies – Libya',\r\n          },\r\n {\r\n            name: 'Dr. Mohamed Nouri Sbeta',\r\n            img: '/M.png',\r\n            role: ' Libyan Center for Climate Change Research – Tripoli, Libya',\r\n          },\r\n {\r\n            name: 'Dr. Nouri Al-Fallo,',\r\n            img: '/N.jpg',\r\n            role: ' Nafusa Oil Operations Company – Tripoli, Libya',\r\n          },\r\n {\r\n            name: 'Dr. Abdelsalam Al-Hanashi,',\r\n            img: '/A.jpg',\r\n            role: 'University of Pisa – Pisa, Italy',\r\n          },\r\n\r\n {\r\n            name: 'Dr. Hala Al-Khazandar',\r\n            img: '/H.png',\r\n            role: 'Islamic University of Gaza – Gaza, Palestine',\r\n          },\r\n {\r\n            name: 'Dr. Firas Obeidat',\r\n            img: '/F.png',\r\n            role: 'Philadelphia University – Jerash, Jordan',\r\n          },\r\n {\r\n            name: 'Dr. Yara Haddad',\r\n            img: '/Y.svg',\r\n            role: 'Al-Hussein Technical University – Amman, Jordan',\r\n          },\r\n {\r\n            name: 'Dr. Abdel Taleb Bun',\r\n            img: '/A.jpg',\r\n            role: ' Universiti Tun Hussein Onn – Malaysia',\r\n          },\r\n {\r\n            name: 'Dr. Anas Buer',\r\n            img: '/A.jpg',\r\n            role: 'University of Benghazi – Libya'\r\n,\r\n          },\r\n {\r\n            name: 'Dr. Mohamed Almaktar',\r\n            img: '/M.png',\r\n            role: 'Benghazi - Libya',\r\n          },\r\n {\r\n            name: 'Dr. Yasser Nassar',\r\n            img: '/Y.svg',\r\n            role: 'Wadi Al-Shati University – Wadi Al-Shati, Libya',\r\n          },\r\n\r\n {\r\n            name: 'Dr. Abdelkader Al-Sherif',\r\n            img: '/A.jpg',\r\n            role: ' Faculty of Technical Sciences – Sabha, Libya',\r\n          },\r\n\r\n\r\n {\r\n            name: 'Dr. Taher Tata',\r\n            img: '/T.png',\r\n            role: ' Faculty of Technical Sciences – Sabha, Libya',\r\n          },\r\n\r\n {\r\n            name: 'Engg. Ahmed Lajili A.Ali',\r\n            img: '/A.jpg',\r\n            role: ' College of Engineering Technology, Janzour – Tripoli, Libya',\r\n          },\r\n\r\n        \r\n {\r\n            name: 'Engg. Sohaila Abdelkarim',\r\n            img: '/S.png',\r\n            role: 'College of Engineering Technology, Janzour – Tripoli, Libya',\r\n          },\r\n          \r\n\r\n {\r\n            name: 'Dr. Jamel Azibi, Law Faculty ',\r\n            img: '/J.jpg',\r\n            role: 'Law Faculty - Tunisia',\r\n          },\r\n\r\n\r\n\r\n\r\n\r\n\r\n {\r\n            name: 'Dr. Mohamed Karim KEFI',\r\n            img: '/M.png',\r\n            role: ' IDRAC Business School - Franceisia',\r\n          },\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n        ].map((member, index) => (\r\n          <div key={index} className=\"bg-white shadow-md rounded-lg p-6\">\r\n            <div className=\"flex items-center gap-3 mb-2\">\r\n              <img\r\n                src={member.img}\r\n                alt={member.name}\r\n                className=\"w-14 h-14 object-cover rounded-full\"\r\n              />\r\n              <div>\r\n                <h3 className=\"text-lg font-semibold\">{member.name}</h3>\r\n                <p className=\"text-sm text-gray-600\">{member.role}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Another Leadership */}\r\n      <div className=\"mt-16 text-center\">\r\n        <span className=\"text-black text-3xl font-bold\">Conference </span>\r\n        <span className=\"text-orange-500 text-3xl font-semibold\">Leadership</span>\r\n      </div>\r\n\r\n\r\n\r\n      <div className=\"flex justify-center mt-16 px-4\">\r\n        <div className=\"flex flex-col md:flex-row items-center bg-white shadow-md rounded-lg overflow-hidden max-w-md w-full\">\r\n          <img\r\n            src=\"/mo.png\"\r\n            alt=\"Eng. Muad Muftah Al-Jarmi\"\r\n            className=\"w-24 h-24 object-cover rounded-full\"\r\n          />\r\n          <div className=\"p-6 text-center md:text-left\">\r\n            <h3 className=\"text-xl font-bold\">Eng. Muad Muftah Al-Jarmi</h3>\r\n            <p className=\"text-orange-500 font-semibold\">Conference Chair</p>\r\n            <p className=\"text-gray-600 mt-2\">\r\n              Zawia Oil Refining Company<br />\r\n              Tripoli-Libya\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n        \r\n\r\n\r\n\r\n\r\n\r\n\r\n  <div className=\" transition duration-700 ease-in-out ... max-w-5xl mx-auto mt-16 px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6\">\r\n        {[\r\n          {\r\n            name: 'Fatima Al-Jahawi',\r\n            img: '/F.png',\r\n          },\r\n          {\r\n            name: 'Abdulshafi Fathi Al-Hadi',\r\n            img: '/A.jpg',\r\n          },\r\n          {\r\n            name: 'Ibtehal Mohammed Rasheed',\r\n            img: '/E.jpg  ',\r\n          },\r\n          {\r\n            name: 'Hanan Abdulqader Agmeid',\r\n            img: '/H.png',\r\n          },\r\n {\r\n            name: 'Musab Mohammed Shauron',\r\n            img: '/M.png',\r\n          },\r\n {\r\n            name: 'Zuheir Al-Marzouq',\r\n            img: '/Z.png',\r\n          },\r\n {\r\n            name: 'Ibrahim Elashhab – Italy',\r\n            img: '/E.jpg',\r\n          },\r\n {\r\n            name: 'Taha Al-Juwashi – Spain',\r\n            img: '/T.png',\r\n          },\r\n\r\n {\r\n            name: 'Sara Al-Qadi',\r\n            img: '/S.png',\r\n\r\n          },\r\n {\r\n            name: 'Othman Al-Werfalli – Germany',\r\n            img: '/H.png',\r\n          },\r\n {\r\n            name: 'Wedad Al-Awami',\r\n            img: '/M.png',\r\n          },\r\n {\r\n            name: 'Mohamed Al-Kouni Al-Ahrash – Germany',\r\n            img: '/M.png',\r\n          },\r\n {\r\n            name: 'Khaled Liyas – United States of America',\r\n            img: '/K.png',\r\n\r\n          },\r\n {\r\n            name: 'Asaad Khalifa',\r\n            img: '/A.jpg',\r\n          },\r\n\r\n {\r\n            name: 'Taha Al-Juwashi – Spain',\r\n            img: '/T.png  ',\r\n          },\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n        ].map((member, index) => (\r\n          <div key={index} className=\"bg-white shadow-md rounded-lg p-6\">\r\n            <div className=\"flex items-center gap-3 mb-2\">\r\n              <img\r\n                src={member.img}\r\n                alt={member.name}\r\n                className=\"w-14 h-14 object-cover rounded-full\"\r\n              />\r\n              <div >\r\n                <h3 className=\"text-lg font-semibold\">{member.name}</h3>\r\n                <p className=\"text-sm text-gray-600\">{member.img}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGe,SAAS;IACtB,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;;;;;;;;;;;;0BAK9D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAChD,8OAAC;wBAAK,WAAU;kCAAyC;;;;;;;;;;;;0BAG3D,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAI;gCACJ,KAAI;gCACJ,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;;4CAAqB;0DACS,8OAAC;;;;;4CAAK;;;;;;;;;;;;;;;;;;;kCAOrD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAI;gCACJ,KAAI;gCACJ,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;;4CAAqB;0DACR,8OAAC;;;;;4CAAK;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAChD,8OAAC;wBAAK,WAAU;kCAAyC;;;;;;;;;;;;0BAG3D,8OAAC;gBAAI,WAAU;0BACZ;oBACC;wBAAE,MAAM;wBAAsB,MAAM;oBAAiB;oBACrD;wBAAE,MAAM;wBAAiC,MAAM;oBAAU;oBACzD;wBAAE,MAAM;wBAAiC,MAAM;oBAAoB;oBACnE;wBAAE,MAAM;wBAA8B,MAAM;wBAAe,OAAO;oBAAuB;oBACzF;wBAAE,MAAM;wBAAwB,MAAM;oBAAU;oBAChD;wBAAE,MAAM;wBAA+B,MAAM;oBAAU;iBACxD,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,8OAAC;wBAAgB,WAAU;kCACzB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAW,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,gBAAgB,CAAC;;;;;;8CAClD,8OAAC;oCAAG,WAAU;;wCACX,OAAO,IAAI;wCACX,OAAO,KAAK,kBACX,8OAAC;4CAAK,WAAU;sDAA+B,OAAO,KAAK;;;;;;;;;;;;;;;;;;uBANzD;;;;;;;;;;0BAed,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAChD,8OAAC;wBAAK,WAAU;kCAAyC;;;;;;;;;;;;0BAG3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAI;4BACJ,KAAI;4BACJ,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoB;;;;;;8CAClC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAC7C,8OAAC;oCAAE,WAAU;;wCAAqB;sDACC,8OAAC;;;;;wCAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC;gBAAI,WAAU;0BACZ;oBACC;wBACE,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACA;wBACE,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACA;wBACE,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACA;wBACE,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBAET;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBAER;oBACT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBACT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBAET;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBAGT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBAET;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBAGT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBAGT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;oBAOT;wBACW,MAAM;wBACN,KAAK;wBACL,MAAM;oBACR;iBAeD,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,8OAAC;wBAAgB,WAAU;kCACzB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK,OAAO,GAAG;oCACf,KAAK,OAAO,IAAI;oCAChB,WAAU;;;;;;8CAEZ,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyB,OAAO,IAAI;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAyB,OAAO,IAAI;;;;;;;;;;;;;;;;;;uBAT7C;;;;;;;;;;0BAiBd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAChD,8OAAC;wBAAK,WAAU;kCAAyC;;;;;;;;;;;;0BAK3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAI;4BACJ,KAAI;4BACJ,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoB;;;;;;8CAClC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAC7C,8OAAC;oCAAE,WAAU;;wCAAqB;sDACN,8OAAC;;;;;wCAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAc5C,8OAAC;gBAAI,WAAU;0BACR;oBACC;wBACE,MAAM;wBACN,KAAK;oBACP;oBACA;wBACE,MAAM;wBACN,KAAK;oBACP;oBACA;wBACE,MAAM;wBACN,KAAK;oBACP;oBACA;wBACE,MAAM;wBACN,KAAK;oBACP;oBACT;wBACW,MAAM;wBACN,KAAK;oBACP;oBACT;wBACW,MAAM;wBACN,KAAK;oBACP;oBACT;wBACW,MAAM;wBACN,KAAK;oBACP;oBACT;wBACW,MAAM;wBACN,KAAK;oBACP;oBAET;wBACW,MAAM;wBACN,KAAK;oBAEP;oBACT;wBACW,MAAM;wBACN,KAAK;oBACP;oBACT;wBACW,MAAM;wBACN,KAAK;oBACP;oBACT;wBACW,MAAM;wBACN,KAAK;oBACP;oBACT;wBACW,MAAM;wBACN,KAAK;oBAEP;oBACT;wBACW,MAAM;wBACN,KAAK;oBACP;oBAET;wBACW,MAAM;wBACN,KAAK;oBACP;iBAUD,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,8OAAC;wBAAgB,WAAU;kCACzB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK,OAAO,GAAG;oCACf,KAAK,OAAO,IAAI;oCAChB,WAAU;;;;;;8CAEZ,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyB,OAAO,IAAI;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAyB,OAAO,GAAG;;;;;;;;;;;;;;;;;;uBAT5C;;;;;;;;;;;;;;;;AAgCpB", "debugId": null}}]}