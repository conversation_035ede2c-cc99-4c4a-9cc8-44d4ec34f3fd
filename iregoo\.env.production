# Production Environment Configuration
# Copy this file to .env and update the values for production

APP_NAME="IREGO Conference"
APP_ENV=production
APP_KEY=base64:your_production_app_key_here
APP_DEBUG=false
APP_URL=https://your-api-domain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Database Configuration - Update for production
DB_CONNECTION=mysql
DB_HOST=your_database_host
DB_PORT=3306
DB_DATABASE=irego_conference_prod
DB_USERNAME=your_db_username
DB_PASSWORD=your_secure_db_password

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=public
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_redis_password
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# JWT Configuration
JWT_SECRET=your_production_jwt_secret_key_here
JWT_ALGO=HS256
JWT_TTL=60

# Frontend Configuration
FRONTEND_URL=https://your-frontend-domain.com
CORS_ALLOWED_ORIGINS="https://your-frontend-domain.com"

# Rate Limiting
THROTTLE_REQUESTS_PER_MINUTE=60

# Security
BCRYPT_ROUNDS=12

# File Storage (if using cloud storage)
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_DEFAULT_REGION=us-east-1
# AWS_BUCKET=your_s3_bucket
# AWS_USE_PATH_STYLE_ENDPOINT=false
