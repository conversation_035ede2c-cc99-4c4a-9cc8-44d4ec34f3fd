<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    public function run()
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'IREGO Conference',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Website name',
            ],
            [
                'key' => 'site_description',
                'value' => 'International Renewable Energy, Gas & Oil, and Climate Change Conference',
                'type' => 'string',
                'group' => 'general',
                'description' => 'Website description',
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'contact',
                'description' => 'Main contact email',
            ],
            [
                'key' => 'contact_phone',
                'value' => '+218-21-123-4567',
                'type' => 'string',
                'group' => 'contact',
                'description' => 'Main contact phone',
            ],
            [
                'key' => 'contact_address',
                'value' => 'Tripoli, Libya',
                'type' => 'string',
                'group' => 'contact',
                'description' => 'Contact address',
            ],
            
            // Social Media
            [
                'key' => 'facebook_url',
                'value' => '',
                'type' => 'string',
                'group' => 'social',
                'description' => 'Facebook page URL',
            ],
            [
                'key' => 'twitter_url',
                'value' => '',
                'type' => 'string',
                'group' => 'social',
                'description' => 'Twitter profile URL',
            ],
            [
                'key' => 'linkedin_url',
                'value' => '',
                'type' => 'string',
                'group' => 'social',
                'description' => 'LinkedIn profile URL',
            ],
            
            // Email Settings
            [
                'key' => 'smtp_host',
                'value' => 'smtp.gmail.com',
                'type' => 'string',
                'group' => 'email',
                'description' => 'SMTP host',
            ],
            [
                'key' => 'smtp_port',
                'value' => '587',
                'type' => 'integer',
                'group' => 'email',
                'description' => 'SMTP port',
            ],
            [
                'key' => 'smtp_encryption',
                'value' => 'tls',
                'type' => 'string',
                'group' => 'email',
                'description' => 'SMTP encryption',
            ],
            
            // Conference Settings
            [
                'key' => 'registration_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'conference',
                'description' => 'Enable conference registration',
            ],
            [
                'key' => 'paper_submission_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'conference',
                'description' => 'Enable paper submission',
            ],
            [
                'key' => 'max_registrations',
                'value' => 500,
                'type' => 'integer',
                'group' => 'conference',
                'description' => 'Maximum number of registrations',
            ],
        ];

        foreach ($settings as $settingData) {
            Setting::create($settingData);
        }
    }
}
