<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Speaker;
use App\Models\Conference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SpeakerController extends Controller
{
    public function index(Request $request)
    {
        $query = Speaker::with('conference');

        // Filter by conference
        if ($request->has('conference_id') && $request->conference_id) {
            $query->where('conference_id', $request->conference_id);
        }

        // Filter by type
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('organization', 'like', "%{$search}%")
                  ->orWhere('position', 'like', "%{$search}%");
            });
        }

        $speakers = $query->orderBy('display_order')->orderBy('name')->paginate(15);
        $conferences = Conference::active()->orderBy('title')->get();

        return view('admin.speakers.index', compact('speakers', 'conferences'));
    }

    public function create()
    {
        $conferences = Conference::active()->orderBy('title')->get();
        return view('admin.speakers.create', compact('conferences'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'name' => 'required|string|max:255',
            'title' => 'nullable|string|max:50',
            'position' => 'nullable|string|max:255',
            'organization' => 'nullable|string|max:255',
            'bio' => 'nullable|string',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:20',
            'linkedin_url' => 'nullable|url',
            'twitter_url' => 'nullable|url',
            'website_url' => 'nullable|url',
            'country' => 'nullable|string|max:100',
            'city' => 'nullable|string|max:100',
            'type' => 'required|in:keynote,invited,committee_chair,committee_member',
            'committee_type' => 'nullable|in:advisory,scientific,organizing',
            'is_featured' => 'boolean',
            'display_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Handle photo upload
        if ($request->hasFile('photo')) {
            $photoPath = $request->file('photo')->store('speakers', 'public');
            $data['photo'] = basename($photoPath);
        }

        $data['is_featured'] = $request->has('is_featured');
        $data['is_active'] = $request->has('is_active');
        $data['display_order'] = $request->display_order ?: 0;

        Speaker::create($data);

        return redirect()->route('admin.speakers.index')
            ->with('success', 'Speaker created successfully.');
    }

    public function show(Speaker $speaker)
    {
        $speaker->load(['conference', 'events']);
        return view('admin.speakers.show', compact('speaker'));
    }

    public function edit(Speaker $speaker)
    {
        $conferences = Conference::active()->orderBy('title')->get();
        return view('admin.speakers.edit', compact('speaker', 'conferences'));
    }

    public function update(Request $request, Speaker $speaker)
    {
        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'name' => 'required|string|max:255',
            'title' => 'nullable|string|max:50',
            'position' => 'nullable|string|max:255',
            'organization' => 'nullable|string|max:255',
            'bio' => 'nullable|string',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'email' => 'nullable|email',
            'phone' => 'nullable|string|max:20',
            'linkedin_url' => 'nullable|url',
            'twitter_url' => 'nullable|url',
            'website_url' => 'nullable|url',
            'country' => 'nullable|string|max:100',
            'city' => 'nullable|string|max:100',
            'type' => 'required|in:keynote,invited,committee_chair,committee_member',
            'committee_type' => 'nullable|in:advisory,scientific,organizing',
            'is_featured' => 'boolean',
            'display_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Handle photo upload
        if ($request->hasFile('photo')) {
            // Delete old photo
            if ($speaker->photo) {
                Storage::disk('public')->delete('speakers/' . $speaker->photo);
            }
            $photoPath = $request->file('photo')->store('speakers', 'public');
            $data['photo'] = basename($photoPath);
        }

        $data['is_featured'] = $request->has('is_featured');
        $data['is_active'] = $request->has('is_active');
        $data['display_order'] = $request->display_order ?: 0;

        $speaker->update($data);

        return redirect()->route('admin.speakers.index')
            ->with('success', 'Speaker updated successfully.');
    }

    public function destroy(Speaker $speaker)
    {
        // Delete photo
        if ($speaker->photo) {
            Storage::disk('public')->delete('speakers/' . $speaker->photo);
        }

        $speaker->delete();

        return redirect()->route('admin.speakers.index')
            ->with('success', 'Speaker deleted successfully.');
    }
}
