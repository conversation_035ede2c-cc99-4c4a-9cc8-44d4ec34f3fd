<?php

namespace Database\Seeders;

use App\Models\Sponsor;
use Illuminate\Database\Seeder;

class SponsorSeeder extends Seeder
{
    public function run()
    {
        $sponsors = [
            [
                'name' => 'National Oil Corporation',
                'description' => 'Official Sponsor of IREGO Conference',
                'website_url' => 'https://niclibya.com/',
                'type' => 'official',
                'is_featured' => true,
                'display_order' => 1,
            ],
            [
                'name' => 'Brega Company',
                'description' => 'Gold Sponsor',
                'type' => 'gold',
                'is_featured' => true,
                'display_order' => 2,
            ],
            [
                'name' => 'Haya Libya',
                'description' => 'Silver Sponsor',
                'type' => 'silver',
                'display_order' => 3,
            ],
            [
                'name' => 'Hokoma Company',
                'description' => 'Bronze Sponsor',
                'type' => 'bronze',
                'display_order' => 4,
            ],
            [
                'name' => 'Climate Research Center',
                'description' => 'Research Partner',
                'type' => 'partner',
                'display_order' => 5,
            ],
            [
                'name' => 'Libyan Academy',
                'description' => 'Academic Partner',
                'type' => 'partner',
                'display_order' => 6,
            ],
        ];

        foreach ($sponsors as $sponsorData) {
            Sponsor::create(array_merge([
                'conference_id' => 1,
                'is_active' => true,
            ], $sponsorData));
        }
    }
}
