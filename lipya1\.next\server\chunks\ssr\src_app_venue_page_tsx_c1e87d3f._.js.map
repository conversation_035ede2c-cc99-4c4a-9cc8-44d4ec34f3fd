{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/src/app/venue/page.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\n\r\nconst hotels = [\r\n  {\r\n    name: \"Bab Al Bahr Hotel\",\r\n    rooms: [\r\n      {\r\n        type: \"Single Room\",\r\n        prices: [\r\n          { label: \"Libyans & Arabs\", value: \"350 LYD\" },\r\n          { label: \"Foreigners\", value: \"80 USD\" },\r\n        ],\r\n      },\r\n      {\r\n        type: \"Suite\",\r\n        prices: [\r\n          { label: \"Libyans & Arabs\", value: \"500 LYD\" },\r\n          { label: \"Foreigners\", value: \"100 USD\" },\r\n        ],\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    name: \"Al Mahary Hotel\",\r\n    rooms: [\r\n      {\r\n        type: \"Single Room\",\r\n        prices: [\r\n          { label: \"Libyans & Arabs\", value: \"500 LYD\" },\r\n          { label: \"Foreigners\", value: \"100 USD\" },\r\n        ],\r\n      },\r\n      {\r\n        type: \"Suite\",\r\n        prices: [\r\n          { label: \"Libyans & Arabs\", value: \"750 LYD\" },\r\n          { label: \"Foreigners\", value: \"160 USD\" },\r\n        ],\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\nconst services = [\r\n  {\r\n    title: \"Airport Transfers\",\r\n    description: \"Shuttle service from Mitiga Airport to designated hotels.\",\r\n    icon: \"/a1.png\",\r\n  },\r\n  {\r\n    title: \"Hotel to Venue Transfers\",\r\n    description: \"Daily shuttle service from hotels to Tripoli International Fair.\",\r\n    icon: \"/a2.png\",\r\n  },\r\n  {\r\n    title: \"Coordination\",\r\n    description: \"Coordinated by our dedicated team and international marketing offices.\",\r\n    icon: \"/a3.png\",\r\n  },\r\n  {\r\n    title: \"Flight Ticket Assistance\",\r\n    description: \"Streamlined booking process and special conference rates.\",\r\n    icon: \"/a4.png\",\r\n  },\r\n  {\r\n    title: \"VIP Transfers\",\r\n    description: \"Premium transportation for key individuals.\",\r\n    icon: \"/a5.png\",\r\n  },\r\n  {\r\n    title: \"Visa & Travel Support\",\r\n    description: \"Assistance via international offices.\",\r\n    icon: \"/a6.png\",\r\n  },\r\n];\r\n\r\nconst Page: React.FC = () => {\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {/* Header Image Section */}\r\n      <div className=\"relative h-[300px] md:h-[400px] w-full\">\r\n        <img\r\n          src=\"/photo1.png\"\r\n          alt=\"About IREGO\"\r\n          className=\"object-cover w-full h-full\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n          <h1 className=\"text-3xl md:text-5xl font-bold text-white animate-fade-in-up\">\r\n            VEN<span className=\"text-orange-500\">UE</span>\r\n          </h1>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Venue Details & Map */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 px-6 py-12\">\r\n        <div className=\"space-y-4\">\r\n          <h2 className=\"text-2xl font-bold text-gray-800\">IREGO 2025 Venue</h2>\r\n          <p className=\"text-orange-600 hover:text-orange-700 transition-colors duration-300 cursor-pointer select-text\">\r\n            <EMAIL>\r\n          </p>\r\n          <p className=\"text-orange-600 hover:text-orange-700 transition-colors duration-300 select-text\">\r\n            +218 123 456 789\r\n          </p>\r\n          <p className=\"text-gray-700\">Tripoli, Libya</p>\r\n        </div>\r\n\r\n        <div>\r\n          <iframe\r\n            title=\"Tripoli, Libya Map\"\r\n            className=\"w-full h-64 md:h-80 rounded-lg shadow-md\"\r\n            src=\"https://www.openstreetmap.org/export/embed.html?bbox=13.1609%2C32.8708%2C13.2023%2C32.8897&layer=mapnik&marker=32.8795,13.1816\"\r\n            loading=\"lazy\"\r\n          ></iframe>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hotels Section */}\r\n      <div className=\"px-6 pb-12\">\r\n        <div className=\"flex flex-col md:flex-row flex-wrap gap-8 justify-center\">\r\n          {hotels.map((hotel) => (\r\n            <div\r\n              key={hotel.name}\r\n              className=\"w-full md:w-[300px] bg-white border border-gray-200 rounded-2xl p-6 shadow-sm relative transition-transform hover:scale-[1.02]\"\r\n            >\r\n              <h2 className=\"font-bold text-lg mb-4 text-gray-800\">{hotel.name}</h2>\r\n\r\n              {hotel.rooms.map((room) => (\r\n                <div key={room.type} className=\"mb-4\">\r\n                  <h3 className=\"font-semibold text-sm text-gray-700 mb-2\">{room.type}</h3>\r\n                  {room.prices.map((price) => (\r\n                    <div key={price.label} className=\"flex justify-between text-sm py-1\">\r\n                      <span className=\"text-gray-500\">{price.label}</span>\r\n                      <span className=\"font-medium text-gray-800\">{price.value}</span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              ))}\r\n\r\n              <div className=\"absolute top-6 right-6 w-6 h-6 rounded-md bg-orange-500 flex items-center justify-center cursor-pointer\" title=\"Contact Hotel\">\r\n                <div className=\"w-3 h-[2px] bg-white\" />\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        <p className=\"text-center text-sm mt-8 font-semibold text-gray-600\">\r\n          Note: For reservations, attendees should contact the hotels directly.\r\n        </p>\r\n      </div>\r\n\r\n      {/* Transportation Section */}\r\n      <section className=\"bg-white px-6 pb-16\">\r\n        <h2 className=\"text-2xl font-bold mb-6 text-gray-900\">Transportation & Transfers</h2>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\">\r\n          {services.slice(0, 3).map((service) => (\r\n            <div\r\n              key={service.title}\r\n              className=\"bg-orange-50 border border-gray-200 rounded-xl p-4 shadow-sm flex gap-4 items-start\"\r\n            >\r\n              <div className=\"w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center overflow-hidden\">\r\n                <img src={service.icon} alt={`${service.title} icon`} className=\"w-6 h-6 object-contain\" />\r\n              </div>\r\n              <div>\r\n                <h3 className=\"font-semibold text-sm text-gray-800\">{service.title}</h3>\r\n                <p className=\"text-sm text-gray-600 mt-1\">{service.description}</p>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        <h2 className=\"text-2xl font-bold mb-6 text-gray-900\">Official Airline Partner</h2>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n          {services.slice(3).map((service) => (\r\n            <div\r\n              key={service.title}\r\n              className=\"bg-orange-50 border border-gray-200 rounded-xl p-4 shadow-sm flex gap-4 items-start\"\r\n            >\r\n              <div className=\"w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center overflow-hidden\">\r\n                <img src={service.icon} alt={`${service.title} icon`} className=\"w-6 h-6 object-contain\" />\r\n              </div>\r\n              <div>\r\n                <h3 className=\"font-semibold text-sm text-gray-800\">{service.title}</h3>\r\n                <p className=\"text-sm text-gray-600 mt-1\">{service.description}</p>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Page;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM,SAAS;IACb;QACE,MAAM;QACN,OAAO;YACL;gBACE,MAAM;gBACN,QAAQ;oBACN;wBAAE,OAAO;wBAAmB,OAAO;oBAAU;oBAC7C;wBAAE,OAAO;wBAAc,OAAO;oBAAS;iBACxC;YACH;YACA;gBACE,MAAM;gBACN,QAAQ;oBACN;wBAAE,OAAO;wBAAmB,OAAO;oBAAU;oBAC7C;wBAAE,OAAO;wBAAc,OAAO;oBAAU;iBACzC;YACH;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;YACL;gBACE,MAAM;gBACN,QAAQ;oBACN;wBAAE,OAAO;wBAAmB,OAAO;oBAAU;oBAC7C;wBAAE,OAAO;wBAAc,OAAO;oBAAU;iBACzC;YACH;YACA;gBACE,MAAM;gBACN,QAAQ;oBACN;wBAAE,OAAO;wBAAmB,OAAO;oBAAU;oBAC7C;wBAAE,OAAO;wBAAc,OAAO;oBAAU;iBACzC;YACH;SACD;IACH;CACD;AAED,MAAM,WAAW;IACf;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAED,MAAM,OAAiB;IACrB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAA+D;8CACxE,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAkG;;;;;;0CAG/G,8OAAC;gCAAE,WAAU;0CAAmF;;;;;;0CAGhG,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAG/B,8OAAC;kCACC,cAAA,8OAAC;4BACC,OAAM;4BACN,WAAU;4BACV,KAAI;4BACJ,SAAQ;;;;;;;;;;;;;;;;;0BAMd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAG,WAAU;kDAAwC,MAAM,IAAI;;;;;;oCAE/D,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAG,WAAU;8DAA4C,KAAK,IAAI;;;;;;gDAClE,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,sBAChB,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC;gEAAK,WAAU;0EAAiB,MAAM,KAAK;;;;;;0EAC5C,8OAAC;gEAAK,WAAU;0EAA6B,MAAM,KAAK;;;;;;;uDAFhD,MAAM,KAAK;;;;;;2CAHf,KAAK,IAAI;;;;;kDAWrB,8OAAC;wCAAI,WAAU;wCAA0G,OAAM;kDAC7H,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;;+BAlBZ,MAAM,IAAI;;;;;;;;;;kCAwBrB,8OAAC;wBAAE,WAAU;kCAAuD;;;;;;;;;;;;0BAMtE,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAI,WAAU;kCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACzB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,KAAK,QAAQ,IAAI;4CAAE,KAAK,GAAG,QAAQ,KAAK,CAAC,KAAK,CAAC;4CAAE,WAAU;;;;;;;;;;;kDAElE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAuC,QAAQ,KAAK;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAA8B,QAAQ,WAAW;;;;;;;;;;;;;+BAR3D,QAAQ,KAAK;;;;;;;;;;kCAcxB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAI,WAAU;kCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,wBACtB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,KAAK,QAAQ,IAAI;4CAAE,KAAK,GAAG,QAAQ,KAAK,CAAC,KAAK,CAAC;4CAAE,WAAU;;;;;;;;;;;kDAElE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAuC,QAAQ,KAAK;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAA8B,QAAQ,WAAW;;;;;;;;;;;;;+BAR3D,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;AAgBhC;uCAEe", "debugId": null}}]}