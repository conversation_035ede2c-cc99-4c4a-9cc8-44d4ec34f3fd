# IREGO Conference API Documentation

## Base URL
```
Production: https://your-domain.com/api/v1
Development: http://localhost:8000/api/v1
```

## Authentication
يستخدم النظام JWT (JSON Web Tokens) للمصادقة.

### Headers المطلوبة
```http
Content-Type: application/json
Authorization: Bearer {token}
```

## 🔐 Authentication Endpoints

### تسجيل الدخول
```http
POST /auth/login
```

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "admin123"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "bearer",
        "expires_in": 3600,
        "user": {
            "id": 1,
            "name": "Super Ad<PERSON>",
            "email": "<EMAIL>",
            "role": "super_admin"
        }
    }
}
```

### تسجيل الخروج
```http
POST /auth/logout
Authorization: Bearer {token}
```

### معلومات المستخدم الحالي
```http
GET /auth/me
Authorization: Bearer {token}
```

### تجديد الرمز المميز
```http
POST /auth/refresh
Authorization: Bearer {token}
```

## 🎯 Conference Endpoints

### قائمة المؤتمرات
```http
GET /conferences
```

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "title": "International Renewable Energy, Gas & Oil, and Climate Change Conference",
            "subtitle": "IREGO Conference 2025",
            "description": "Conference description...",
            "start_date": "2025-11-25T09:00:00.000000Z",
            "end_date": "2025-11-27T17:00:00.000000Z",
            "location": "Tripoli, Libya",
            "venue": "Conference Center Tripoli",
            "logo_url": "https://domain.com/storage/conferences/logo.jpg",
            "banner_image_url": "https://domain.com/storage/conferences/banner.jpg",
            "registration_fee_local": 200.00,
            "registration_fee_international": 200.00,
            "currency_local": "LYD",
            "currency_international": "EUR",
            "featured_speakers": [...]
        }
    ]
}
```

### تفاصيل مؤتمر محدد
```http
GET /conferences/{id}
```

### المؤتمر الحالي
```http
GET /conferences/current
```

## 👥 Speaker Endpoints

### قائمة المتحدثين
```http
GET /speakers
```

**Query Parameters:**
- `conference_id` - تصفية حسب المؤتمر
- `type` - نوع المتحدث (keynote, invited, committee_chair, committee_member)
- `committee_type` - نوع اللجنة (advisory, scientific, organizing)
- `featured` - المتحدثين المميزين (true/false)

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "Prof. Ibrahim Rahoma",
            "position": "Conference Chair",
            "organization": "College of Engineering Technology Janzour",
            "bio": "Professor Ibrahim Rahoma serves as...",
            "photo_url": "https://domain.com/storage/speakers/photo.jpg",
            "country": "Libya",
            "city": "Tripoli",
            "type": "committee_chair",
            "committee_type": "organizing",
            "is_featured": true
        }
    ]
}
```

### تفاصيل متحدث محدد
```http
GET /speakers/{id}
```

### اللجان
```http
GET /speakers/committees/all
```

### المتحدثين الرئيسيين
```http
GET /speakers/keynotes/all
```

## 📝 Registration Endpoints

### تسجيل جديد
```http
POST /registrations
```

**Request Body:**
```json
{
    "conference_id": 1,
    "first_name": "أحمد",
    "last_name": "محمد",
    "email": "<EMAIL>",
    "phone": "+218912345678",
    "country": "Libya",
    "city": "Tripoli",
    "organization": "جامعة طرابلس",
    "position": "أستاذ مساعد",
    "participant_type": "local",
    "registration_type": "presenter",
    "dietary_requirements": "نباتي",
    "special_needs": "كرسي متحرك"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Registration submitted successfully",
    "data": {
        "id": 1,
        "confirmation_code": "IREGO-A1B2C3D4",
        "full_name": "أحمد محمد",
        "email": "<EMAIL>",
        "participant_type": "local",
        "registration_type": "presenter",
        "registration_fee": 200.00,
        "currency": "LYD",
        "payment_status": "pending",
        "status": "Pending Confirmation"
    }
}
```

### تفاصيل التسجيل
```http
GET /registrations/{confirmation_code}
```

### فحص حالة التسجيل
```http
POST /registrations/check-status
```

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "confirmation_code": "IREGO-A1B2C3D4"
}
```

## 📰 News Endpoints

### قائمة الأخبار
```http
GET /news
```

**Query Parameters:**
- `conference_id` - تصفية حسب المؤتمر
- `featured` - الأخبار المميزة (true/false)
- `per_page` - عدد العناصر في الصفحة (افتراضي: 10)

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "title": "IREGO Conference 2025 Call for Papers Now Open",
            "slug": "irego-conference-2025-call-for-papers-now-open",
            "excerpt": "We are pleased to invite scholars...",
            "featured_image_url": "https://domain.com/storage/news/image.jpg",
            "author_name": "Conference Committee",
            "published_at": "2024-01-15T10:00:00.000000Z",
            "reading_time": "3 min read",
            "is_featured": true
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 5,
        "per_page": 10,
        "total": 50
    }
}
```

### تفاصيل خبر محدد
```http
GET /news/{slug}
```

### الأخبار المميزة
```http
GET /news/featured?limit=5
```

### الأخبار الحديثة
```http
GET /news/recent?limit=5
```

## 🤝 Sponsor Endpoints

### قائمة الشركاء
```http
GET /sponsors
```

**Query Parameters:**
- `conference_id` - تصفية حسب المؤتمر
- `type` - نوع الشريك (official, gold, silver, bronze, partner, media)
- `featured` - الشركاء المميزين (true/false)
- `group_by_type` - تجميع حسب النوع (true/false)

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "National Oil Corporation",
            "description": "Official Sponsor of IREGO Conference",
            "logo_url": "https://domain.com/storage/sponsors/logo.jpg",
            "website_url": "https://niclibya.com/",
            "type": "official",
            "type_display": "Official Sponsor",
            "is_featured": true
        }
    ]
}
```

### تفاصيل شريك محدد
```http
GET /sponsors/{id}
```

## 📅 Event Endpoints

### قائمة الفعاليات
```http
GET /events
```

**Query Parameters:**
- `conference_id` - تصفية حسب المؤتمر
- `type` - نوع الفعالية (session, keynote, workshop, break, lunch, registration)
- `date` - تصفية حسب التاريخ (YYYY-MM-DD)
- `upcoming` - الفعاليات القادمة (true/false)

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "title": "Opening Ceremony",
            "description": "Official opening ceremony with welcome speeches",
            "type": "keynote",
            "start_time": "2025-11-25T09:00:00.000000Z",
            "end_time": "2025-11-25T10:00:00.000000Z",
            "location": "Main Auditorium",
            "room": "Hall A",
            "duration": "1 hour",
            "time_range": "09:00 - 10:00",
            "speakers": [
                {
                    "id": 1,
                    "name": "Prof. Ibrahim Rahoma",
                    "position": "Conference Chair",
                    "photo_url": "https://domain.com/storage/speakers/photo.jpg"
                }
            ]
        }
    ]
}
```

### تفاصيل فعالية محددة
```http
GET /events/{id}
```

### جدول الفعاليات
```http
GET /events/schedule/all
```

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "date": "2025-11-25",
            "date_formatted": "Monday, November 25, 2025",
            "events": [...]
        }
    ]
}
```

## 🔧 Admin API Endpoints

### إحصائيات Dashboard
```http
GET /admin/dashboard/stats
Authorization: Bearer {admin_token}
```

### إدارة المؤتمرات
```http
GET /admin/conferences
POST /admin/conferences
GET /admin/conferences/{id}
PUT /admin/conferences/{id}
DELETE /admin/conferences/{id}
```

### إدارة المتحدثين
```http
GET /admin/speakers
POST /admin/speakers
PUT /admin/speakers/{id}
DELETE /admin/speakers/{id}
```

### إدارة التسجيلات
```http
GET /admin/registrations
POST /admin/registrations/{id}/confirm
POST /admin/registrations/{id}/check-in
GET /admin/registrations/export/excel
GET /admin/registrations/export/csv
```

### رفع الملفات
```http
POST /admin/upload/image
POST /admin/upload/file
DELETE /admin/upload/{filename}
```

**Request (multipart/form-data):**
```
image: file
folder: conferences|speakers|sponsors|news|events
resize: true|false
width: 800
height: 600
```

## 📊 Response Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 422 | Validation Error |
| 500 | Server Error |

## 🔍 Error Response Format

```json
{
    "success": false,
    "message": "Validation error",
    "errors": {
        "email": ["The email field is required."],
        "password": ["The password field is required."]
    }
}
```

## 📝 Rate Limiting

- **API Calls**: 60 requests per minute per IP
- **Authentication**: 5 attempts per minute per IP
- **File Upload**: 10 uploads per minute per user

## 🔧 Testing Examples

### cURL Examples

```bash
# تسجيل الدخول
curl -X POST https://your-domain.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# الحصول على المؤتمرات
curl -X GET https://your-domain.com/api/v1/conferences

# تسجيل مشارك جديد
curl -X POST https://your-domain.com/api/v1/registrations \
  -H "Content-Type: application/json" \
  -d '{
    "conference_id": 1,
    "first_name": "أحمد",
    "last_name": "محمد",
    "email": "<EMAIL>",
    "country": "Libya",
    "participant_type": "local",
    "registration_type": "attendee"
  }'
```

### JavaScript Examples

```javascript
// تسجيل الدخول
const login = async () => {
    const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            email: '<EMAIL>',
            password: 'admin123'
        })
    });
    
    const data = await response.json();
    localStorage.setItem('token', data.data.token);
};

// الحصول على المؤتمرات
const getConferences = async () => {
    const response = await fetch('/api/v1/conferences', {
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
    });
    
    return await response.json();
};
```

## 📱 SDK Integration

### PHP SDK Example
```php
use GuzzleHttp\Client;

class IregoAPI {
    private $client;
    private $token;
    
    public function __construct($baseUrl) {
        $this->client = new Client(['base_uri' => $baseUrl]);
    }
    
    public function login($email, $password) {
        $response = $this->client->post('/auth/login', [
            'json' => ['email' => $email, 'password' => $password]
        ]);
        
        $data = json_decode($response->getBody(), true);
        $this->token = $data['data']['token'];
        
        return $data;
    }
    
    public function getConferences() {
        $response = $this->client->get('/conferences', [
            'headers' => ['Authorization' => 'Bearer ' . $this->token]
        ]);
        
        return json_decode($response->getBody(), true);
    }
}
```

هذا التوثيق الشامل يغطي جميع endpoints المتاحة في النظام مع أمثلة عملية للاستخدام! 🚀
