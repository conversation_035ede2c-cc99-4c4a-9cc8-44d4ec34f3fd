# IREGO Conference Management System

A complete conference management system with Node.js backend and Next.js frontend.

## 🚀 Quick Start

### Prerequisites
- Node.js 18 or higher
- npm or yarn

### Automatic Setup
Run the setup script to automatically configure both projects:

```powershell
# Windows PowerShell
.\setup.ps1
```

### Manual Setup

#### Backend (Node.js)
```bash
cd backend
npm install
```

#### Frontend (Next.js)
```bash
cd lipya1
npm install
```

### Running the Application

#### Automatic Start
```powershell
# Windows PowerShell
.\start-dev.ps1
```

#### Manual Start
```bash
# Terminal 1 - Node.js Backend
cd backend
node server.js

# Terminal 2 - Next.js Frontend
cd lipya1
npm run dev
```

## 📍 Access Points

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8001
- **API Health Check**: http://localhost:8001/api/v1/health

## 🔧 Configuration

### Environment Variables

#### Laravel (.env)
```env
APP_NAME="IREGO Conference"
APP_URL=http://localhost:8000
DB_DATABASE=irego_conference
JWT_SECRET=your_jwt_secret
FRONTEND_URL=http://localhost:3000
```

#### Next.js (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=IREGO Conference
```

## 🔐 Authentication

The system uses JWT authentication:
- Login: `POST /api/v1/auth/login`
- Register: `POST /api/v1/auth/register`
- Logout: `POST /api/v1/auth/logout`
- Refresh: `POST /api/v1/auth/refresh`

## 📚 API Endpoints

### Public Endpoints
- `GET /api/v1/conferences` - List conferences
- `GET /api/v1/speakers` - List speakers
- `GET /api/v1/news` - List news
- `GET /api/v1/events` - List events
- `POST /api/v1/registrations` - Submit registration

### Protected Endpoints
- `GET /api/v1/auth/me` - Get current user
- `POST /api/v1/auth/logout` - Logout user

### Admin Endpoints (require admin role)
- `GET /api/v1/admin/dashboard/stats` - Dashboard statistics
- CRUD operations for conferences, speakers, news, events

## 🛡️ Security Features

- JWT Authentication
- CORS protection
- Rate limiting
- Security headers
- Input validation
- SQL injection protection
- XSS protection

## 🏗️ Project Structure

```
├── iregoo/                 # Laravel Backend
│   ├── app/
│   │   ├── Http/
│   │   │   ├── Controllers/
│   │   │   └── Middleware/
│   │   └── Models/
│   ├── config/
│   ├── database/
│   └── routes/
├── lipya1/                 # Next.js Frontend
│   ├── src/
│   │   ├── app/           # App Router pages
│   │   ├── components/    # React components
│   │   ├── context/       # React context
│   │   ├── hooks/         # Custom hooks
│   │   └── services/      # API services
│   └── public/
├── setup.ps1             # Setup script
├── start-dev.ps1         # Development start script
└── README.md
```

## 🔄 Development Workflow

1. **Setup**: Run `.\setup.ps1` once
2. **Development**: Run `.\start-dev.ps1` daily
3. **Testing**: Access http://localhost:3000
4. **API Testing**: Use http://localhost:8000/api/v1

## 📱 Features

### Frontend Features
- Responsive design
- User authentication
- Conference registration
- Speaker profiles
- News and updates
- Event scheduling
- Contact forms

### Backend Features
- RESTful API
- JWT authentication
- Admin panel
- File uploads
- Email notifications
- Database migrations
- API documentation

## 🚀 Production Deployment

### Backend Deployment
1. Configure production database
2. Set `APP_ENV=production`
3. Run `php artisan optimize`
4. Configure web server (Apache/Nginx)

### Frontend Deployment
1. Update API URLs in `.env.local`
2. Run `npm run build`
3. Deploy to hosting service (Vercel, Netlify, etc.)

## 🔧 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill process on port 8000
   netstat -ano | findstr :8000
   taskkill /PID <PID> /F
   ```

2. **Database connection error**
   - Check MySQL service is running
   - Verify database credentials in `.env`

3. **CORS errors**
   - Ensure frontend URL is in `CORS_ALLOWED_ORIGINS`
   - Check `config/cors.php` settings

4. **JWT errors**
   - Run `php artisan jwt:secret`
   - Clear config cache: `php artisan config:clear`

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review Laravel and Next.js documentation
- Contact the development team

## 📄 License

This project is licensed under the MIT License.
