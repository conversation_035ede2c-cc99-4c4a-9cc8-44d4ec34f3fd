# دليل التثبيت السريع - نظام إدارة مؤتمرات IREGO

## متطلبات النظام
- PHP 8.1+
- MySQL 5.7+
- Composer
- Apache/Nginx

## خطوات التثبيت

### 1. تحضير البيئة
```bash
# إنشاء قاعدة البيانات
CREATE DATABASE irego_conference CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. تثبيت التبعيات
```bash
# تثبيت Composer إذا لم يكن مثبتاً
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer

# تثبيت تبعيات Laravel
composer install
```

### 3. إعداد الملفات
```bash
# نسخ ملف البيئة
cp .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate

# توليد مفتاح JWT
php artisan jwt:secret
```

### 4. تكوين قاعدة البيانات
قم بتحرير ملف `.env`:
```env
APP_NAME="IREGO Conference"
APP_ENV=production
APP_DEBUG=false
APP_URL=http://your-domain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=irego_conference
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

JWT_SECRET=your_jwt_secret_here
```

### 5. تشغيل الهجرات
```bash
# تشغيل الهجرات وإدخال البيانات الأولية
php artisan migrate --seed
```

### 6. إعداد الصلاحيات
```bash
# إعداد صلاحيات المجلدات
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache public/uploads

# إنشاء رابط التخزين
php artisan storage:link
```

### 7. تحسين الأداء (للإنتاج)
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## إعداد الخادم

### Apache Virtual Host
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/irego-conference/public
    
    <Directory /var/www/irego-conference/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/irego_error.log
    CustomLog ${APACHE_LOG_DIR}/irego_access.log combined
</VirtualHost>
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/irego-conference/public;
    
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\.ht {
        deny all;
    }
}
```

## بيانات الدخول الافتراضية

### Super Admin
- الرابط: `http://your-domain.com/admin/login`
- البريد: `<EMAIL>`
- كلمة المرور: `admin123`

### Admin
- البريد: `<EMAIL>`
- كلمة المرور: `conference123`

## اختبار التثبيت

### 1. اختبار لوحة التحكم
```bash
# زيارة الرابط
http://your-domain.com/admin
```

### 2. اختبار API
```bash
# اختبار تسجيل الدخول
curl -X POST http://your-domain.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# اختبار الحصول على المؤتمرات
curl -X GET http://your-domain.com/api/v1/conferences
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ 500 - Internal Server Error
```bash
# فحص سجلات الأخطاء
tail -f storage/logs/laravel.log

# التأكد من الصلاحيات
chmod -R 755 storage bootstrap/cache
```

#### خطأ قاعدة البيانات
```bash
# التأكد من إعدادات قاعدة البيانات في .env
# اختبار الاتصال
php artisan tinker
>>> DB::connection()->getPdo();
```

#### مشاكل JWT
```bash
# إعادة توليد مفتاح JWT
php artisan jwt:secret --force
```

#### مشاكل الصور
```bash
# التأكد من رابط التخزين
php artisan storage:link

# إنشاء مجلدات الرفع
mkdir -p public/uploads/{conferences,speakers,sponsors,news,events}
chmod -R 755 public/uploads
```

## الصيانة

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p irego_conference > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf files_backup_$(date +%Y%m%d).tar.gz public/uploads storage
```

### التحديث
```bash
# تحديث التبعيات
composer update

# تشغيل الهجرات الجديدة
php artisan migrate

# مسح الكاش
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

## الأمان

### إعدادات الأمان الموصى بها
```bash
# إخفاء معلومات الخادم
# في Apache: ServerTokens Prod
# في Nginx: server_tokens off;

# تفعيل HTTPS
# استخدام Let's Encrypt أو شهادة SSL

# تحديث كلمات المرور الافتراضية
# تغيير كلمات مرور الأدمن فوراً بعد التثبيت
```

### مراقبة الأمان
```bash
# مراقبة سجلات الدخول
tail -f storage/logs/laravel.log | grep "login"

# مراقبة محاولات الدخول الفاشلة
grep "Invalid credentials" storage/logs/laravel.log
```

## الدعم

للحصول على المساعدة:
1. راجع ملف README.md
2. فحص سجلات الأخطاء في `storage/logs/`
3. التواصل مع فريق الدعم

---

**ملاحظة مهمة**: تأكد من تغيير كلمات المرور الافتراضية وإعدادات الأمان قبل النشر في بيئة الإنتاج.
