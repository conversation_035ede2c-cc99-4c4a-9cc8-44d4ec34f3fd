<?php

namespace App\Exports;

use App\Models\Registration;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class RegistrationsExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    protected $conferenceId;

    public function __construct($conferenceId = null)
    {
        $this->conferenceId = $conferenceId;
    }

    public function collection()
    {
        $query = Registration::with('conference');

        if ($this->conferenceId) {
            $query->where('conference_id', $this->conferenceId);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    public function headings(): array
    {
        return [
            'ID',
            'Conference',
            'First Name',
            'Last Name',
            'Email',
            'Phone',
            'Country',
            'City',
            'Organization',
            'Position',
            'Participant Type',
            'Registration Type',
            'Payment Status',
            'Payment Method',
            'Payment Reference',
            'Registration Fee',
            'Currency',
            'Confirmation Code',
            'Is Confirmed',
            'Confirmed At',
            'Checked In',
            'Checked In At',
            'Dietary Requirements',
            'Special Needs',
            'Notes',
            'Created At',
        ];
    }

    public function map($registration): array
    {
        return [
            $registration->id,
            $registration->conference->title,
            $registration->first_name,
            $registration->last_name,
            $registration->email,
            $registration->phone,
            $registration->country,
            $registration->city,
            $registration->organization,
            $registration->position,
            ucfirst($registration->participant_type),
            ucfirst($registration->registration_type),
            ucfirst($registration->payment_status),
            $registration->payment_method,
            $registration->payment_reference,
            $registration->registration_fee,
            $registration->currency,
            $registration->confirmation_code,
            $registration->is_confirmed ? 'Yes' : 'No',
            $registration->confirmed_at ? $registration->confirmed_at->format('Y-m-d H:i:s') : '',
            $registration->checked_in ? 'Yes' : 'No',
            $registration->checked_in_at ? $registration->checked_in_at->format('Y-m-d H:i:s') : '',
            $registration->dietary_requirements,
            $registration->special_needs,
            $registration->notes,
            $registration->created_at->format('Y-m-d H:i:s'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => ['font' => ['bold' => true]],
        ];
    }
}
