<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Conference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class NewsController extends Controller
{
    public function index(Request $request)
    {
        $query = News::with('conference');

        // Filter by conference
        if ($request->has('conference_id') && $request->conference_id) {
            $query->where('conference_id', $request->conference_id);
        }

        // Filter by published status
        if ($request->has('is_published') && $request->is_published !== '') {
            $query->where('is_published', $request->is_published);
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('author_name', 'like', "%{$search}%");
            });
        }

        $news = $query->orderBy('created_at', 'desc')->paginate(15);
        $conferences = Conference::active()->orderBy('title')->get();

        return view('admin.news.index', compact('news', 'conferences'));
    }

    public function create()
    {
        $conferences = Conference::active()->orderBy('title')->get();
        return view('admin.news.create', compact('conferences'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news,slug',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'author_name' => 'nullable|string|max:255',
            'published_at' => 'nullable|date',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        } else {
            $data['slug'] = Str::slug($data['slug']);
        }

        // Ensure slug is unique
        $originalSlug = $data['slug'];
        $counter = 1;
        while (News::where('slug', $data['slug'])->exists()) {
            $data['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $imagePath = $request->file('featured_image')->store('news', 'public');
            $data['featured_image'] = basename($imagePath);
        }

        $data['is_featured'] = $request->has('is_featured');
        $data['is_published'] = $request->has('is_published');

        // Set published_at if publishing
        if ($data['is_published'] && !$data['published_at']) {
            $data['published_at'] = now();
        }

        // Set author name if not provided
        if (empty($data['author_name'])) {
            $data['author_name'] = auth()->user()->name;
        }

        News::create($data);

        return redirect()->route('admin.news.index')
            ->with('success', 'News article created successfully.');
    }

    public function show(News $news)
    {
        $news->load('conference');
        return view('admin.news.show', compact('news'));
    }

    public function edit(News $news)
    {
        $conferences = Conference::active()->orderBy('title')->get();
        return view('admin.news.edit', compact('news', 'conferences'));
    }

    public function update(Request $request, News $news)
    {
        $validator = Validator::make($request->all(), [
            'conference_id' => 'required|exists:conferences,id',
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news,slug,' . $news->id,
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'author_name' => 'nullable|string|max:255',
            'published_at' => 'nullable|date',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->all();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        } else {
            $data['slug'] = Str::slug($data['slug']);
        }

        // Ensure slug is unique (excluding current record)
        $originalSlug = $data['slug'];
        $counter = 1;
        while (News::where('slug', $data['slug'])->where('id', '!=', $news->id)->exists()) {
            $data['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($news->featured_image) {
                Storage::disk('public')->delete('news/' . $news->featured_image);
            }
            $imagePath = $request->file('featured_image')->store('news', 'public');
            $data['featured_image'] = basename($imagePath);
        }

        $data['is_featured'] = $request->has('is_featured');
        $data['is_published'] = $request->has('is_published');

        // Set published_at if publishing for the first time
        if ($data['is_published'] && !$news->published_at && !$data['published_at']) {
            $data['published_at'] = now();
        }

        $news->update($data);

        return redirect()->route('admin.news.index')
            ->with('success', 'News article updated successfully.');
    }

    public function destroy(News $news)
    {
        // Delete featured image
        if ($news->featured_image) {
            Storage::disk('public')->delete('news/' . $news->featured_image);
        }

        $news->delete();

        return redirect()->route('admin.news.index')
            ->with('success', 'News article deleted successfully.');
    }

    public function publish(News $news)
    {
        $news->publish();

        return back()->with('success', 'News article published successfully.');
    }

    public function unpublish(News $news)
    {
        $news->unpublish();

        return back()->with('success', 'News article unpublished successfully.');
    }
}
