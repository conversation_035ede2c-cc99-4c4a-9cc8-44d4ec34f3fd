# PowerShell script to setup the IREGO Conference project

Write-Host "🔧 Setting up IREGO Conference Project..." -ForegroundColor Green

# Check if required tools are installed
Write-Host "🔍 Checking required tools..." -ForegroundColor Yellow

# Check PHP
try {
    $phpVersion = php -v 2>$null
    if ($phpVersion) {
        Write-Host "✅ PHP is installed" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ PHP is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check Composer
try {
    $composerVersion = composer --version 2>$null
    if ($composerVersion) {
        Write-Host "✅ Composer is installed" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Composer is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check Node.js
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "✅ Node.js is installed" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check npm
try {
    $npmVersion = npm --version 2>$null
    if ($npmVersion) {
        Write-Host "✅ npm is installed" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ npm is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🔧 Setting up Laravel Backend..." -ForegroundColor Cyan

# Navigate to Laravel directory and install dependencies
Set-Location "iregoo"

Write-Host "📦 Installing PHP dependencies..." -ForegroundColor Yellow
composer install --no-dev --optimize-autoloader

Write-Host "🔑 Generating application key..." -ForegroundColor Yellow
php artisan key:generate

Write-Host "🔐 Generating JWT secret..." -ForegroundColor Yellow
php artisan jwt:secret

Write-Host "📊 Running database migrations..." -ForegroundColor Yellow
php artisan migrate --force

Write-Host "🌱 Seeding database..." -ForegroundColor Yellow
php artisan db:seed --force

Write-Host "🔗 Creating storage link..." -ForegroundColor Yellow
php artisan storage:link

Write-Host "🧹 Clearing caches..." -ForegroundColor Yellow
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

Write-Host "⚡ Optimizing for production..." -ForegroundColor Yellow
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Navigate back to root
Set-Location ".."

Write-Host ""
Write-Host "⚡ Setting up Next.js Frontend..." -ForegroundColor Cyan

# Navigate to Next.js directory and install dependencies
Set-Location "lipya1"

Write-Host "📦 Installing Node.js dependencies..." -ForegroundColor Yellow
npm install

Write-Host "🏗️ Building Next.js application..." -ForegroundColor Yellow
npm run build

# Navigate back to root
Set-Location ".."

Write-Host ""
Write-Host "🎉 Setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Configure your database settings in iregoo/.env" -ForegroundColor White
Write-Host "   2. Update API URLs in lipya1/.env.local if needed" -ForegroundColor White
Write-Host "   3. Run './start-dev.ps1' to start the development environment" -ForegroundColor White
Write-Host ""
Write-Host "📝 Important files:" -ForegroundColor Cyan
Write-Host "   🔧 Laravel config: iregoo/.env" -ForegroundColor White
Write-Host "   🔧 Next.js config: lipya1/.env.local" -ForegroundColor White
Write-Host "   🚀 Start script: start-dev.ps1" -ForegroundColor White
Write-Host ""

Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
