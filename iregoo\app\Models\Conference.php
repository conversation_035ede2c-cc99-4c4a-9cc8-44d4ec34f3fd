<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Conference extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'subtitle',
        'description',
        'start_date',
        'end_date',
        'location',
        'venue',
        'logo',
        'banner_image',
        'website_url',
        'email',
        'phone',
        'registration_fee_local',
        'registration_fee_international',
        'currency_local',
        'currency_international',
        'abstract_deadline',
        'notification_date',
        'final_paper_deadline',
        'is_active',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'abstract_deadline' => 'datetime',
        'notification_date' => 'datetime',
        'final_paper_deadline' => 'datetime',
        'is_active' => 'boolean',
        'registration_fee_local' => 'decimal:2',
        'registration_fee_international' => 'decimal:2',
    ];

    // Relationships
    public function speakers()
    {
        return $this->hasMany(Speaker::class);
    }

    public function registrations()
    {
        return $this->hasMany(Registration::class);
    }

    public function sponsors()
    {
        return $this->hasMany(Sponsor::class);
    }

    public function events()
    {
        return $this->hasMany(Event::class);
    }

    public function news()
    {
        return $this->hasMany(News::class);
    }

    // Accessors
    public function getLogoUrlAttribute()
    {
        return $this->logo ? asset('uploads/conferences/' . $this->logo) : null;
    }

    public function getBannerImageUrlAttribute()
    {
        return $this->banner_image ? asset('uploads/conferences/' . $this->banner_image) : null;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCurrent($query)
    {
        return $query->where('start_date', '>=', now())
                    ->where('end_date', '<=', now()->addYear())
                    ->orderBy('start_date');
    }
}
