// API Service for Laravel Backend Integration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: any;
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
}

interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at?: string;
  created_at: string;
  updated_at: string;
}

interface AuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

class ApiService {
  private getAuthHeaders(): HeadersInit {
    const token = this.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    try {
      const data = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          message: data.message || 'An error occurred',
          errors: data.errors || null,
        };
      }

      return {
        success: true,
        data: data.data || data,
        message: data.message,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Network error or invalid response',
        errors: error,
      };
    }
  }

  // Token Management
  setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token');
    }
    return null;
  }

  removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
    }
  }

  // User Management
  setUser(user: User): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('user_data', JSON.stringify(user));
    }
  }

  getUser(): User | null {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('user_data');
      return userData ? JSON.parse(userData) : null;
    }
    return null;
  }

  // Authentication Methods
  async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(credentials),
    });

    const result = await this.handleResponse<AuthResponse>(response);
    
    if (result.success && result.data) {
      this.setToken(result.data.access_token);
      this.setUser(result.data.user);
    }

    return result;
  }

  async register(userData: RegisterData): Promise<ApiResponse<AuthResponse>> {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(userData),
    });

    const result = await this.handleResponse<AuthResponse>(response);
    
    if (result.success && result.data) {
      this.setToken(result.data.access_token);
      this.setUser(result.data.user);
    }

    return result;
  }

  async logout(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/logout`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    const result = await this.handleResponse(response);
    this.removeToken();
    
    return result;
  }

  async refreshToken(): Promise<ApiResponse<AuthResponse>> {
    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    const result = await this.handleResponse<AuthResponse>(response);
    
    if (result.success && result.data) {
      this.setToken(result.data.access_token);
    }

    return result;
  }

  async getMe(): Promise<ApiResponse<User>> {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<User>(response);
  }

  // Conference Methods
  async getConferences(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/conferences`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse(response);
  }

  async getCurrentConference(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/conferences/current`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse(response);
  }

  // Speakers Methods
  async getSpeakers(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/speakers`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse(response);
  }

  async getCommittees(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/speakers/committees/all`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse(response);
  }

  async getKeynotes(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/speakers/keynotes/all`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse(response);
  }

  // News Methods
  async getNews(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/news`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse(response);
  }

  async getFeaturedNews(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/news/featured`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse(response);
  }

  // Registration Methods
  async submitRegistration(registrationData: any): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/registrations`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(registrationData),
    });

    return this.handleResponse(response);
  }

  async checkRegistrationStatus(data: any): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/registrations/check-status`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse(response);
  }

  // Events Methods
  async getEvents(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/events`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse(response);
  }

  async getSchedule(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/events/schedule/all`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse(response);
  }

  // Sponsors Methods
  async getSponsors(): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/sponsors`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse(response);
  }

  // Utility Methods
  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  async checkConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/conferences`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}

export const apiService = new ApiService();
export type { User, LoginCredentials, RegisterData, AuthResponse, ApiResponse };
