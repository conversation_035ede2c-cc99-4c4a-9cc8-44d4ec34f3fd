{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SuOzsvn0SKfTqZAUjO154u/ZZoIpsbZ/90BJU7XLQRw=", "__NEXT_PREVIEW_MODE_ID": "ff4adba925bcace5c283b67cb9e52e17", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c6062109e7872010cd5691e372cf8b34d126318ccdd014bf62d717c179f5f473", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a43490a402c6a55d67dda2a77b0479cd5346fb9a9de229392a13957651cf1e7b"}}}, "sortedMiddleware": ["/"], "functions": {}}