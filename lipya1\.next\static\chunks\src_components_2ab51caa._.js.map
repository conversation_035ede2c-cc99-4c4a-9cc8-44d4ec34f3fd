{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/src/components/ApiStatus.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from 'react';\nimport { apiService } from '../services/api';\n\ninterface ApiStatusProps {\n  className?: string;\n}\n\nexport default function ApiStatus({ className = '' }: ApiStatusProps) {\n  const [isConnected, setIsConnected] = useState<boolean | null>(null);\n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    const checkConnection = async () => {\n      setIsChecking(true);\n      try {\n        const connected = await apiService.checkConnection();\n        setIsConnected(connected);\n      } catch (error) {\n        setIsConnected(false);\n      } finally {\n        setIsChecking(false);\n      }\n    };\n\n    checkConnection();\n\n    // Check connection every 30 seconds\n    const interval = setInterval(checkConnection, 30000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  if (isChecking) {\n    return (\n      <div className={`flex items-center space-x-2 ${className}`}>\n        <div className=\"w-2 h-2 bg-yellow-500 rounded-full animate-pulse\"></div>\n        <span className=\"text-sm text-gray-600\">Checking API...</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`flex items-center space-x-2 ${className}`}>\n      <div \n        className={`w-2 h-2 rounded-full ${\n          isConnected ? 'bg-green-500' : 'bg-red-500'\n        }`}\n      ></div>\n      <span className=\"text-sm text-gray-600\">\n        API {isConnected ? 'Connected' : 'Disconnected'}\n      </span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASe,SAAS,UAAU,EAAE,YAAY,EAAE,EAAkB;;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;uDAAkB;oBACtB,cAAc;oBACd,IAAI;wBACF,MAAM,YAAY,MAAM,yHAAA,CAAA,aAAU,CAAC,eAAe;wBAClD,eAAe;oBACjB,EAAE,OAAO,OAAO;wBACd,eAAe;oBACjB,SAAU;wBACR,cAAc;oBAChB;gBACF;;YAEA;YAEA,oCAAoC;YACpC,MAAM,WAAW,YAAY,iBAAiB;YAE9C;uCAAO,IAAM,cAAc;;QAC7B;8BAAG,EAAE;IAEL,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;;8BACxD,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;IAG9C;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;;0BACxD,6LAAC;gBACC,WAAW,CAAC,qBAAqB,EAC/B,cAAc,iBAAiB,cAC/B;;;;;;0BAEJ,6LAAC;gBAAK,WAAU;;oBAAwB;oBACjC,cAAc,cAAc;;;;;;;;;;;;;AAIzC;GA9CwB;KAAA", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/src/components/Home.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Swiper, SwiperSlide } from 'swiper/react';\r\nimport 'swiper/css';\r\nimport ApiStatus from './ApiStatus';\r\n\r\n// باقي الكود ...\r\n\r\nexport default function HomePage() {\r\n  const [time, setTime] = useState({\r\n    seconds: 0,\r\n    minutes: 0,\r\n    hours: 0,\r\n    days: 0,\r\n    weeks: 0,\r\n    months: 0,\r\n  });\r\n\r\n  const [showInfo, setShowInfo] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const STORAGE_KEY = \"countdownStart\";\r\n\r\n    let startTime = localStorage.getItem(STORAGE_KEY);\r\n    if (!startTime) {\r\n      startTime = Date.now().toString();\r\n      localStorage.setItem(STORAGE_KEY, startTime);\r\n    }\r\n\r\n    const updateTime = () => {\r\n      const now = Date.now();\r\n      const diff = Math.floor((now - parseInt(startTime)) / 1000);\r\n\r\n      let seconds = diff % 60;\r\n      let totalMinutes = Math.floor(diff / 60);\r\n      let minutes = totalMinutes % 60;\r\n      let totalHours = Math.floor(totalMinutes / 60);\r\n      let hours = totalHours % 24;\r\n      let totalDays = Math.floor(totalHours / 24);\r\n      let days = totalDays % 7;\r\n      let totalWeeks = Math.floor(totalDays / 7);\r\n      let weeks = totalWeeks % 4;\r\n      let months = Math.floor(totalWeeks / 4);\r\n\r\n      setTime({ seconds, minutes, hours, days, weeks, months });\r\n    };\r\n\r\n    updateTime();\r\n\r\n    const timer = setInterval(updateTime, 1000);\r\n\r\n    return () => clearInterval(timer);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"relative w-full text-black overflow-hidden bg-white min-h-screen\">\r\n      {/* API Status - Development Only */}\r\n      {process.env.NODE_ENV === 'development' && (\r\n        <div className=\"fixed top-20 right-4 z-50 bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-lg\">\r\n          <ApiStatus />\r\n        </div>\r\n      )}\r\n      {/* خلفية الصورة */}\r\n      <div className=\"relative w-full h-[500px] sm:h-[550px] md:h-[600px]\">\r\n        <Image\r\n          src=\"/photo1.png\"\r\n          alt=\"Conference\"\r\n          fill\r\n          style={{ objectFit: \"cover\" }}\r\n          className=\"z-0\"\r\n          priority\r\n        />\r\n        <div className=\"absolute inset-0 bg-black/40 z-10\" />\r\n        <div className=\"relative z-20 flex flex-col items-center justify-center h-full text-center px-4 max-w-4xl mx-auto\">\r\n          <h1 className=\"text-3xl md:text-5xl font-bold leading-snug text-white\">\r\n            Pioneering Sustainable{\" \"}\r\n            <span className=\"bg-orange-500 px-2 py-1 rounded text-white inline-block\">\r\n              Energy\r\n            </span>{\" \"}\r\n            for a Greener Future\r\n          </h1>\r\n          <p className=\"mt-6 text-lg md:text-xl max-w-xl text-white\">\r\n            Join global experts, researchers, and policymakers in Tripoli to\r\n            explore innovations in renewable energy, oil & gas, and climate\r\n            solutions.\r\n          </p>\r\n          <div className=\"mt-8 flex gap-4 justify-center\">\r\n            <Link\r\n              href=\"/registration\"\r\n              className=\"bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-semibold transition\"\r\n            >\r\n              Register Now\r\n            </Link>\r\n            <Link\r\n              href=\"/submission\"\r\n              className=\"bg-transparent border border-white hover:border-orange-500 hover:text-orange-500 text-white px-6 py-2 rounded-md font-semibold transition\"\r\n            >\r\n              Submit Your Paper\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* العداد */}\r\n      <div className=\"mt-6 text-center\">\r\n        <span className=\"text-black text-xl font-bold\">Conference </span>\r\n        <span className=\"text-orange-500 text-xl font-semibold\">Countdown</span>\r\n      </div>\r\n      <div className=\"mt-6 flex justify-center gap-6 px-4 flex-wrap max-w-4xl mx-auto\">\r\n        {[\r\n          { label: \"SECOND\", value: time.seconds },\r\n          { label: \"MINUTE\", value: time.minutes },\r\n          { label: \"HOURS\", value: time.hours },\r\n          { label: \"DAYS\", value: time.days },\r\n          { label: \"WEEKS\", value: time.weeks },\r\n          { label: \"MONTH\", value: time.months },\r\n        ].map(({ label, value }) => (\r\n          <div\r\n            key={label}\r\n            className=\"bg-orange-500 text-white w-20 h-24 rounded-md flex flex-col items-center justify-center shadow-lg\"\r\n          >\r\n            <span className=\"text-3xl font-bold\">{value}</span>\r\n            <span className=\"text-sm mt-1\">{label}</span>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* جملة READ MORE و معلومات المؤتمر */}\r\n      <div className=\"mt-10 text-center px-4 max-w-3xl mx-auto\">\r\n        <p className=\"text-lg md:text-xl font-semibold\">\r\n          Get to know about{\" \"}\r\n          <span className=\"text-orange-500 uppercase font-bold\">CONFERENCE</span>\r\n        </p>\r\n        <button\r\n          onClick={() => setShowInfo(!showInfo)}\r\n          className=\" mt-4 bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-semibold transition\"\r\n        >\r\n          READ MORE\r\n        </button>\r\n\r\n        {showInfo && (\r\n          <div className=\"mt-6 text-left bg-gray-100 p-6 rounded-md shadow-md text-gray-900 text-base md:text-lg\">\r\n            The Renewable Energy, Gas & Oil, and Climate Change Conference will\r\n            take place in Tripoli, Libya, in November 2025, bringing together\r\n            experts, researchers, industry leaders, and policymakers to address\r\n            pressing energy and environmental challenges. This pioneering event\r\n            aims to bridge the gap between academia, industry, and government\r\n            by fostering dialogue and collaboration on innovative solutions in\r\n            renewable energy, oil and gas, and climate change mitigation.\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Scientific Journals Section */}\r\n      <div className=\"mt-16 text-center px-4\">\r\n  <h2 className=\"text-2xl md:text-3xl font-bold mb-8\">\r\n    <span className=\"text-orange-500\">Scientific</span> Journals\r\n  </h2>\r\n\r\n  <Swiper\r\n    spaceBetween={20}\r\n    slidesPerView={1.2}\r\n    breakpoints={{\r\n      640: { slidesPerView: 2 },\r\n      1024: { slidesPerView: 3 },\r\n    }}\r\n  >\r\n    {/* المجلة 1 */}\r\n    <SwiperSlide>\r\n      <div className=\"flex flex-col items-center\">\r\n        <Image\r\n          src=\"/photo2.png\"\r\n          alt=\"Libya Journal\"\r\n          width={120}\r\n          height={120}\r\n          className=\"rounded shadow-md\"\r\n        />\r\n        <p className=\"mt-4 text-center font-medium\">\r\n          Libya Journal for Applied Sciences and Technology\r\n        </p>\r\n      </div>\r\n    </SwiperSlide>\r\n\r\n    {/* المجلة 2 */}\r\n    <SwiperSlide>\r\n      <div className=\"flex flex-col items-center\">\r\n        <Image\r\n          src=\"/photo3.png\"\r\n          alt=\"Solar Journal\"\r\n          width={120}\r\n          height={120}\r\n          className=\"rounded shadow-md\"\r\n        />\r\n        <p className=\"mt-4 text-center font-medium\">\r\n          Solar Energy and Sustainable Development Journal\r\n        </p>\r\n      </div>\r\n    </SwiperSlide>\r\n\r\n    {/* المجلة 3 */}\r\n    <SwiperSlide>\r\n      <div className=\"flex flex-col items-center\">\r\n        <Image\r\n          src=\"/photo4.png\"\r\n          alt=\"Pure Sciences Journal\"\r\n          width={120}\r\n          height={120}\r\n          className=\"rounded shadow-md\"\r\n        />\r\n        <p className=\"mt-4 text-center font-medium\">\r\n          Pure and Applied Sciences Journal\r\n        </p>\r\n      </div>\r\n    </SwiperSlide>\r\n  </Swiper>\r\n</div>\r\n{/* Submit Your Research Section */}\r\n<div className=\"mt-20 px-4 w-full flex flex-col items-center text-center\">\r\n  <h2 className=\"text-lg md:text-2xl font-bold mb-4\">\r\n    Submit Your <span className=\"text-orange-500\">Research</span>\r\n  </h2>\r\n\r\n  <p className=\"text-gray-700 text-sm md:text-base mb-8 max-w-2xl\">\r\n    Share your innovative research and contribute to the advancement of sustainable energy solutions.\r\n  </p>\r\n\r\n  {/* اجعل الحاوية أفقيّة */}\r\n  <div className=\"flex flex-row flex-wrap justify-center gap-8 max-w-xl mx-auto text-left\">\r\n    {/* العمود الأيسر */}\r\n    <div className=\"flex flex-col gap-4 min-w-[140px]\">\r\n      <div className=\"flex items-center gap-2 text-xs sm:text-sm\">\r\n        <i className=\"fas fa-solar-panel text-orange-500\"></i>\r\n        <span className=\"font-medium\">Renewable Energy</span>\r\n      </div>\r\n      <div className=\"flex items-center gap-2 text-xs sm:text-sm\">\r\n        <i className=\"fas fa-globe-americas text-orange-500\"></i>\r\n        <span className=\"font-medium\">Climate Change</span>\r\n      </div>\r\n    </div>\r\n\r\n    {/* العمود الأيمن */}\r\n    <div className=\"flex flex-col gap-4 min-w-[140px]\">\r\n      <div className=\"flex items-center gap-2 text-xs sm:text-sm\">\r\n        <i className=\"fas fa-industry text-orange-500\"></i>\r\n        <span className=\"font-medium\">Oil &amp; Gas Transition</span>\r\n      </div>\r\n      <div className=\"flex items-center gap-2 text-xs sm:text-sm\">\r\n        <i className=\"fas fa-leaf text-orange-500\"></i>\r\n        <span className=\"font-medium\">Sustainable Development</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<div className=\"mt-10 flex justify-center\">\r\n  <button className=\"bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-md font-semibold transition flex items-center gap-2\">\r\n    <i className=\"fas fa-file-upload\"></i>\r\n     <input type=\"file\" /> Submit Your Paper\r\n  </button>\r\n</div>\r\n<h2 className=\"text-2xl md:text-3xl font-bold text-center mt-16\">\r\n  <span className=\"text-orange-500\">Conference</span> Timeline\r\n</h2>\r\n<div className=\"mt-10 flex flex-row flex-wrap items-center justify-center gap-12 px-4 max-w-5xl mx-auto\">\r\n  {/* النقطة 1 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mb-2\">\r\n      June 15, 2025\r\n    </div>\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Abstract Submission Deadline</p>\r\n  </div>\r\n\r\n  {/* النقطة 2 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Notification of Acceptance</p>\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mt-2\">\r\n      August 15, 2025\r\n    </div>\r\n  </div>\r\n\r\n  {/* النقطة 3 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mb-2\">\r\n      October 1, 2025\r\n    </div>\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Final Paper Submission</p>\r\n  </div>\r\n\r\n  {/* النقطة 4 */}\r\n  <div className=\"flex flex-col items-center min-w-[150px]\">\r\n    <div className=\"w-4 h-4 bg-orange-500 rounded-full shadow-lg mb-2\" />\r\n    <p className=\"text-center text-sm md:text-base font-medium\">Conference Dates</p>\r\n    <div className=\"bg-orange-500 text-white text-sm px-4 py-1 rounded-full shadow-md mt-2\">\r\n      November 25–27, 2025\r\n    </div>\r\n  </div>\r\n</div>\r\n<div className=\"mt-20 text-center px-4\">\r\n  <h2 className=\"text-2xl md:text-3xl font-bold\">\r\n    Our Official <span className=\"text-orange-500\">Sponsors</span>\r\n  </h2>\r\n</div>\r\n <div className=\"mt-10 px-4 max-w-6xl mx-auto\">\r\n  <Swiper\r\n    spaceBetween={20}\r\n    slidesPerView={2}\r\n    breakpoints={{\r\n      640: { slidesPerView: 2 },\r\n      768: { slidesPerView: 3 },\r\n      1024: { slidesPerView: 4 },\r\n    }}\r\n  >\r\n    {[\"photo5.png\", \"photo6.png\", \"photo7.png\", \"photo8.png\"].map((src, index) => (\r\n      <SwiperSlide key={index} className=\"flex justify-center\">\r\n        <img\r\n          src={`/${src}`}\r\n          alt={`Sponsor ${index + 1}`}\r\n          className=\"rounded shadow-md max-w-[120px] h-auto object-contain\"\r\n        />\r\n      </SwiperSlide>\r\n    ))}\r\n  </Swiper>\r\n</div>\r\n<div id=\"contact\" className=\"mt-24 px-4 max-w-4xl mx-auto\">\r\n\r\n  {/* العنوان والوصف */}\r\n  <div className=\"  text-center mb-10\">\r\n    <h2 className=\"text-3xl md:text-4xl font-bold mb-2\">\r\n      <span className=\"text-orange-500\">Contact</span> Us\r\n    </h2>\r\n    <p className=\"text-gray-600 text-base md:text-lg\">\r\n      Service description example: To buy a plot to build your house, this requires documenting\r\n      the sale and purchase process in the notarial offices to register the property in your name.\r\n    </p>\r\n  </div>\r\n\r\n  {/* النموذج */}\r\n  <form className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n    {/* الاسم */}\r\n    <div className=\"flex items-center border border-gray-300 rounded-md px-4 py-3\">\r\n      <i className=\"fas fa-user text-orange-500 mr-3\" />\r\n      <input\r\n        type=\"text\"\r\n        placeholder=\"Full Name\"\r\n        className=\"w-full outline-none text-sm\"\r\n      />\r\n    </div>\r\n\r\n    {/* الإيميل */}\r\n    <div className=\"flex items-center border border-gray-300 rounded-md px-4 py-3\">\r\n      <i className=\"fas fa-envelope text-orange-500 mr-3\" />\r\n      <input\r\n        type=\"email\"\r\n        placeholder=\"Email\"\r\n        className=\"w-full outline-none text-sm\"\r\n      />\r\n    </div>\r\n\r\n    {/* الموضوع */}\r\n    <div className=\"md:col-span-2 flex items-center border border-gray-300 rounded-md px-4 py-3\">\r\n      <i className=\"fas fa-tag text-orange-500 mr-3\" />\r\n      <input\r\n        type=\"text\"\r\n        placeholder=\"Subject\"\r\n        className=\"w-full outline-none text-sm\"\r\n      />\r\n    </div>\r\n\r\n    {/* الرسالة */}\r\n    <div className=\"md:col-span-2 flex border border-gray-300 rounded-md px-4 py-3\">\r\n      <i className=\"fas fa-comment-dots text-orange-500 mr-3 mt-1\" />\r\n      <textarea\r\n        placeholder=\"Your Message\"\r\n        className=\"w-full outline-none text-sm resize-none min-h-[120px]\"\r\n      />\r\n    </div>\r\n\r\n    {/* زر الإرسال */}\r\n    <div className=\"md:col-span-2 text-center\">\r\n      <button\r\n        type=\"submit\"\r\n        className=\"bg-orange-500 r:bg-orange-600 text-white font-semibold px-8 py-2 rounded-md transition mb-12\"\r\n      >\r\n        Send Message\r\n      </button>\r\n    </div>\r\n  </form>\r\n</div>\r\n\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;AA4DO;;AA1DP;AACA;AACA;AACA;AAEA;;;AAPA;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,cAAc;YAEpB,IAAI,YAAY,aAAa,OAAO,CAAC;YACrC,IAAI,CAAC,WAAW;gBACd,YAAY,KAAK,GAAG,GAAG,QAAQ;gBAC/B,aAAa,OAAO,CAAC,aAAa;YACpC;YAEA,MAAM;iDAAa;oBACjB,MAAM,MAAM,KAAK,GAAG;oBACpB,MAAM,OAAO,KAAK,KAAK,CAAC,CAAC,MAAM,SAAS,UAAU,IAAI;oBAEtD,IAAI,UAAU,OAAO;oBACrB,IAAI,eAAe,KAAK,KAAK,CAAC,OAAO;oBACrC,IAAI,UAAU,eAAe;oBAC7B,IAAI,aAAa,KAAK,KAAK,CAAC,eAAe;oBAC3C,IAAI,QAAQ,aAAa;oBACzB,IAAI,YAAY,KAAK,KAAK,CAAC,aAAa;oBACxC,IAAI,OAAO,YAAY;oBACvB,IAAI,aAAa,KAAK,KAAK,CAAC,YAAY;oBACxC,IAAI,QAAQ,aAAa;oBACzB,IAAI,SAAS,KAAK,KAAK,CAAC,aAAa;oBAErC,QAAQ;wBAAE;wBAAS;wBAAS;wBAAO;wBAAM;wBAAO;oBAAO;gBACzD;;YAEA;YAEA,MAAM,QAAQ,YAAY,YAAY;YAEtC;sCAAO,IAAM,cAAc;;QAC7B;6BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;YAEZ,oDAAyB,+BACxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,kIAAA,CAAA,UAAS;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,IAAI;wBACJ,OAAO;4BAAE,WAAW;wBAAQ;wBAC5B,WAAU;wBACV,QAAQ;;;;;;kCAEV,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAyD;oCAC9C;kDACvB,6LAAC;wCAAK,WAAU;kDAA0D;;;;;;oCAElE;oCAAI;;;;;;;0CAGd,6LAAC;gCAAE,WAAU;0CAA8C;;;;;;0CAK3D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAA+B;;;;;;kCAC/C,6LAAC;wBAAK,WAAU;kCAAwC;;;;;;;;;;;;0BAE1D,6LAAC;gBAAI,WAAU;0BACZ;oBACC;wBAAE,OAAO;wBAAU,OAAO,KAAK,OAAO;oBAAC;oBACvC;wBAAE,OAAO;wBAAU,OAAO,KAAK,OAAO;oBAAC;oBACvC;wBAAE,OAAO;wBAAS,OAAO,KAAK,KAAK;oBAAC;oBACpC;wBAAE,OAAO;wBAAQ,OAAO,KAAK,IAAI;oBAAC;oBAClC;wBAAE,OAAO;wBAAS,OAAO,KAAK,KAAK;oBAAC;oBACpC;wBAAE,OAAO;wBAAS,OAAO,KAAK,MAAM;oBAAC;iBACtC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,iBACrB,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;0CACtC,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;uBAJ3B;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;4BAAmC;4BAC5B;0CAClB,6LAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;kCAExD,6LAAC;wBACC,SAAS,IAAM,YAAY,CAAC;wBAC5B,WAAU;kCACX;;;;;;oBAIA,0BACC,6LAAC;wBAAI,WAAU;kCAAyF;;;;;;;;;;;;0BAa5G,6LAAC;gBAAI,WAAU;;kCACnB,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAK,WAAU;0CAAkB;;;;;;4BAAiB;;;;;;;kCAGrD,6LAAC,6IAAA,CAAA,SAAM;wBACL,cAAc;wBACd,eAAe;wBACf,aAAa;4BACX,KAAK;gCAAE,eAAe;4BAAE;4BACxB,MAAM;gCAAE,eAAe;4BAAE;wBAC3B;;0CAGA,6LAAC,6IAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAOhD,6LAAC,6IAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAOhD,6LAAC,6IAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAqC;0CACrC,6LAAC;gCAAK,WAAU;0CAAkB;;;;;;;;;;;;kCAGhD,6LAAC;wBAAE,WAAU;kCAAoD;;;;;;kCAKjE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;0CAKlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMtC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAE,WAAU;;;;;;sCACZ,6LAAC;4BAAM,MAAK;;;;;;wBAAS;;;;;;;;;;;;0BAG1B,6LAAC;gBAAG,WAAU;;kCACZ,6LAAC;wBAAK,WAAU;kCAAkB;;;;;;oBAAiB;;;;;;;0BAErD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAyE;;;;;;0CAGxF,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAI9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAC5D,6LAAC;gCAAI,WAAU;0CAAyE;;;;;;;;;;;;kCAM1F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAyE;;;;;;0CAGxF,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAI9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAC5D,6LAAC;gCAAI,WAAU;0CAAyE;;;;;;;;;;;;;;;;;;0BAK5F,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;;wBAAiC;sCAChC,6LAAC;4BAAK,WAAU;sCAAkB;;;;;;;;;;;;;;;;;0BAGlD,6LAAC;gBAAI,WAAU;0BACd,cAAA,6LAAC,6IAAA,CAAA,SAAM;oBACL,cAAc;oBACd,eAAe;oBACf,aAAa;wBACX,KAAK;4BAAE,eAAe;wBAAE;wBACxB,KAAK;4BAAE,eAAe;wBAAE;wBACxB,MAAM;4BAAE,eAAe;wBAAE;oBAC3B;8BAEC;wBAAC;wBAAc;wBAAc;wBAAc;qBAAa,CAAC,GAAG,CAAC,CAAC,KAAK,sBAClE,6LAAC,6IAAA,CAAA,cAAW;4BAAa,WAAU;sCACjC,cAAA,6LAAC;gCACC,KAAK,CAAC,CAAC,EAAE,KAAK;gCACd,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG;gCAC3B,WAAU;;;;;;2BAJI;;;;;;;;;;;;;;;0BAUxB,6LAAC;gBAAI,IAAG;gBAAU,WAAU;;kCAG1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAkB;;;;;;oCAAc;;;;;;;0CAElD,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;kCAOpD,6LAAC;wBAAK,WAAU;;0CAEd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;;;;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;;;;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;;;;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;;;;;kDACb,6LAAC;wCACC,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASP;GAlYwB;KAAA", "debugId": null}}]}