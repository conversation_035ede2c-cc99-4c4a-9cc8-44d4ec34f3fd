{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/src/services/api.ts"], "sourcesContent": ["// API Service for Laravel Backend Integration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';\n\ninterface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  errors?: any;\n}\n\ninterface LoginCredentials {\n  email: string;\n  password: string;\n}\n\ninterface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  password_confirmation: string;\n}\n\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n  email_verified_at?: string;\n  created_at: string;\n  updated_at: string;\n}\n\ninterface AuthResponse {\n  access_token: string;\n  token_type: string;\n  expires_in: number;\n  user: User;\n}\n\nclass ApiService {\n  private getAuthHeaders(): HeadersInit {\n    const token = this.getToken();\n    return {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n      ...(token && { 'Authorization': `Bearer ${token}` }),\n    };\n  }\n\n  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {\n    try {\n      const data = await response.json();\n      \n      if (!response.ok) {\n        return {\n          success: false,\n          message: data.message || 'An error occurred',\n          errors: data.errors || null,\n        };\n      }\n\n      return {\n        success: true,\n        data: data.data || data,\n        message: data.message,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: 'Network error or invalid response',\n        errors: error,\n      };\n    }\n  }\n\n  // Token Management\n  setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('auth_token', token);\n    }\n  }\n\n  getToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('auth_token');\n    }\n    return null;\n  }\n\n  removeToken(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('user_data');\n    }\n  }\n\n  // User Management\n  setUser(user: User): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('user_data', JSON.stringify(user));\n    }\n  }\n\n  getUser(): User | null {\n    if (typeof window !== 'undefined') {\n      const userData = localStorage.getItem('user_data');\n      return userData ? JSON.parse(userData) : null;\n    }\n    return null;\n  }\n\n  // Authentication Methods\n  async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {\n    const response = await fetch(`${API_BASE_URL}/auth/login`, {\n      method: 'POST',\n      headers: this.getAuthHeaders(),\n      body: JSON.stringify(credentials),\n    });\n\n    const result = await this.handleResponse<AuthResponse>(response);\n    \n    if (result.success && result.data) {\n      this.setToken(result.data.access_token);\n      this.setUser(result.data.user);\n    }\n\n    return result;\n  }\n\n  async register(userData: RegisterData): Promise<ApiResponse<AuthResponse>> {\n    const response = await fetch(`${API_BASE_URL}/auth/register`, {\n      method: 'POST',\n      headers: this.getAuthHeaders(),\n      body: JSON.stringify(userData),\n    });\n\n    const result = await this.handleResponse<AuthResponse>(response);\n    \n    if (result.success && result.data) {\n      this.setToken(result.data.access_token);\n      this.setUser(result.data.user);\n    }\n\n    return result;\n  }\n\n  async logout(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n      method: 'POST',\n      headers: this.getAuthHeaders(),\n    });\n\n    const result = await this.handleResponse(response);\n    this.removeToken();\n    \n    return result;\n  }\n\n  async refreshToken(): Promise<ApiResponse<AuthResponse>> {\n    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {\n      method: 'POST',\n      headers: this.getAuthHeaders(),\n    });\n\n    const result = await this.handleResponse<AuthResponse>(response);\n    \n    if (result.success && result.data) {\n      this.setToken(result.data.access_token);\n    }\n\n    return result;\n  }\n\n  async getMe(): Promise<ApiResponse<User>> {\n    const response = await fetch(`${API_BASE_URL}/auth/me`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse<User>(response);\n  }\n\n  // Conference Methods\n  async getConferences(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/conferences`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  async getCurrentConference(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/conferences/current`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  // Speakers Methods\n  async getSpeakers(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/speakers`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  async getCommittees(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/speakers/committees/all`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  async getKeynotes(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/speakers/keynotes/all`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  // News Methods\n  async getNews(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/news`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  async getFeaturedNews(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/news/featured`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  // Registration Methods\n  async submitRegistration(registrationData: any): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/registrations`, {\n      method: 'POST',\n      headers: this.getAuthHeaders(),\n      body: JSON.stringify(registrationData),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  async checkRegistrationStatus(data: any): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/registrations/check-status`, {\n      method: 'POST',\n      headers: this.getAuthHeaders(),\n      body: JSON.stringify(data),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  // Events Methods\n  async getEvents(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/events`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  async getSchedule(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/events/schedule/all`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  // Sponsors Methods\n  async getSponsors(): Promise<ApiResponse> {\n    const response = await fetch(`${API_BASE_URL}/sponsors`, {\n      method: 'GET',\n      headers: this.getAuthHeaders(),\n    });\n\n    return this.handleResponse(response);\n  }\n\n  // Utility Methods\n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n\n  async checkConnection(): Promise<boolean> {\n    try {\n      const response = await fetch(`${API_BASE_URL}/conferences`, {\n        method: 'GET',\n        headers: this.getAuthHeaders(),\n      });\n      return response.ok;\n    } catch {\n      return false;\n    }\n  }\n}\n\nexport const apiService = new ApiService();\nexport type { User, LoginCredentials, RegisterData, AuthResponse, ApiResponse };\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AACzB;AAArB,MAAM,eAAe,oEAAmC;AAqCxD,MAAM;IACI,iBAA8B;QACpC,MAAM,QAAQ,IAAI,CAAC,QAAQ;QAC3B,OAAO;YACL,gBAAgB;YAChB,UAAU;YACV,GAAI,SAAS;gBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;YAAC,CAAC;QACrD;IACF;IAEA,MAAc,eAAkB,QAAkB,EAA2B;QAC3E,IAAI;YACF,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,OAAO;oBACL,SAAS;oBACT,SAAS,KAAK,OAAO,IAAI;oBACzB,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,MAAM,KAAK,IAAI,IAAI;gBACnB,SAAS,KAAK,OAAO;YACvB;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,QAAQ;YACV;QACF;IACF;IAEA,mBAAmB;IACnB,SAAS,KAAa,EAAQ;QAC5B,wCAAmC;YACjC,aAAa,OAAO,CAAC,cAAc;QACrC;IACF;IAEA,WAA0B;QACxB,wCAAmC;YACjC,OAAO,aAAa,OAAO,CAAC;QAC9B;;IAEF;IAEA,cAAoB;QAClB,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,kBAAkB;IAClB,QAAQ,IAAU,EAAQ;QACxB,wCAAmC;YACjC,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACnD;IACF;IAEA,UAAuB;QACrB,wCAAmC;YACjC,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,OAAO,WAAW,KAAK,KAAK,CAAC,YAAY;QAC3C;;IAEF;IAEA,yBAAyB;IACzB,MAAM,MAAM,WAA6B,EAAsC;QAC7E,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;YACzD,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;YAC5B,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc,CAAe;QAEvD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;YACjC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,YAAY;YACtC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,IAAI;QAC/B;QAEA,OAAO;IACT;IAEA,MAAM,SAAS,QAAsB,EAAsC;QACzE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;YAC5D,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;YAC5B,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc,CAAe;QAEvD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;YACjC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,YAAY;YACtC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,IAAI;QAC/B;QAEA,OAAO;IACT;IAEA,MAAM,SAA+B;QACnC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc,CAAC;QACzC,IAAI,CAAC,WAAW;QAEhB,OAAO;IACT;IAEA,MAAM,eAAmD;QACvD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC,EAAE;YAC3D,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc,CAAe;QAEvD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;YACjC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,YAAY;QACxC;QAEA,OAAO;IACT;IAEA,MAAM,QAAoC;QACxC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAO;IACnC;IAEA,qBAAqB;IACrB,MAAM,iBAAuC;QAC3C,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,MAAM,uBAA6C;QACjD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,oBAAoB,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,mBAAmB;IACnB,MAAM,cAAoC;QACxC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,MAAM,gBAAsC;QAC1C,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,wBAAwB,CAAC,EAAE;YACtE,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,MAAM,cAAoC;QACxC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,sBAAsB,CAAC,EAAE;YACpE,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,eAAe;IACf,MAAM,UAAgC;QACpC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,KAAK,CAAC,EAAE;YACnD,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,MAAM,kBAAwC;QAC5C,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;YAC5D,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,gBAAqB,EAAwB;QACpE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;YAC5D,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;YAC5B,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,MAAM,wBAAwB,IAAS,EAAwB;QAC7D,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,2BAA2B,CAAC,EAAE;YACzE,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;YAC5B,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,iBAAiB;IACjB,MAAM,YAAkC;QACtC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,MAAM,cAAoC;QACxC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,oBAAoB,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,mBAAmB;IACnB,MAAM,cAAoC;QACxC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS,IAAI,CAAC,cAAc;QAC9B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA,kBAAkB;IAClB,kBAA2B;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ;IACxB;IAEA,MAAM,kBAAoC;QACxC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;gBAC1D,QAAQ;gBACR,SAAS,IAAI,CAAC,cAAc;YAC9B;YACA,OAAO,SAAS,EAAE;QACpB,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;AAEO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/src/context/UserContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from \"react\";\r\nimport { apiService, User } from \"../services/api\";\r\n\r\ntype UserContextType = {\r\n  user: User | null;\r\n  setUser: (user: User | null) => void;\r\n  login: (email: string, password: string) => Promise<{ success: boolean; message?: string }>;\r\n  register: (userData: any) => Promise<{ success: boolean; message?: string }>;\r\n  logout: () => Promise<void>;\r\n  isLoading: boolean;\r\n  isAuthenticated: boolean;\r\n};\r\n\r\nconst UserContext = createContext<UserContextType | undefined>(undefined);\r\n\r\nexport function UserProvider({ children }: { children: ReactNode }) {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n\r\n  // Initialize user from localStorage on mount\r\n  useEffect(() => {\r\n    const initializeUser = async () => {\r\n      try {\r\n        const storedUser = apiService.getUser();\r\n        const token = apiService.getToken();\r\n\r\n        if (storedUser && token) {\r\n          // Verify token is still valid\r\n          const response = await apiService.getMe();\r\n          if (response.success && response.data) {\r\n            setUser(response.data);\r\n            setIsAuthenticated(true);\r\n          } else {\r\n            // Token is invalid, clear storage\r\n            apiService.removeToken();\r\n            setUser(null);\r\n            setIsAuthenticated(false);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error initializing user:', error);\r\n        apiService.removeToken();\r\n        setUser(null);\r\n        setIsAuthenticated(false);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    initializeUser();\r\n  }, []);\r\n\r\n  const login = async (email: string, password: string) => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await apiService.login({ email, password });\r\n\r\n      if (response.success && response.data) {\r\n        setUser(response.data.user);\r\n        setIsAuthenticated(true);\r\n        return { success: true };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: response.message || 'Login failed'\r\n        };\r\n      }\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        message: 'Network error. Please try again.'\r\n      };\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const register = async (userData: any) => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await apiService.register(userData);\r\n\r\n      if (response.success && response.data) {\r\n        setUser(response.data.user);\r\n        setIsAuthenticated(true);\r\n        return { success: true };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: response.message || 'Registration failed'\r\n        };\r\n      }\r\n    } catch (error) {\r\n      return {\r\n        success: false,\r\n        message: 'Network error. Please try again.'\r\n      };\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      await apiService.logout();\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setUser(null);\r\n      setIsAuthenticated(false);\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const contextValue = {\r\n    user,\r\n    setUser: (newUser: User | null) => {\r\n      setUser(newUser);\r\n      setIsAuthenticated(!!newUser);\r\n      if (newUser) {\r\n        apiService.setUser(newUser);\r\n      }\r\n    },\r\n    login,\r\n    register,\r\n    logout,\r\n    isLoading,\r\n    isAuthenticated,\r\n  };\r\n\r\n  return (\r\n    <UserContext.Provider value={contextValue}>\r\n      {children}\r\n    </UserContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useUser() {\r\n  const context = useContext(UserContext);\r\n  if (!context) {\r\n    throw new Error(\"useUser must be used within a UserProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;yDAAiB;oBACrB,IAAI;wBACF,MAAM,aAAa,yHAAA,CAAA,aAAU,CAAC,OAAO;wBACrC,MAAM,QAAQ,yHAAA,CAAA,aAAU,CAAC,QAAQ;wBAEjC,IAAI,cAAc,OAAO;4BACvB,8BAA8B;4BAC9B,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK;4BACvC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gCACrC,QAAQ,SAAS,IAAI;gCACrB,mBAAmB;4BACrB,OAAO;gCACL,kCAAkC;gCAClC,yHAAA,CAAA,aAAU,CAAC,WAAW;gCACtB,QAAQ;gCACR,mBAAmB;4BACrB;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,yHAAA,CAAA,aAAU,CAAC,WAAW;wBACtB,QAAQ;wBACR,mBAAmB;oBACrB,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YAE1D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,mBAAmB;gBACnB,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS,SAAS,OAAO,IAAI;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,yHAAA,CAAA,aAAU,CAAC,QAAQ,CAAC;YAE3C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,QAAQ,SAAS,IAAI,CAAC,IAAI;gBAC1B,mBAAmB;gBACnB,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS,SAAS,OAAO,IAAI;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,aAAa;YACb,MAAM,yHAAA,CAAA,aAAU,CAAC,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;YACR,mBAAmB;YACnB,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB;QACA,SAAS,CAAC;YACR,QAAQ;YACR,mBAAmB,CAAC,CAAC;YACrB,IAAI,SAAS;gBACX,yHAAA,CAAA,aAAU,CAAC,OAAO,CAAC;YACrB;QACF;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GA1HgB;KAAA;AA4HT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/lipya%20project/lipya1/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport { useUser } from \"../context/UserContext\"; // عدل حسب مكان الملف\r\nimport { useRouter } from \"next/navigation\";\r\nimport Page from \"@/app/committees/page\";\r\n function Navbar() {\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const toggleMenu = () => setMenuOpen(!menuOpen);\r\n\r\n  const { user, logout, isAuthenticated } = useUser();\r\n  const router = useRouter();\r\n\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // إغلاق الـ dropdown عند الضغط خارجها\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setDropdownOpen(false);\r\n      }\r\n    }\r\n    if (dropdownOpen) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    } else {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [dropdownOpen]);\r\n\r\n  const handleLogout = async () => {\r\n    await logout();\r\n    setDropdownOpen(false);\r\n    router.push(\"/login\");\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <nav className=\"absolute top-[35px] left-0 w-full bg-gray-800/30 text-white z-40 backdrop-blur-md border-b border-white/10\">\r\n  <div className=\"max-w-7xl mx-auto px-4 py-3 flex items-center justify-between h-16\">\r\n    {/* الشعار */}\r\n    <div className=\"flex items-center space-x-2 min-w-[60px]\">\r\n      <Image src=\"/logo.png\" alt=\"Logo\" width={50} height={40} />\r\n    </div>\r\n\r\n    {/* روابط سطح المكتب */}\r\n    <ul className=\"hidden md:flex space-x-4 text-sm uppercase font-medium flex-grow justify-center\">\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/\">Home</Link></li>\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/about\">About us</Link></li>\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/committees\">Committees</Link></li>\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/call-for-papers\">Call For Papers</Link></li>\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/submission\">Submission</Link></li>\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/registration\">Registration</Link></li> {/* Corrected href */}\r\n      <li><Link className=\"hover:text-orange-400 transition\" href=\"/venue\">Venue</Link></li>\r\n      <li>\r\n        <Link\r\n          href=\"/contact-us\"\r\n          scroll={true}\r\n          onClick={toggleMenu}\r\n          className=\"block bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded-md text-sm font-semibold\"\r\n        >\r\n          Contact Us\r\n        </Link>\r\n      </li>\r\n    </ul>\r\n\r\n    {/* صورة المستخدم أو زر تسجيل الدخول + زر القائمة للموبايل */}\r\n    <div className=\"flex items-center space-x-3 min-w-[100px] justify-end relative\">\r\n      {isAuthenticated && user ? (\r\n        <>\r\n          <button onClick={() => setDropdownOpen(!dropdownOpen)} className=\"focus:outline-none\">\r\n            <div className=\"w-9 h-9 bg-orange-500 rounded-full flex items-center justify-center text-white font-semibold text-sm border border-white\">\r\n              {user.name ? user.name.charAt(0).toUpperCase() : 'U'}\r\n            </div>\r\n          </button>\r\n\r\n          {dropdownOpen && (\r\n            <div\r\n              ref={dropdownRef}\r\n              className=\"absolute right-0 mt-2 w-48 bg-gray-900 border border-gray-700 rounded shadow-lg z-50\"\r\n            >\r\n              <div className=\"px-4 py-2 border-b border-gray-700\">\r\n                <p className=\"text-sm font-medium text-white\">{user.name}</p>\r\n                <p className=\"text-xs text-gray-400\">{user.email}</p>\r\n              </div>\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"block w-full text-left px-4 py-2 text-sm hover:bg-orange-600 hover:text-white text-red-500\"\r\n              >\r\n                Logout\r\n              </button>\r\n            </div>\r\n          )}\r\n        </>\r\n      ) : (\r\n        <Link\r\n          href=\"/login\"\r\n          className=\"text-sm bg-orange-500 hover:bg-orange-600 px-3 py-1 rounded text-white\"\r\n        >\r\n          Login\r\n        </Link>\r\n      )}\r\n\r\n      {/* زر القائمة للموبايل */}\r\n      <button\r\n        className=\"md:hidden text-white\"\r\n        onClick={toggleMenu}\r\n        aria-label=\"Toggle menu\"\r\n      >\r\n        {menuOpen ? <X size={28} /> : <Menu size={28} />}\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  {/* القائمة الجانبية للموبايل */}\r\n  {menuOpen && (\r\n    <div className=\"md:hidden flex flex-col px-6 pb-4 pt-2 space-y-4 bg-gray-800/30 text-white backdrop-blur-md border-t border-white/10\">\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/\" onClick={toggleMenu}>Home</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/about\" onClick={toggleMenu}>About us</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/committees\" onClick={toggleMenu}>Committees</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/call-for-papers\" onClick={toggleMenu}>Call For Papers</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/submission\" onClick={toggleMenu}>Submission</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/registration\" onClick={toggleMenu}>Registration</Link>\r\n      <Link className=\"hover:text-orange-400 transition\" href=\"/venue\" onClick={toggleMenu}>Venue</Link>\r\n      <Link\r\n        href=\"/contact-us\"\r\n        scroll={true}\r\n        onClick={toggleMenu}\r\n        className=\"block bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-semibold w-fit\"\r\n      >\r\n        Contact Us\r\n      </Link>\r\n    </div>\r\n  )}\r\n</nav>\r\n    \r\n    </>\r\n  );\r\n}\r\nexport default Navbar ; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA,oOAAkD,qBAAqB;AACvE;;;AAPA;;;;;;;AASC,SAAS;;IACR,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,aAAa,IAAM,YAAY,CAAC;IAEtC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,gBAAgB;gBAClB;YACF;YACA,IAAI,cAAc;gBAChB,SAAS,gBAAgB,CAAC,aAAa;YACzC,OAAO;gBACL,SAAS,mBAAmB,CAAC,aAAa;YAC5C;YACA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG;QAAC;KAAa;IAEjB,MAAM,eAAe;QACnB,MAAM;QACN,gBAAgB;QAChB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAI;gCAAY,KAAI;gCAAO,OAAO;gCAAI,QAAQ;;;;;;;;;;;sCAIvD,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAI;;;;;;;;;;;8CAChE,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAS;;;;;;;;;;;8CACrE,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAc;;;;;;;;;;;8CAC1E,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAmB;;;;;;;;;;;8CAC/E,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAc;;;;;;;;;;;8CAC1E,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAgB;;;;;;;;;;;gCAAwB;8CACpG,6LAAC;8CAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,WAAU;wCAAmC,MAAK;kDAAS;;;;;;;;;;;8CACrE,6LAAC;8CACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAQ;wCACR,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;;gCACZ,mBAAmB,qBAClB;;sDACE,6LAAC;4CAAO,SAAS,IAAM,gBAAgB,CAAC;4CAAe,WAAU;sDAC/D,cAAA,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK;;;;;;;;;;;wCAIpD,8BACC,6LAAC;4CACC,KAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAkC,KAAK,IAAI;;;;;;sEACxD,6LAAC;4DAAE,WAAU;sEAAyB,KAAK,KAAK;;;;;;;;;;;;8DAElD,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;iEAOP,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAMH,6LAAC;oCACC,WAAU;oCACV,SAAS;oCACT,cAAW;8CAEV,yBAAW,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;6DAAS,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;gBAM/C,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAI,SAAS;sCAAY;;;;;;sCACjF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAmB,SAAS;sCAAY;;;;;;sCAChG,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAc,SAAS;sCAAY;;;;;;sCAC3F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAgB,SAAS;sCAAY;;;;;;sCAC7F,6LAAC,+JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,MAAK;4BAAS,SAAS;sCAAY;;;;;;sCACtF,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,QAAQ;4BACR,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;AASP;GAvIU;;QAKkC,iIAAA,CAAA,UAAO;QAClC,qIAAA,CAAA,YAAS;;;KANhB;uCAwIK", "debugId": null}}]}