# PowerShell script for production deployment

param(
    [Parameter(Mandatory=$false)]
    [string]$Environment = "production"
)

Write-Host "🚀 Deploying IREGO Conference to $Environment..." -ForegroundColor Green

# Validate environment
if ($Environment -ne "production" -and $Environment -ne "staging") {
    Write-Host "❌ Invalid environment. Use 'production' or 'staging'" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🔧 Preparing Laravel Backend for $Environment..." -ForegroundColor Cyan

# Navigate to Laravel directory
Set-Location "iregoo"

# Install production dependencies
Write-Host "📦 Installing production dependencies..." -ForegroundColor Yellow
composer install --no-dev --optimize-autoloader --no-interaction

# Clear all caches
Write-Host "🧹 Clearing caches..." -ForegroundColor Yellow
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Generate optimized autoloader
Write-Host "⚡ Optimizing autoloader..." -ForegroundColor Yellow
composer dump-autoload --optimize

# Cache configurations for production
Write-Host "💾 Caching configurations..." -ForegroundColor Yellow
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Run database migrations (with confirmation)
Write-Host "📊 Database migrations..." -ForegroundColor Yellow
$runMigrations = Read-Host "Run database migrations? (y/N)"
if ($runMigrations -eq "y" -or $runMigrations -eq "Y") {
    php artisan migrate --force
}

# Create storage link
Write-Host "🔗 Creating storage link..." -ForegroundColor Yellow
php artisan storage:link

# Set proper permissions (Linux/Unix only)
if ($IsLinux -or $IsMacOS) {
    Write-Host "🔒 Setting permissions..." -ForegroundColor Yellow
    chmod -R 755 storage
    chmod -R 755 bootstrap/cache
}

# Navigate back to root
Set-Location ".."

Write-Host ""
Write-Host "⚡ Preparing Next.js Frontend for $Environment..." -ForegroundColor Cyan

# Navigate to Next.js directory
Set-Location "lipya1"

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm ci --only=production

# Build for production
Write-Host "🏗️ Building for production..." -ForegroundColor Yellow
npm run build

# Navigate back to root
Set-Location ".."

Write-Host ""
Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Next steps for $Environment deployment:" -ForegroundColor Yellow
Write-Host "   1. Upload files to your server" -ForegroundColor White
Write-Host "   2. Configure web server (Apache/Nginx)" -ForegroundColor White
Write-Host "   3. Set up SSL certificates" -ForegroundColor White
Write-Host "   4. Configure environment variables" -ForegroundColor White
Write-Host "   5. Set up database and Redis" -ForegroundColor White
Write-Host "   6. Configure cron jobs for Laravel scheduler" -ForegroundColor White
Write-Host ""
Write-Host "📝 Important files to configure:" -ForegroundColor Cyan
Write-Host "   🔧 Laravel: iregoo/.env (copy from .env.production)" -ForegroundColor White
Write-Host "   🔧 Next.js: lipya1/.env.local (copy from .env.production)" -ForegroundColor White
Write-Host ""
Write-Host "🔒 Security checklist:" -ForegroundColor Red
Write-Host "   ✓ Update all passwords and secrets" -ForegroundColor White
Write-Host "   ✓ Configure firewall rules" -ForegroundColor White
Write-Host "   ✓ Set up SSL/TLS certificates" -ForegroundColor White
Write-Host "   ✓ Configure backup strategy" -ForegroundColor White
Write-Host "   ✓ Set up monitoring and logging" -ForegroundColor White
Write-Host ""

Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
