<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Event extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'conference_id',
        'title',
        'description',
        'type', // session, keynote, workshop, break, lunch, registration
        'start_time',
        'end_time',
        'location',
        'room',
        'max_participants',
        'current_participants',
        'is_parallel',
        'display_order',
        'is_active',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_parallel' => 'boolean',
        'is_active' => 'boolean',
        'max_participants' => 'integer',
        'current_participants' => 'integer',
        'display_order' => 'integer',
    ];

    // Relationships
    public function conference()
    {
        return $this->belongsTo(Conference::class);
    }

    public function speakers()
    {
        return $this->belongsToMany(Speaker::class, 'event_speakers');
    }

    // Accessors
    public function getDurationAttribute()
    {
        if (!$this->start_time || !$this->end_time) {
            return null;
        }

        $duration = $this->end_time->diffInMinutes($this->start_time);
        
        if ($duration < 60) {
            return $duration . ' minutes';
        }
        
        $hours = floor($duration / 60);
        $minutes = $duration % 60;
        
        if ($minutes === 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }
        
        return $hours . 'h ' . $minutes . 'm';
    }

    public function getTimeRangeAttribute()
    {
        if (!$this->start_time || !$this->end_time) {
            return null;
        }

        return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
    }

    public function getAvailableSpotsAttribute()
    {
        if (!$this->max_participants) {
            return null;
        }

        return $this->max_participants - $this->current_participants;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByDate($query, $date)
    {
        return $query->whereDate('start_time', $date);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('start_time')->orderBy('display_order');
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_time', '>', now());
    }

    // Methods
    public function isKeynote()
    {
        return $this->type === 'keynote';
    }

    public function isSession()
    {
        return $this->type === 'session';
    }

    public function isWorkshop()
    {
        return $this->type === 'workshop';
    }

    public function hasAvailableSpots()
    {
        return !$this->max_participants || $this->current_participants < $this->max_participants;
    }

    public function isFull()
    {
        return $this->max_participants && $this->current_participants >= $this->max_participants;
    }
}
