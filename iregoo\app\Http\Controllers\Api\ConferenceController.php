<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Conference;
use Illuminate\Http\Request;

class ConferenceController extends Controller
{
    public function index()
    {
        $conferences = Conference::active()
            ->with(['speakers' => function($query) {
                $query->active()->featured()->ordered();
            }])
            ->orderBy('start_date', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $conferences->map(function($conference) {
                return [
                    'id' => $conference->id,
                    'title' => $conference->title,
                    'subtitle' => $conference->subtitle,
                    'description' => $conference->description,
                    'start_date' => $conference->start_date,
                    'end_date' => $conference->end_date,
                    'location' => $conference->location,
                    'venue' => $conference->venue,
                    'logo_url' => $conference->logo_url,
                    'banner_image_url' => $conference->banner_image_url,
                    'website_url' => $conference->website_url,
                    'email' => $conference->email,
                    'phone' => $conference->phone,
                    'registration_fee_local' => $conference->registration_fee_local,
                    'registration_fee_international' => $conference->registration_fee_international,
                    'currency_local' => $conference->currency_local,
                    'currency_international' => $conference->currency_international,
                    'abstract_deadline' => $conference->abstract_deadline,
                    'notification_date' => $conference->notification_date,
                    'final_paper_deadline' => $conference->final_paper_deadline,
                    'featured_speakers' => $conference->speakers->map(function($speaker) {
                        return [
                            'id' => $speaker->id,
                            'name' => $speaker->full_name,
                            'position' => $speaker->position,
                            'organization' => $speaker->organization,
                            'photo_url' => $speaker->photo_url,
                            'type' => $speaker->type,
                        ];
                    }),
                ];
            })
        ]);
    }

    public function show($id)
    {
        $conference = Conference::active()
            ->with([
                'speakers' => function($query) {
                    $query->active()->ordered();
                },
                'sponsors' => function($query) {
                    $query->active()->ordered();
                },
                'events' => function($query) {
                    $query->active()->ordered();
                },
                'news' => function($query) {
                    $query->published()->recent(5);
                }
            ])
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $conference->id,
                'title' => $conference->title,
                'subtitle' => $conference->subtitle,
                'description' => $conference->description,
                'start_date' => $conference->start_date,
                'end_date' => $conference->end_date,
                'location' => $conference->location,
                'venue' => $conference->venue,
                'logo_url' => $conference->logo_url,
                'banner_image_url' => $conference->banner_image_url,
                'website_url' => $conference->website_url,
                'email' => $conference->email,
                'phone' => $conference->phone,
                'registration_fee_local' => $conference->registration_fee_local,
                'registration_fee_international' => $conference->registration_fee_international,
                'currency_local' => $conference->currency_local,
                'currency_international' => $conference->currency_international,
                'abstract_deadline' => $conference->abstract_deadline,
                'notification_date' => $conference->notification_date,
                'final_paper_deadline' => $conference->final_paper_deadline,
                'speakers' => $conference->speakers->map(function($speaker) {
                    return [
                        'id' => $speaker->id,
                        'name' => $speaker->full_name,
                        'position' => $speaker->position,
                        'organization' => $speaker->organization,
                        'bio' => $speaker->bio,
                        'photo_url' => $speaker->photo_url,
                        'type' => $speaker->type,
                        'committee_type' => $speaker->committee_type,
                        'country' => $speaker->country,
                        'city' => $speaker->city,
                    ];
                }),
                'sponsors' => $conference->sponsors->map(function($sponsor) {
                    return [
                        'id' => $sponsor->id,
                        'name' => $sponsor->name,
                        'description' => $sponsor->description,
                        'logo_url' => $sponsor->logo_url,
                        'website_url' => $sponsor->website_url,
                        'type' => $sponsor->type,
                        'type_display' => $sponsor->type_display,
                    ];
                }),
                'events' => $conference->events->map(function($event) {
                    return [
                        'id' => $event->id,
                        'title' => $event->title,
                        'description' => $event->description,
                        'type' => $event->type,
                        'start_time' => $event->start_time,
                        'end_time' => $event->end_time,
                        'location' => $event->location,
                        'room' => $event->room,
                        'duration' => $event->duration,
                        'time_range' => $event->time_range,
                    ];
                }),
                'recent_news' => $conference->news->map(function($news) {
                    return [
                        'id' => $news->id,
                        'title' => $news->title,
                        'slug' => $news->slug,
                        'excerpt' => $news->excerpt,
                        'featured_image_url' => $news->featured_image_url,
                        'published_at' => $news->published_at,
                        'reading_time' => $news->reading_time,
                    ];
                }),
            ]
        ]);
    }

    public function current()
    {
        $conference = Conference::active()
            ->current()
            ->with([
                'speakers' => function($query) {
                    $query->active()->featured()->ordered();
                }
            ])
            ->first();

        if (!$conference) {
            return response()->json([
                'success' => false,
                'message' => 'No current conference found'
            ], 404);
        }

        return $this->show($conference->id);
    }
}
