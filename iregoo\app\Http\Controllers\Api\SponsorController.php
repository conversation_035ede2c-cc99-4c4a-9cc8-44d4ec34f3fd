<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Sponsor;
use Illuminate\Http\Request;

class SponsorController extends Controller
{
    public function index(Request $request)
    {
        $query = Sponsor::active()->with('conference');

        // Filter by conference
        if ($request->has('conference_id')) {
            $query->where('conference_id', $request->conference_id);
        }

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter featured sponsors
        if ($request->has('featured') && $request->featured) {
            $query->where('is_featured', true);
        }

        $sponsors = $query->ordered()->get();

        // Group by type if requested
        if ($request->has('group_by_type') && $request->group_by_type) {
            $groupedSponsors = $sponsors->groupBy('type')->map(function($typeSponsors, $type) {
                return [
                    'type' => $type,
                    'type_display' => $typeSponsors->first()->type_display,
                    'sponsors' => $typeSponsors->map(function($sponsor) {
                        return [
                            'id' => $sponsor->id,
                            'name' => $sponsor->name,
                            'description' => $sponsor->description,
                            'logo_url' => $sponsor->logo_url,
                            'website_url' => $sponsor->website_url,
                            'type' => $sponsor->type,
                            'is_featured' => $sponsor->is_featured,
                        ];
                    })->values(),
                ];
            })->values();

            return response()->json([
                'success' => true,
                'data' => $groupedSponsors
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $sponsors->map(function($sponsor) {
                return [
                    'id' => $sponsor->id,
                    'name' => $sponsor->name,
                    'description' => $sponsor->description,
                    'logo_url' => $sponsor->logo_url,
                    'website_url' => $sponsor->website_url,
                    'contact_email' => $sponsor->contact_email,
                    'contact_phone' => $sponsor->contact_phone,
                    'type' => $sponsor->type,
                    'type_display' => $sponsor->type_display,
                    'is_featured' => $sponsor->is_featured,
                    'conference' => [
                        'id' => $sponsor->conference->id,
                        'title' => $sponsor->conference->title,
                    ],
                ];
            })
        ]);
    }

    public function show($id)
    {
        $sponsor = Sponsor::active()
            ->with('conference')
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $sponsor->id,
                'name' => $sponsor->name,
                'description' => $sponsor->description,
                'logo_url' => $sponsor->logo_url,
                'website_url' => $sponsor->website_url,
                'contact_email' => $sponsor->contact_email,
                'contact_phone' => $sponsor->contact_phone,
                'type' => $sponsor->type,
                'type_display' => $sponsor->type_display,
                'is_featured' => $sponsor->is_featured,
                'conference' => [
                    'id' => $sponsor->conference->id,
                    'title' => $sponsor->conference->title,
                    'start_date' => $sponsor->conference->start_date,
                    'end_date' => $sponsor->conference->end_date,
                ],
            ]
        ]);
    }
}
